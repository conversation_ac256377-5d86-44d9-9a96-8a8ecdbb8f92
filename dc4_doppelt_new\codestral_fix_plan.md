# Plan to Fix Codestral Integration in Telegram Bot

## 1. Identified Issues

1. **Wrong Endpoint**: Currently using `https://api.cerebras.ai/v1/chat/completions` but should be using `https://codestral.mistral.ai/v1/chat/completions`

2. **Wrong API Key**: Currently using Cerebras API key but should be using the Codestral-specific key

3. **Wrong Model Name**: Currently using `llama-3.1-8b` but should be using `codestral-latest`

## 2. Required Changes

### 2.1. Update the CodestralProvider Class

We need to modify the `CodestralProvider` class to:
- Use the correct endpoint URL
- Use the correct API key
- Use the correct model name

### 2.2. Update Configuration

- Update the `CODESTRAL_API_KEY` in config.txt
- Update the `CODESTRAL_MODEL` in config.txt
- Ensure Codestral is first in the provider order

## 3. Implementation Steps

1. **Modify the CodestralProvider class**:
   - Change the endpoint URL to `https://codestral.mistral.ai/v1/chat/completions`
   - Update any other Codestral-specific code

2. **Update config.txt**:
   - Set `CODESTRAL_API_KEY=n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350`
   - Set `CODESTRAL_MODEL=codestral-latest`
   - Confirm `PROVIDER_ORDER=codestral,cohere,mistral,cerebras,lamini`

3. **Test the changes**:
   - Create a simple test script to verify Codestral works with the new configuration
   - Run the bot and check if it successfully uses Codestral

## 4. Potential Issues and Solutions

1. **Rate Limiting**: If Codestral has rate limits, we may need to implement backoff strategies

2. **API Changes**: If the Codestral API changes in the future, we'll need to update our code accordingly

3. **Error Handling**: Ensure proper error handling for Codestral-specific errors

## 5. Long-term Improvements

1. **Better Provider Abstraction**: Create a more flexible provider system that can handle different API endpoints and formats

2. **Configuration Validation**: Add validation for API keys and endpoints to catch configuration errors early

3. **Automatic Fallback**: Implement smarter fallback logic that can detect when a provider is consistently failing

## 6. Testing Strategy

1. **Unit Testing**: Test the CodestralProvider class in isolation

2. **Integration Testing**: Test the entire bot with the Codestral provider

3. **Error Testing**: Test how the bot handles various error conditions from the Codestral API
