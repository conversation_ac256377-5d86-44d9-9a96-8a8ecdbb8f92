import os
from openai import OpenAI
import time

# Replace with your actual OpenAI API key
api_key = "********************************************************************************************************************************************************************"

client = OpenAI(api_key=api_key)

models_to_test = [
    "gpt-4.1-nano",
    "gpt-4.1-nano-2025-04-14",
    "gpt-4.1-mini",
    "gpt-4.1-mini-2025-04-14",
    "gpt-4o-mini"
]

delay_seconds = 30  # You can adjust this value

for model_name in models_to_test:
    try:
        print(f"Testing model: {model_name}")
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": "This is a test."}],
            max_tokens=10  # Keep the token usage low for testing
        )
        print(f"  Response from {model_name}: {response.choices[0].message.content}")
    except Exception as e:
        print(f"  Error with {model_name}: {e}")

    time.sleep(delay_seconds)  # Pause for the specified number of seconds

print("Testing complete. Please check your OpenAI account usage to confirm if any charges were incurred.")