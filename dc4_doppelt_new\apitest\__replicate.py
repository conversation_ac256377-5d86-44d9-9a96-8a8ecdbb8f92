import asyncio
import traceback
import aiohttp
import json

# Configuration
url = "https://api.replicate.com/v1/predictions"  # Replicate API endpoint
API_KEY = "****************************************"     # Provided Replicate API key
TEST_MESSAGE = "Hello, how are you?"
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Token {API_KEY}"
}
DELAY_SECONDS = 30  # Adjust as needed

# Replicate models to test
REPLICATE_MODELS = [
    "aihilums/sehatsanjha",
    "aiscaleandgrow/jivatts-character1-lora",
    "aiscaleandgrow/ketrin-character1",
    "astramlco/easycontrol-ghibli",
    "denzelhooke/juliav2",
    "fishwowater/flux-dev-controlnet-inpainting-beta",
    "ghostface-ai/gif-upscaler",
    "gigidiva/gigi",
    "gigidiva/gigi_v2",
    "halstonblim/distilbert-base-uncased-finetuned-sst-2-english",
    "koolamusic/flux-dev-lora-mahi",
    "mertguvencli/face-swap-with-indexes",
    "natalia176/psicanalista",
    "pcgarat/paco_face",
    "phospho-app/gr00t-policy",
    "popcornpepzi/mtwr64",
    "prunaai/hidream-l1-fast",
    "raphaelmassagardi/modelo_raphael_v3",
    "terminallyai/report",
    "tesyolan/flaskbottle",
    "thefluxtrain/oye-cartoon",
    "tuannha/f5-tts-vi",
    "tuannha/instant-character",
    "w95/tinyclick",
    "zsxkib/kimi-vl-a3b-thinking"
]

async def test_model(model_name):
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json={
                "version": model_name,
                "input": {
                    "prompt": TEST_MESSAGE
                }
            }, headers=headers, ssl=False) as response:
                response.raise_for_status()
                data = await response.json()
                if 'output' in data:
                    print(f"  Response from {model_name}: {data['output']}")
                    return True
                else:
                    print(f"  No response from {model_name}: {data}")
                    return False
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

async def main():
    print("Testing Replicate models...")
    for model_name in REPLICATE_MODELS:
        print(f"\nTesting model: {model_name}")
        success = await test_model(model_name)
        if success:
            print(f"{model_name} is accessible")
        else:
            print(f"{model_name} is not accessible")
        await asyncio.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    asyncio.run(main())
