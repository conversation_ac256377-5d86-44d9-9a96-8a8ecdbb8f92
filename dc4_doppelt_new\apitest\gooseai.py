import requests

API_KEY = "sk-igjrOSJlDj1DeI4uTvqL8QwrgTLKiXKrWBjfKHX0jkz2rvOK"  # Replace with your actual API key
BASE_URL = "https://api.goose.ai/v1/engines/{model}/completions"

models = [
    "fairseq-125m",
    "gpt-neo-125m",
    "gpt-neo-1-3b",
    "fairseq-1-3b",
    "gpt-neo-2-7b",
    "fairseq-2-7b",
    "gpt-j-6b",
    "fairseq-6b-7b",
    "fairseq-13b",
    "gpt-neo-20b"
]

def test_model(model_name):
    url = BASE_URL.format(model=model_name)
    headers = {
        "Authorization": f"Bearer {API_KEY}"
    }
    data = {
        "prompt": "This is a test prompt.",
        "max_tokens": 10
    }
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        print(f"Model '{model_name}' is available. Status code: {response.status_code}")
        print(f"Response content: {response.json()}")  # Print the response content
    except requests.exceptions.RequestException as e:
        print(f"Model '{model_name}' is not available. Error: {e}")

if __name__ == "__main__":
    for model in models:
        test_model(model)
