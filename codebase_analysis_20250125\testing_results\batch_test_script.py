#!/usr/bin/env python3
"""
COMPREHENSIVE BATCH TESTING SCRIPT
Tests all Python scripts in the codebase systematically
"""

import os
import subprocess
import sys
import time
from pathlib import Path

def test_script_syntax(script_path):
    """Test if a Python script has valid syntax"""
    try:
        result = subprocess.run([
            sys.executable, '-m', 'py_compile', script_path
        ], capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stderr
    except subprocess.TimeoutExpired:
        return False, "Timeout during syntax check"
    except Exception as e:
        return False, str(e)

def test_script_execution(script_path, timeout=30):
    """Test if a Python script can execute without immediate errors"""
    try:
        # For scripts that might require arguments, just test import
        result = subprocess.run([
            sys.executable, '-c', f'import sys; sys.path.append("{os.path.dirname(script_path)}"); exec(open("{script_path}").read())'
        ], capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Timeout during execution"
    except Exception as e:
        return False, "", str(e)

def find_all_python_scripts(root_dir):
    """Find all Python scripts in the codebase"""
    python_scripts = []
    for root, dirs, files in os.walk(root_dir):
        # Skip certain directories
        skip_dirs = {'.git', '__pycache__', '.pytest_cache', 'node_modules', '.venv', 'venv'}
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if file.endswith('.py'):
                python_scripts.append(os.path.join(root, file))
    
    return sorted(python_scripts)

def main():
    """Main testing function"""
    print("🎯 COMPREHENSIVE CODEBASE TESTING STARTED")
    print("=" * 60)
    
    # Find all Python scripts
    root_dir = "/home/<USER>/colestart"
    scripts = find_all_python_scripts(root_dir)
    
    print(f"📊 Found {len(scripts)} Python scripts to test")
    print("=" * 60)
    
    results = {
        'syntax_valid': [],
        'syntax_invalid': [],
        'execution_success': [],
        'execution_failed': [],
        'total_tested': 0
    }
    
    # Test each script
    for i, script in enumerate(scripts, 1):
        relative_path = os.path.relpath(script, root_dir)
        print(f"\n[{i}/{len(scripts)}] Testing: {relative_path}")
        
        # Test syntax
        syntax_ok, syntax_error = test_script_syntax(script)
        if syntax_ok:
            print(f"  ✅ Syntax: VALID")
            results['syntax_valid'].append(relative_path)
            
            # Test execution for simple scripts only
            if any(keyword in relative_path.lower() for keyword in ['test', 'hello', 'simple']):
                exec_ok, stdout, stderr = test_script_execution(script, timeout=10)
                if exec_ok:
                    print(f"  ✅ Execution: SUCCESS")
                    results['execution_success'].append(relative_path)
                else:
                    print(f"  ❌ Execution: FAILED - {stderr[:100]}")
                    results['execution_failed'].append((relative_path, stderr))
        else:
            print(f"  ❌ Syntax: INVALID - {syntax_error[:100]}")
            results['syntax_invalid'].append((relative_path, syntax_error))
        
        results['total_tested'] += 1
        
        # Progress update every 10 scripts
        if i % 10 == 0:
            print(f"\n📊 Progress: {i}/{len(scripts)} scripts tested")
            print(f"   ✅ Syntax Valid: {len(results['syntax_valid'])}")
            print(f"   ❌ Syntax Invalid: {len(results['syntax_invalid'])}")
    
    # Final results
    print("\n" + "=" * 60)
    print("🏆 FINAL TESTING RESULTS")
    print("=" * 60)
    print(f"📊 Total Scripts Tested: {results['total_tested']}")
    print(f"✅ Syntax Valid: {len(results['syntax_valid'])} ({len(results['syntax_valid'])/results['total_tested']*100:.1f}%)")
    print(f"❌ Syntax Invalid: {len(results['syntax_invalid'])} ({len(results['syntax_invalid'])/results['total_tested']*100:.1f}%)")
    print(f"✅ Execution Success: {len(results['execution_success'])}")
    print(f"❌ Execution Failed: {len(results['execution_failed'])}")
    
    # Save detailed results
    with open('/home/<USER>/colestart/codebase_analysis_20250125/testing_results/batch_test_results.txt', 'w') as f:
        f.write("COMPREHENSIVE BATCH TEST RESULTS\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Total Scripts: {results['total_tested']}\n")
        f.write(f"Syntax Valid: {len(results['syntax_valid'])}\n")
        f.write(f"Syntax Invalid: {len(results['syntax_invalid'])}\n\n")
        
        f.write("SYNTAX VALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script in results['syntax_valid']:
            f.write(f"✅ {script}\n")
        
        f.write("\nSYNTAX INVALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script, error in results['syntax_invalid']:
            f.write(f"❌ {script}\n   Error: {error}\n\n")
    
    print(f"\n📄 Detailed results saved to: batch_test_results.txt")

if __name__ == "__main__":
    main()
