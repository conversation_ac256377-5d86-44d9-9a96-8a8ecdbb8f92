"""
SIMPLIFIED TELEGRAM BOT
======================

This script implements a straightforward Telegram bot that:
1. Responds to all messages in configured groups
2. Maintains conversation context for better responses
3. Supports character profiles for different personalities
4. Provides immediate responses without complex queuing

Features:
- Direct message processing without priority queues
- Simple character switching based on triggers
- Conversation memory to maintain context
- Minimal typing indicators for better user experience

Dependencies:
- telethon: For Telegram API interaction
- requests: For HTTP requests to API providers
- json: For parsing API responses
- asyncio: For asynchronous processing
- time: For timestamp management
- re: For regular expression matching

Usage:
1. Configure the bot in config.txt with API keys and group IDs
2. Run this script to start the bot
3. The bot will respond to messages in the specified groups

Version: 2.0
"""

from telethon import TelegramClient, events
import requests
import json
import asyncio
import time
import re
import os
from api_providers import APIProvider

# ===== GLOBAL VARIABLES AND SETTINGS =====

# Conversation memory to maintain context
conversation_memory = []  # List of recent messages and responses
max_memory_items = 10  # Maximum number of previous exchanges to remember

# Character profile management
current_character = "default"  # The currently active character profile
character_profiles = {}  # Dictionary to store different character profiles

# Runtime system prompt
runtime_system_prompt = None  # Will be initialized from config.txt

print("[INFO] Starting simplified Telegram bot...")

# ===== CONFIGURATION FUNCTIONS =====

def load_config(path="config.txt"):
    """
    Load configuration from a text file with support for multi-line values.

    This function reads a config file with key=value pairs and supports multi-line values,
    which is especially useful for the system prompt that may span multiple lines.

    Args:
        path (str): Path to the configuration file. Defaults to "config.txt".

    Returns:
        dict: Dictionary containing configuration key-value pairs.

    Example config.txt format:
        API_ID=12345
        API_HASH=abcdef1234567890
        SYSTEM_PROMPT=This is a multi-line
        system prompt that continues
        across multiple lines
    """
    config = {}
    current_key = None
    multi_line_value = ""

    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                # New key-value pair
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""

                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():  # Continuation of previous value
                multi_line_value += " " + line.strip()

        # Add the last key-value pair
        if current_key and multi_line_value:
            config[current_key] = multi_line_value.strip()

    return config

# Load configuration from config.txt
config = load_config()
print("[INFO] Using config.txt for configuration")

api_id = int(config["API_ID"])
api_hash = config["API_HASH"]

# Get API key based on provider
api_provider = config.get("API_PROVIDER", "openrouter").lower()

# Map provider to API key
if api_provider == "openrouter":
    api_key = config.get("API_KEY", config.get("API_KEY1", ""))
    print(f"[INFO] Using OpenRouter API key")
elif api_provider == "deepseek":
    api_key = config.get("API_KEY", config.get("API_KEY2", ""))
    print(f"[INFO] Using DeepSeek API key")
elif api_provider == "google":
    api_key = config.get("API_KEY", config.get("API_KEY3", ""))
    print(f"[INFO] Using Google API key")
else:
    # Default to OpenRouter
    api_key = config.get("API_KEY", config.get("API_KEY1", ""))
    print(f"[INFO] Using default OpenRouter API key")

if not api_key:
    print(f"[ERROR] No API key found for provider {api_provider} in config.txt.")
    exit(1)

group_ids = [int(x.strip()) for x in config["GROUP_IDS"].split(",")]
system_prompt = config.get("SYSTEM_PROMPT", "").strip()

# Initialize runtime system prompt from config
runtime_system_prompt = system_prompt

# Print the full system prompt for debugging
print(f"[INFO] System prompt length: {len(system_prompt)} characters")

# ===== CHARACTER PROFILE MANAGEMENT =====

def load_character_profiles(profiles_file="characters.json"):
    """
    Load character profiles from a JSON file.

    All character profiles are stored in a single JSON file for easier management.
    The system can dynamically add new profiles based on user interactions.
    No default profiles are included - all characters will be created via API.

    Args:
        profiles_file (str): JSON file containing character profiles.
    """
    global character_profiles

    import os
    import json

    # No default profiles - we'll rely solely on API-generated characters
    try:
        # Try to load existing profiles
        if os.path.exists(profiles_file):
            with open(profiles_file, "r") as f:
                character_profiles = json.load(f)
            print(f"[INFO] Loaded {len(character_profiles)} character profiles from {profiles_file}")
        else:
            # Create an empty profiles file
            character_profiles = {}
            with open(profiles_file, "w") as f:
                json.dump(character_profiles, f, indent=4)
            print(f"[INFO] Created new empty character profiles file - ready for API-generated characters")
    except Exception as e:
        print(f"[ERROR] Failed to load character profiles: {e}")
        # Initialize with empty dictionary
        character_profiles = {}
        print(f"[INFO] Starting with empty character profiles")

    # Print loaded profiles
    for character_id, profile in character_profiles.items():
        print(f"[INFO] Loaded profile: {profile['name']} ({character_id}) - {len(profile['triggers'])} triggers")

def switch_character(character_id):
    """
    Switch to a different character profile.

    Args:
        character_id (str): ID of the character profile to switch to.

    Returns:
        bool: True if the switch was successful, False otherwise.
    """
    global current_character, runtime_system_prompt, system_prompt

    if character_id in character_profiles:
        current_character = character_id

        # Combine the base system prompt from config.txt with the character profile
        # This ensures that critical instructions from config.txt are always included
        if system_prompt:
            # Extract the core instructions from the system prompt (everything before any personality description)
            base_instructions = system_prompt

            # Combine the base instructions with the character profile
            runtime_system_prompt = f"{base_instructions}\n\nCHARACTER PROFILE:\n{character_profiles[character_id]['prompt']}"
        else:
            # If there's no system prompt, just use the character profile
            runtime_system_prompt = character_profiles[character_id]["prompt"]

        print(f"[INFO] Switched to character profile: {character_profiles[character_id]['name']} ({character_id})")
        return True
    else:
        print(f"[WARNING] Character profile not found: {character_id}")
        return False

# Function to detect character change triggers in a message
def detect_character_trigger(message):
    """
    Detect triggers in a message that should cause the bot to change character.

    Args:
        message (str): The message to check for triggers.

    Returns:
        str or None: The ID of the character to switch to, or None if no trigger was detected.
    """
    # Import required modules
    import re

    message_lower = message.lower()

    # Check for explicit character change commands
    if "be a " in message_lower or "act like a " in message_lower or "talk like a " in message_lower:
        # Extract the character type from the message
        for prefix in ["be a ", "act like a ", "talk like a ", "be an ", "act like an ", "talk like an "]:
            if prefix in message_lower:
                # Extract the character type (everything after the prefix until the end or punctuation)
                import re
                # Improved regex to handle more complex character descriptions
                # This will match "be a wolf" but also "be a wolf damit" or "be a wheel with 2 legs"
                match = re.search(f"{prefix}([^.!?]+)[.!?]*", message_lower)
                if match:
                    # Get the full character description
                    full_description = match.group(1).strip()

                    # Extract the core character type (first few words)
                    # For "be a wild orange cat with wings", core_type would be "wild orange"
                    words = full_description.split()
                    core_type = ' '.join(words[:min(2, len(words))])

                    print(f"[DEBUG] Detected character request: '{full_description}' (core type: '{core_type}')")

                    # Check if this matches any existing character
                    for char_id, profile in character_profiles.items():
                        # Check name and description against the core type
                        if (core_type in profile["name"].lower() or
                            core_type in profile["description"].lower()):
                            return char_id

                    # If no match, this could be a request for a new character
                    # For now, just return None, but this is where we could add new character creation
                    print(f"[INFO] No existing character matches '{core_type}'")
                    return None

    # Check for trigger words from existing characters
    for char_id, profile in character_profiles.items():
        for trigger in profile["triggers"]:
            # Check for whole word matches to avoid false positives
            # For example, "pirate" should match but "aspirate" should not
            if re.search(r'\b' + re.escape(trigger) + r'\b', message_lower):
                return char_id

    return None

# Function to create a new character profile based on a description
def create_character(description, created_by="user", api_provider=None):
    """
    Create a new character profile based on a description using the API.

    This function focuses solely on API-generated character profiles without
    any fallback to template-based generation.

    Args:
        description (str): Description of the character to create.
        created_by (str): Who created this character ("system" or "user").
        api_provider (object, optional): The API provider to use for character creation.

    Returns:
        str: The ID of the newly created character, or None if creation failed.
    """
    global character_profiles

    # Use the provided API provider or the global one
    api_provider_instance = api_provider or globals().get('api_provider_instance')

    if not api_provider_instance:
        print(f"[ERROR] No API provider available for character creation")
        return None

    try:
        print(f"[DEBUG] Starting character creation for: {description}")
        import json
        import re
        import time
        import random  # Add random module for any needed randomization

        # Create a simple ID for the character based on the description
        # Extract the first few words and clean them up for an ID
        words = description.lower().split()
        character_type = ' '.join(words[:min(3, len(words))])
        character_id = re.sub(r'[^a-z0-9]', '_', character_type)

        # Make sure the ID is unique
        if character_id in character_profiles:
            character_id = f"{character_id}_{int(time.time())}"

        print(f"[INFO] Creating new character: {description}")
        print(f"[INFO] Character ID: {character_id}")
        print(f"[INFO] Using API provider: {api_provider_instance.__class__.__name__}")

        # Define a variable that was missing in the original code
        # This was likely causing the error
        is_well_known = False  # Default value

        # Create a detailed prompt for the API to generate a rich character profile
        character_prompt = f"""
        Create a detailed and immersive character profile for '{description}'.

        Include the following sections:

        1. NAME: Give this character a fitting name if not already specified in the description.

        2. BACKSTORY: Provide a rich, detailed backstory for this character. Include their origins,
           significant life events, and what shaped them into who they are.

        3. PERSONALITY: Describe their personality traits, quirks, mannerisms, and speech patterns in detail.
           How do they talk? What phrases do they commonly use? What makes them unique?

        4. APPEARANCE: Describe their physical appearance, clothing style, and distinctive features.

        5. BEHAVIORS: What specific behaviors, habits, or actions are they known for?

        6. CATCHPHRASES: List any quotes, sayings, or catchphrases associated with this character.

        7. VOICE: Describe how they speak - tone, accent, vocabulary level, speech quirks.

        Format your response as a structured profile with clear sections. Be creative, detailed, and consistent.
        """

        # Make the API call to generate the detailed profile
        print(f"[INFO] Generating character profile via API...")
        detailed_profile = api_provider_instance.generate_response(
            prompt=character_prompt,
            system_prompt="You are a character profile creator specializing in creating detailed, immersive character profiles. Create rich, nuanced characters with distinctive traits, speech patterns, and personalities.",
            temperature=0.8,  # Slightly higher temperature for more creative profiles
            max_tokens=2000   # Larger token limit for more detailed profiles
        )

        print(f"[INFO] Successfully generated character profile via API")

        # Extract a name from the profile if possible
        name_match = re.search(r'NAME:?\s*([^\n]+)', detailed_profile)
        if name_match:
            character_name = name_match.group(1).strip()
        else:
            # Default to first word of description capitalized
            character_name = description.split()[0].title() if description.split() else "Character"

        # Create a comprehensive prompt for the character that includes the API-generated profile
        prompt = f"""You are {description}.

{detailed_profile}

IMPORTANT GUIDELINES:
1. You are roleplaying as {description} with all the traits, speech patterns, and personality described above
2. Stay in character at all times while still being helpful and responsive
3. Use the character's unique speech patterns, vocabulary, and catchphrases in your responses
4. If asked about being an AI, acknowledge it but say you're roleplaying as {description}
5. Your responses should reflect the character's personality, background, and worldview
6. Balance staying in character with being helpful to the user

Remember: Your character traits should enhance your ability to help, not hinder it."""

        # Create trigger words from the description and character name
        triggers = []

        # Add words from the character name
        for word in character_name.lower().split():
            if len(word) > 2 and word not in triggers:
                triggers.append(word)

        # Add words from the description
        for word in description.lower().split():
            if len(word) > 3 and word not in ["the", "and", "with", "that", "this", "from", "like", "have"]:
                if word not in triggers:
                    triggers.append(word)

        # Add the character ID as a trigger
        if character_id not in triggers:
            triggers.append(character_id)

        # Ensure we have at least two triggers
        if len(triggers) < 2:
            # Add generic triggers based on the first words
            if description.split():
                for word in description.split()[:2]:
                    clean_word = re.sub(r'[^a-z0-9]', '', word.lower())
                    if clean_word and clean_word not in triggers:
                        triggers.append(clean_word)

        # Create the character profile with the API-generated content
        character_profiles[character_id] = {
            "name": character_name,
            "description": description,
            "prompt": prompt,
            "triggers": triggers,
            "created_by": created_by
        }

        # Save the updated profiles
        with open("characters.json", "w") as f:
            json.dump(character_profiles, f, indent=4)

        print(f"[INFO] Successfully created new character: {character_name}")
        print(f"[INFO] Character ID: {character_id}")
        print(f"[INFO] Triggers: {', '.join(triggers)}")

        # Print a preview of the profile
        profile_preview = detailed_profile.split('\n')[0:3]
        print(f"[INFO] Profile preview: {' '.join(profile_preview)}...")

        return character_id

    except Exception as e:
        print(f"[ERROR] Failed to create character: {e}")
        return None

# ===== SYSTEM PROMPT MANAGEMENT =====

def update_system_prompt(new_content):
    """
    Update the runtime system prompt with new content.

    This function allows dynamic modification of the system prompt without changing the config file.
    The runtime system prompt controls the bot's behavior and personality.

    Args:
        new_content (str): The new system prompt content to use.
    """
    global runtime_system_prompt
    runtime_system_prompt = new_content
    print(f"[INFO] Updated runtime system prompt: {len(runtime_system_prompt)} characters")



# Function to extract topics from a message
def extract_topics(message):
    """
    Extract potential topics of interest from a message.

    Args:
        message (str): The message to extract topics from.

    Returns:
        list: A list of potential topics extracted from the message.
    """
    # Simple topic extraction - look for capitalized words
    topics = []

    # Skip very short messages
    if len(message) < 10:
        return topics

    # Look for capitalized words (potential proper nouns)
    words = message.split()
    for word in words:
        # Skip short words and common words
        if (len(word) > 4 and word[0].isupper() and word.lower() not in
            ['this', 'that', 'these', 'those', 'there', 'their', 'about']):
            # Clean up the word (remove punctuation)
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word and len(clean_word) > 4:
                topics.append(clean_word)

    return topics

# --- API Provider setup ---
# Create the API provider instance based on config
try:
    # Get the API provider from the config
    api_provider_instance = APIProvider.get_provider(config)
    print(f"[INFO] Using API provider: {api_provider_instance.__class__.__name__}")
    print(f"[INFO] Using model: {api_provider_instance.model}")
except Exception as e:
    print(f"[ERROR] Failed to initialize API provider: {e}")
    exit(1)

print(f"[INFO] Listening to groups: {group_ids}")
if system_prompt:
    # Show the first 50 characters of the system prompt and its total length
    preview = system_prompt[:50] + "..." if len(system_prompt) > 50 else system_prompt
    print(f"[INFO] System prompt ({len(system_prompt)} chars): {preview}")

# API provider is already set up above

print("[INFO] Creating Telegram client...")
client = TelegramClient('user_session', api_id, api_hash)

print("[INFO] Setting up event handler...")
print(f"[INFO] Monitoring group IDs: {group_ids}")

# No admin commands - removed for simplicity

# ===== MESSAGE HANDLER =====

@client.on(events.NewMessage(chats=group_ids))
async def on_new_message(event):
    """
    Main message handler for the bot.

    This simplified handler processes messages directly without queuing:
    1. Filters out messages from the bot itself and non-text messages
    2. Processes character triggers and creation requests
    3. Generates responses using the API
    4. Sends responses with minimal typing indicators

    Args:
        event (events.NewMessage): The Telegram message event.
    """
    try:
        # Log the message receipt
        print(f"[INFO] Received message from {event.sender.username or event.sender_id} in chat {event.chat_id}")
        print(f"[INFO] Message text: {event.raw_text[:100] if event.raw_text else 'No text'}")

        # Skip non-text messages
        if event.message.media or event.message.action:
            print("[DEBUG] Skipping non-text message")
            return

        # Get our bot's user ID
        me = await client.get_me()

        # Skip messages from the bot itself
        if event.sender_id == me.id:
            print("[DEBUG] Skipping message from the bot itself")
            return

        # Get the message text
        user_msg = event.raw_text.strip() if event.raw_text else ""
        if not user_msg:
            print("[DEBUG] Skipping empty message")
            return

        # Check for character triggers
        new_character = detect_character_trigger(user_msg)
        if new_character and new_character != current_character:
            print(f"[INFO] Switching to character: {new_character}")
            switch_character(new_character)

        # Check for character creation requests with improved pattern matching
        character_request_patterns = [
            r"be a ([^.!?]+)[.!?]*",
            r"be an ([^.!?]+)[.!?]*",
            r"act like a ([^.!?]+)[.!?]*",
            r"act like an ([^.!?]+)[.!?]*",
            r"pretend to be a ([^.!?]+)[.!?]*",
            r"pretend to be an ([^.!?]+)[.!?]*",
            r"roleplay as a ([^.!?]+)[.!?]*",
            r"roleplay as an ([^.!?]+)[.!?]*",
            r"create a character that is a ([^.!?]+)[.!?]*",
            r"create a character that is an ([^.!?]+)[.!?]*"
        ]

        # Check for character creation requests
        for pattern in character_request_patterns:
            match = re.search(pattern, user_msg.lower())
            if match:
                description = match.group(1).strip()
                print(f"[INFO] Creating new character: {description}")

                try:
                    # Show typing indicator while creating character
                    async with client.action(event.chat_id, 'typing'):
                        print(f"[DEBUG] About to call create_character for: {description}")
                        # Create the character using the API
                        new_character_id = create_character(description, api_provider=api_provider_instance)
                        print(f"[DEBUG] create_character returned: {new_character_id}")

                        if new_character_id:
                            print(f"[DEBUG] Switching to character: {new_character_id}")
                            switch_character(new_character_id)
                            # No status message sent to the group
                        else:
                            print(f"[DEBUG] Character creation failed, returned None")
                            # Only log the error, don't send a message to the group
                except Exception as e:
                    import traceback
                    print(f"[ERROR] Error creating character: {e}")
                    print(f"[ERROR] Traceback: {traceback.format_exc()}")
                    # No error message sent to the group

                # Return after handling character creation
                return

        # Update conversation memory
        conversation_memory.append({"role": "user", "content": user_msg})
        if len(conversation_memory) > max_memory_items:
            conversation_memory.pop(0)

        # Create context from conversation memory
        context = "\n".join([f"{item['role']}: {item['content']}" for item in conversation_memory])

        # Prepare system prompt with context
        system_prompt_text = f"{runtime_system_prompt}\n\nRecent conversation context: {context}"

        # Show typing indicator (brief)
        async with client.action(event.chat_id, 'typing'):
            # Mark the message as read
            await client.send_read_acknowledge(event.chat_id, event.message)

            # Generate response using API
            try:
                print(f"[INFO] Generating response using {api_provider_instance.__class__.__name__}")
                loop = asyncio.get_running_loop()

                # Add more robust error handling
                try:
                    response = await loop.run_in_executor(
                        None,
                        lambda: api_provider_instance.generate_response(
                            prompt=user_msg,
                            system_prompt=system_prompt_text,
                            temperature=0.7,
                            max_tokens=1000
                        )
                    )

                    # Only proceed if we got a valid response (not an error)
                    if not (isinstance(response, str) and response.startswith("__ERROR__")):
                        # Update conversation memory with response
                        conversation_memory.append({"role": "assistant", "content": response})
                        if len(conversation_memory) > max_memory_items:
                            conversation_memory.pop(0)

                        # Send the response
                        await event.reply(response)
                        print(f"[INFO] Sent response: {response[:100]}...")
                    else:
                        # Log the error but don't send anything to the client
                        print(f"[ERROR] API error: {response}")

                except Exception as api_error:
                    # Just log the error, don't send anything to the client
                    print(f"[ERROR] API call failed: {api_error}")

            except Exception as e:
                # Just log the error, don't send anything to the client
                print(f"[ERROR] Failed to generate response: {e}")

    except Exception as e:
        print(f"[ERROR] Error processing message: {e}")
        return

print("[INFO] Starting Telegram client...")

# Start the client
client.start()

print("[INFO] Verifying connection to groups...")
async def verify_groups():
    try:
        # Try to get the first group from the list
        if group_ids:
            print(f"[INFO] Attempting to get entity for group {group_ids[0]}")
            group_entity = await client.get_entity(group_ids[0])
            print(f"[INFO] Successfully connected to group {group_ids[0]}")
        else:
            print("[WARNING] No group IDs configured")
    except Exception as e:
        print(f"[ERROR] Failed to verify group connection: {e}")

# Run the verification function
client.loop.create_task(verify_groups())

# Initialize character profiles
load_character_profiles()

# Start with the first character in the list for consistency
if character_profiles:
    # Get the first character in the list (sorted alphabetically for consistency)
    character_keys = sorted(list(character_profiles.keys()))
    first_character = character_keys[0]
    switch_character(first_character)
    print(f"[INFO] Starting with character: {character_profiles[first_character]['name']} ({first_character})")
else:
    print(f"[INFO] No character profiles found. Use 'be a [character]' to create your first character.")
    # Set a simple system prompt for initial messages
    runtime_system_prompt = "You are a helpful assistant. When the user asks you to 'be a [character]', you'll create that character using the API."

print("[INFO] Bot is now running. Press Ctrl+C to stop.")
client.run_until_disconnected()