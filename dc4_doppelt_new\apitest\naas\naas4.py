import requests
import json
import time

API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzNDk4YWE5MC1kNTc4LTRkOGUtOWRkOS0wYjAxMmQxYjU1ZWIiLCJhcGlfa2V5X2lkIjoiMzE0OTcxZWEtNzU2NC00M2NjLTlhMTctMGVlMzRjNTA3MTQwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDQtMTJUMDk6MjE6NTMuODUwODA2KzAwOjAwIn0.0irR8V6GseNppTqfhDnENiJB54kpvXD0eF5sXDPxRjM"
BASE_URL = "https://api.naas.ai/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

def fetch_available_models():
    """
    Fetches available models from the Naas.ai API.
    """
    url = f"{BASE_URL}/models/list"
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get("models", [])
    except requests.exceptions.RequestException as e:
        print(f"Error fetching models: {e}")
        if hasattr(e.response, 'text'):
            print(f"Response: {e.response.text}")
        return []

def test_model_completion(model_id):
    """
    Tests a specific model using the chat/completions endpoint.
    """
    url = f"{BASE_URL}/chat/completions"
    payload = {
        "model": model_id,
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Write a short greeting."}
        ],
        "max_tokens": 50,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        
        if "choices" in data and len(data["choices"]) > 0:
            return {
                "success": True,
                "response": data["choices"][0]["message"]["content"],
                "usage": data.get("usage", {}),
                "model": model_id
            }
        return {
            "success": False,
            "error": "No response generated",
            "model": model_id
        }
    except requests.exceptions.HTTPError as e:
        print(f"HTTP Error with model {model_id}: {e}")
        print(f"Response: {e.response.text if hasattr(e, 'response') else 'No response text'}")
        return {
            "success": False,
            "error": str(e),
            "model": model_id
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "model": model_id
        }

def main():
    print("Fetching available models from Naas.ai...")
    models = fetch_available_models()

    if not models:
        print("No models found or error fetching models.")
        return

    print(f"\nFound {len(models)} models:")
    for model in models:
        print(f"- {model}")

    results = []
    print("\nTesting models:")
    for model in models:
        print(f"\nTesting model: {model}")
        result = test_model_completion(model)
        results.append(result)
        
        if result["success"]:
            print(f"Success! Response: {result['response']}")
            if "usage" in result:
                print(f"Token usage: {result['usage']}")
        else:
            print(f"Failed! Error: {result.get('error', 'Unknown error')}")
        
        time.sleep(2)  # Rate limiting precaution

    # Save results to file
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = f"naas_model_results_{timestamp}.json"
    with open(filename, "w") as f:
        json.dump(results, f, indent=2)
    print(f"\nResults saved to {filename}")

if __name__ == "__main__":
    main()
