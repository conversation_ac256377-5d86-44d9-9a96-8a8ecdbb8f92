# Telegram <PERSON>t Context Handling Improvements

## Current Issues
1. <PERSON><PERSON> is directly quoting messages in its responses (repeating "Message 3:", "Message 5:", etc.)
2. <PERSON><PERSON> is addressing specific messages rather than responding to the overall conversation
3. The formatting we're using for context is being reflected in the bot's responses
4. <PERSON><PERSON> sometimes refers to users by IDs or as "User"
5. Including bot's own messages wastes token context

## What We've Tried

1. **Changed how we label users**: Replaced usernames/IDs with generic labels
   - Replaced numeric IDs with "Someone"
   - Changed bot's label from "bot" to "Assistant"
   - Used position markers instead of usernames

2. **Filtered out bot messages**: 
   - Modified `get_group_context()` to exclude bot messages
   - Added `include_bot_messages=False` parameter

3. **Used message numbers**: 
   - Replaced user identifiers with "Message 1", "Message 2", etc.
   - Removed sender information entirely from some contexts

4. **Improved special context**: 
   - Made it clearer which message is being replied to
   - Used "Your previous message" instead of "<PERSON><PERSON>:"
   - Used "Their reply" instead of usernames

5. **Simplified context formatting**: 
   - Removed unnecessary labels and headers
   - Made context structure more consistent

## Potential Solutions

### Solution 1: Separate Content from Metadata
- Modify the context format to clearly separate message content from metadata
- Use a format that the AI won't be tempted to mimic in its responses
- Example:
  ```
  CONTEXT:
  [1] "Hey, what's the weather like?"
  [2] "It's sunny today."
  ```

### Solution 2: Use System Prompt to Explicitly Prohibit Message Quoting
- Add explicit instructions in the system prompt to not quote or reference message numbers
- Emphasize responding to the overall conversation rather than individual messages
- Add examples of good and bad responses

### Solution 3: Use a Different Context Format Entirely
- Instead of numbered messages, use a more narrative format
- Present the conversation as a summary rather than verbatim messages
- Focus on topics and themes rather than individual messages

### Solution 4: Use a Two-Stage Approach
- First, generate a summary of the conversation context
- Then, use that summary (rather than raw messages) as context for the response

### Solution 5: Use Special Formatting That Won't Be Mimicked
- Use a format that's clearly for machine parsing, not human writing
- Example: `<msg id="1" content="Hey, what's the weather like?" />`
- The AI is unlikely to mimic XML-like formatting in its responses

## Implementation Priority

1. **Solution 2**: Update system prompt with explicit instructions (easiest, quickest fix)
2. **Solution 1**: Modify context format to separate content from metadata
3. **Solution 5**: Use special formatting that won't be mimicked
4. **Solution 3**: Try a more narrative context format if other solutions don't work
5. **Solution 4**: Implement two-stage approach if needed (most complex)
