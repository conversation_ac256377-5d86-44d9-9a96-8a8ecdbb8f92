import requests
import json
import time
from typing import Optional, Dict, List, Any

class NaasAPI:
    def __init__(self, api_key: str):
        """Initialize the NAAS API client with authentication."""
        self.base_url = "https://api.naas.ai"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def _handle_response(self, response: requests.Response) -> Dict:
        """Handle API response and errors consistently."""
        try:
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            error_msg = str(e)
            if hasattr(e.response, 'text'):
                error_msg = f"{error_msg}: {e.response.text}"
            print(f"API Error: {error_msg}")
            return {
                "error": {
                    "code": getattr(e.response, 'status_code', 500),
                    "message": error_msg
                }
            }

    def list_models(self, page_size: int = 20, page_number: int = 0) -> Dict:
        """
        List AI models available to the authenticated user.
        
        Args:
            page_size: Number of results per page
            page_number: Page number to return
            
        Returns:
            Dict containing list of AI models or error details
        """
        endpoint = "/v1/aimodels"
        params = {
            "page_size": page_size,
            "page_number": page_number
        }
        response = requests.get(
            f"{self.base_url}{endpoint}",
            headers=self.headers,
            params=params
        )
        return self._handle_response(response)

    def get_model(self, model_id: str) -> Dict:
        """
        Get details for a specific AI model.
        
        Args:
            model_id: ID of the model to retrieve
            
        Returns:
            Dict containing model details or error info
        """
        endpoint = f"/v1/aimodels/{model_id}"
        response = requests.get(
            f"{self.base_url}{endpoint}",
            headers=self.headers
        )
        return self._handle_response(response)

    def create_completion(
        self, 
        model_id: str, 
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = 50,
        temperature: Optional[float] = 0.7
    ) -> Dict:
        """
        Create a completion request using a specific AI model.
        
        Args:
            model_id: ID of the model to use
            messages: List of message objects with role and content
            max_tokens: Maximum tokens in response
            temperature: Sampling temperature
            
        Returns:
            Dict containing completion response or error details
        """
        endpoint = "/v1/chat/completions"
        payload = {
            "model": model_id,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        response = requests.post(
            f"{self.base_url}{endpoint}",
            headers=self.headers,
            json=payload
        )
        return self._handle_response(response)

def test_api():
    """Test the NAAS API client functionality."""
    # Replace with your API key
    api_key = "your_api_key_here"
    client = NaasAPI(api_key)
    
    print("Testing NAAS API client...")
    
    # List available models
    print("\nListing available models:")
    models_response = client.list_models()
    if "aimodels" in models_response:
        for model in models_response["aimodels"]:
            print(f"- {model.get('id', 'Unknown ID')}: {model.get('name', 'Unknown name')}")
    else:
        print("Error listing models:", models_response.get("error", {}).get("message"))

    # Test completion with a simple message
    print("\nTesting chat completion:")
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Write a short greeting."}
    ]
    completion_response = client.create_completion(
        model_id="gpt-3.5-turbo",  # Replace with actual model ID from list_models
        messages=messages
    )
    
    if "completion" in completion_response:
        completion = completion_response["completion"]
        print("Response:", completion.get("messages", [{}])[0].get("content", "No response"))
        print(f"Tokens used - Input: {completion.get('input_tokens', 0)}, Output: {completion.get('output_tokens', 0)}")
    else:
        print("Error creating completion:", completion_response.get("error", {}).get("message"))

if __name__ == "__main__":
    test_api()