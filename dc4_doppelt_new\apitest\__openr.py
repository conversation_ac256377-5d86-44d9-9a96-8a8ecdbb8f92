import json
import requests
import time

OPENROUTER_API_KEY = "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"
MODELS = [
    "agentica-org/deepcoder-14b-preview:free",
    "allenai/molmo-7b-d:free",
    "arliai/qwq-32b-arliai-rpr-v1:free",
    "bytedance-research/ui-tars-72b:free",
    "cognitivecomputations/dolphin3.0-mistral-24b:free",
    "cognitivecomputations/dolphin3.0-r1-mistral-24b:free",
    "deepseek/deepseek-chat-v3-0324:free",
    "deepseek/deepseek-chat:free",
    "deepseek/deepseek-r1-distill-llama-70b:free",
    "deepseek/deepseek-r1-distill-qwen-14b:free",
    "deepseek/deepseek-r1-distill-qwen-32b:free",
    "deepseek/deepseek-r1-zero:free",
    "deepseek/deepseek-r1:free",
    "deepseek/deepseek-v3-base:free",
    "featherless/qwerky-72b:free",
    "google/gemini-2.0-flash-exp:free",
    "google/gemini-2.0-flash-thinking-exp-1219:free",
    "google/gemini-2.0-flash-thinking-exp:free",
    "google/gemini-2.5-pro-exp-03-25:free",
    "google/gemma-2-9b-it:free",
    "google/gemma-3-12b-it:free",
    "google/gemma-3-1b-it:free",
    "google/gemma-3-27b-it:free",
    "google/gemma-3-4b-it:free",
    "google/learnlm-1.5-pro-experimental:free",
    "huggingfaceh4/zephyr-7b-beta:free",
    "inflection/inflection-3-pi:free",
    "inflection/inflection-3-productivity:free",
    "meta-llama/llama-3.1-405b:free",
    "meta-llama/llama-3.1-8b-instruct:free",
    "meta-llama/llama-3.2-11b-vision-instruct:free",
    "meta-llama/llama-3.2-1b-instruct:free",
    "meta-llama/llama-3.2-3b-instruct:free",
    "meta-llama/llama-3.3-70b-instruct:free",
    "meta-llama/llama-4-maverick:free",
    "meta-llama/llama-4-scout:free",
    "microsoft/mai-ds-r1:free",
    "mistralai/mistral-7b-instruct:free",
    "mistralai/mistral-nemo:free",
    "mistralai/mistral-small-24b-instruct-2501:free",
    "mistralai/mistral-small-3.1-24b-instruct:free",
    "moonshotai/kimi-vl-a3b-thinking:free",
    "moonshotai/moonlight-16b-a3b-instruct:free",
    "nousresearch/deephermes-3-llama-3-8b-preview:free",
    "nvidia/llama-3.1-nemotron-70b-instruct:free",
    "nvidia/llama-3.1-nemotron-nano-8b-v1:free",
    "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",
    "nvidia/llama-3.3-nemotron-super-49b-v1:free",
    "open-r1/olympiccoder-32b:free",
    "open-r1/olympiccoder-7b:free",
    "qwen/qwen-2.5-72b-instruct:free",
    "qwen/qwen-2.5-7b-instruct:free",
    "qwen/qwen-2.5-coder-32b-instruct:free",
    "qwen/qwen-2.5-vl-32b-instruct:free",
    "qwen/qwen-2.5-vl-3b-instruct:free",
    "qwen/qwen-2.5-vl-72b-instruct:free",
    "qwen/qwen-2.5-vl-7b-instruct:free",
    "qwen/qwen2.5-vl-32b-instruct:free",
    "qwen/qwen2.5-vl-3b-instruct:free",
    "qwen/qwen2.5-vl-72b-instruct:free",
    "qwen/qwq-32b-preview:free",
    "qwen/qwq-32b:free",
    "rekaai/reka-flash-3:free",
    "shisa-ai/shisa-v2-llama3.3-70b:free",
    "sophosympatheia/rogue-rose-103b-v0.2:free",
    "thudm/glm-4-32b:free",
    "thudm/glm-z1-32b:free",
    "x-ai/grok-2-1212:free",
    "x-ai/grok-2-vision-1212:free",
    "x-ai/grok-beta:free"
]

failed_models = []
for MODEL in MODELS:
    OPENROUTER_API_KEY = "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"
    prompt = "only say a number between 1 and 10"

    response = requests.post(
        url="https://openrouter.ai/api/v1/chat/completions",
        headers={
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "HTTP-Referer": "https://example.com",
            "X-Title": "My App",
        },
        data=json.dumps({
            "model": MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 100,
            "temperature": 0.2
        })
    )

    print(f"Model: {MODEL}")
    print(response.json())
    if 'error' in response.json() and 'message' in response.json()['error'] and 'Insufficient credits' in response.json()['error']['message']:
        print(f"Model {MODEL} has insufficient credits.")
        failed_models.append(MODEL)
    elif response.status_code != 200:
        print(f"Model {MODEL} failed with status code: {response.status_code}")
        failed_models.append(MODEL)
    else:
        try:
            response_json = response.json()
            if 'choices' not in response_json or not response_json['choices']:
                print(f"Model {MODEL} failed to provide a valid response.")
                failed_models.append(MODEL)
            else:
                content = response_json['choices'][0]['message']['content']
                try:
                    int(content)
                except ValueError:
                    print(f"Model {MODEL} did not return a number between 1 and 10.")
                    failed_models.append(MODEL)
        except json.JSONDecodeError:
            print(f"Model {MODEL} returned invalid JSON.")
            failed_models.append(MODEL)
    
    # Add 15-second delay between requests
    time.sleep(1)
    print(f"Waiting 1 seconds before next request...")

print(f"Failed models (insufficient credits or invalid responses): {failed_models}")
print(f"Last model tested: {MODELS[-1]}")
