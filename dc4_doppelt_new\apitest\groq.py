import asyncio
from openai import OpenAI
import os

# Get your Groq API key from https://console.groq.com/keys
GROQ_API_KEY = "********************************************************"

client = OpenAI(
    api_key=GROQ_API_KEY,
    base_url="https://api.groq.com/openai/v1"
)

async def test_model(model_name, test_message="Hello, can you tell me a joke?"):
    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": test_message}
            ]
        )
        print(f"Success for {model_name}: {response.choices[0].message.content}")
    except Exception as e:
        print(f"Error for {model_name}: {e}")

async def main():
    free_models = [
        "llama-3.3-70b-versatile",
        "llama-3.1-8b-instant",
        "gemma2-9b-it",
        "deepseek-r1-distill-llama-70b",
        "qwen-qwq-32b",
        "mistral-saba-24b",
        "llama-guard-3-8b",
        "allam-2-7b",
        "compound-beta",
        "compound-beta-mini",
        "meta-llama/llama-4-maverick-17b-128e-instruct",
        "meta-llama/llama-4-scout-17b-16e-instruct",
        "llama3-70b-8192",
        "llama3-8b-8192",
        # Add other free models as needed
    ]

    print("Testing free models on Groq...")
    for model in free_models:
        print(f"\nTesting model: {model}")
        await test_model(model)
        await asyncio.sleep(30) # Be mindful of rate limits

if __name__ == "__main__":
    asyncio.run(main())