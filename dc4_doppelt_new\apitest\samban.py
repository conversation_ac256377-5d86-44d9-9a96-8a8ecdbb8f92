import asyncio
import traceback
import aiohttp

# Configuration
url = "https://api.sambanova.ai/v1/chat/completions"  # Assumed endpoint based on common API structures
API_KEY = "614173dd-ccd8-43b0-9179-2bf8f864064c"     # Provided Sambanova API key
TEST_MESSAGE = "Hello, how are you?"
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}
DELAY_SECONDS = 30  # Adjust as needed

# Sambanova models to test
SAMBOVA_MODELS = [
    "DeepSeek-R1",
    "DeepSeek-R1-Distill-Llama-70B",
    "DeepSeek-V3-0324",
    "Llama-4-Maverick-17B-128E-Instruct",
    "Llama-4-Scout-17B-16E-Instruct",
    "Meta-Llama-3.1-405B-Instruct",
    "Meta-Llama-3.1-8B-Instruct",
    "Meta-Llama-3.2-1B-Instruct",
    "Meta-Llama-3.2-3B-Instruct",
    "Meta-Llama-3.3-70B-Instruct",
    "Meta-Llama-Guard-3-8B",
    "Qwen2-Audio-7B-Instruct",
    "QwQ-32B"
]

async def test_model(model_name):
    try:
        async with aiohttp.ClientSession() as session:
            payload = {
                "model": model_name,
                "messages": [{"role": "user", "content": TEST_MESSAGE}]
            }
            async with session.post(url, json=payload, headers=headers, ssl=False) as response:
                response.raise_for_status()
                data = await response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    print(f"  Response from {model_name}: {data['choices'][0]['message']['content']}")
                    return True
                else:
                    print(f"  No response from {model_name}: {data}")
                    return False
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

async def main():
    print("Testing Sambanova models...")
    for model_name in SAMBOVA_MODELS:
        print(f"\nTesting model: {model_name}")
        success = await test_model(model_name)
        if success:
            print(f"{model_name} is accessible")
        else:
            print(f"{model_name} is not accessible")
        await asyncio.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    asyncio.run(main())
