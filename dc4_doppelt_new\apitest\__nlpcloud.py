import requests
import json
import time

# Configuration
url = "https://api.nlpcloud.io/v1/{model_name}/generation"
API_KEY = "fa1dc2ad1d7099bc758fc4fb575e26a259a4b1f5"
headers = {
    "Authorization": f"Token {API_KEY}",
    "Content-Type": "application/json",
}
data = {
    "text": "LLaMA is a powerful NLP model",
    "stream": True,
}

NLPCLOUD_MODELS = [
    "chatdolphin",
    "chatdolphin-mixtral-8x7b",
    "dolphin-mixtral-8x7b",
    "dolphin-phi-2",
    "dolphin-yi-34b",
    "finetuned-gpt-neox-20b",
    "finetuned-llama-3-70b",
    "llama-3-1-405b",
    "mixtral-8x7b",
    "yi-34b"
]

def test_model(model_name):
    try:
        api_url = url.replace("{model_name}", model_name)
        print(f"Testing model: {model_name} at {api_url}")
        r = requests.post(api_url, headers=headers, json=data, stream=True)
        r.raise_for_status()

        for line in r.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                print(decoded_line)
        return True
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return False

def main():
    print("Testing NLP Cloud models...")
    for model_name in NLPCLOUD_MODELS:
        print(f"\nTesting model: {model_name}")
        success = test_model(model_name)
        if success:
            print(f"{model_name} is accessible")
        else:
            print(f"{model_name} is not accessible")
        time.sleep(5)  # Delay between requests

if __name__ == "__main__":
    main()
