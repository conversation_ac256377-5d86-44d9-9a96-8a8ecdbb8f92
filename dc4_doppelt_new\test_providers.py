#!/usr/bin/env python3
"""
Test script for API providers in the Telegram bot.
This script tests each provider individually to identify issues.
"""

import sys
import json
import httpx
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("provider_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config(path="config.txt"):
    """Load configuration from file"""
    config = {}
    current_key = None
    multi_line_value = ""
    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""
                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():
                multi_line_value += " " + line.strip()
    if current_key and multi_line_value:
        config[current_key] = multi_line_value.strip()
    return config

def test_openrouter(api_key, model="meta-llama/llama-3-8b-instruct"):
    """Test OpenRouter API"""
    logger.info(f"Testing OpenRouter with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    data = {
        "model": model,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    logger.debug(f"OpenRouter request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=30) as client:
            logger.debug("Sending request to OpenRouter API...")
            response = client.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            logger.debug(f"OpenRouter response status: {response.status_code}")
            logger.debug(f"OpenRouter response headers: {response.headers}")
            logger.debug(f"OpenRouter API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"OpenRouter API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            return True, result["choices"][0]["message"]["content"]
    except Exception as e:
        logger.exception("OpenRouter API error")
        return False, f"Exception: {str(e)}"

def test_cerebras(api_key, model="llama-3.1-8b-instruct"):
    """Test Cerebras API"""
    logger.info(f"Testing Cerebras with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    data = {
        "model": model,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    logger.debug(f"Cerebras request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=30) as client:
            logger.debug("Sending request to Cerebras API...")
            response = client.post(
                "https://api.cerebras.ai/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            logger.debug(f"Cerebras response status: {response.status_code}")
            logger.debug(f"Cerebras response headers: {response.headers}")
            logger.debug(f"Cerebras API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Cerebras API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            return True, result["choices"][0]["message"]["content"]
    except Exception as e:
        logger.exception("Cerebras API error")
        return False, f"Exception: {str(e)}"

def test_cohere(api_key, model="command-r"):
    """Test Cohere API"""
    logger.info(f"Testing Cohere with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    data = {
        "chat_history": [],
        "message": "Hello, how are you?",
        "model": model,
        "temperature": 0.7,
        "max_tokens": 100,
        "preamble": "You are a helpful assistant."
    }
    
    logger.debug(f"Cohere request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=30) as client:
            logger.debug("Sending request to Cohere API...")
            response = client.post(
                "https://api.cohere.ai/v1/chat",
                headers=headers,
                json=data
            )
            
            logger.debug(f"Cohere response status: {response.status_code}")
            logger.debug(f"Cohere response headers: {response.headers}")
            logger.debug(f"Cohere API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Cohere API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            if "text" in result:
                return True, result["text"]
            elif "reply" in result:
                return True, result["reply"]
            else:
                return False, "Could not parse Cohere response format"
    except Exception as e:
        logger.exception("Cohere API error")
        return False, f"Exception: {str(e)}"

def test_mistral(api_key, model="mistral-small-latest"):
    """Test Mistral API"""
    logger.info(f"Testing Mistral with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    data = {
        "model": model,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    logger.debug(f"Mistral request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=30) as client:
            logger.debug("Sending request to Mistral API...")
            response = client.post(
                "https://api.mistral.ai/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            logger.debug(f"Mistral response status: {response.status_code}")
            logger.debug(f"Mistral response headers: {response.headers}")
            logger.debug(f"Mistral API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Mistral API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            return True, result["choices"][0]["message"]["content"]
    except Exception as e:
        logger.exception("Mistral API error")
        return False, f"Exception: {str(e)}"

def test_codestral(api_key, model="codestral-latest"):
    """Test Codestral API"""
    logger.info(f"Testing Codestral with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    data = {
        "model": model,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    logger.debug(f"Codestral request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=30) as client:
            logger.debug("Sending request to Codestral API...")
            response = client.post(
                "https://api.cerebras.ai/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            logger.debug(f"Codestral response status: {response.status_code}")
            logger.debug(f"Codestral response headers: {response.headers}")
            logger.debug(f"Codestral API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Codestral API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            return True, result["choices"][0]["message"]["content"]
    except Exception as e:
        logger.exception("Codestral API error")
        return False, f"Exception: {str(e)}"

def main():
    """Main function to test all providers"""
    config = load_config()
    
    # Test each provider
    providers = {
        "openrouter": (test_openrouter, "OPENROUTER_API_KEY", "OPENROUTER_MODEL"),
        "cerebras": (test_cerebras, "CEREBRAS_API_KEY", "CEREBRAS_MODEL"),
        "cohere": (test_cohere, "COHERE_API_KEY", "COHERE_MODEL"),
        "mistral": (test_mistral, "MISTRAL_API_KEY", "MISTRAL_MODEL"),
        "codestral": (test_codestral, "CODESTRAL_API_KEY", "CODESTRAL_MODEL")
    }
    
    results = {}
    
    for provider_name, (test_func, key_name, model_name) in providers.items():
        api_key = config.get(key_name, "")
        model = config.get(model_name, "")
        
        if not api_key:
            logger.warning(f"API key missing for provider: {provider_name}")
            results[provider_name] = (False, "API key missing")
            continue
        
        success, response = test_func(api_key, model)
        results[provider_name] = (success, response)
    
    # Print summary
    print("\n===== PROVIDER TEST RESULTS =====")
    for provider, (success, response) in results.items():
        status = "SUCCESS" if success else "FAILED"
        print(f"{provider.upper()}: {status}")
        if not success:
            print(f"  Error: {response}")
    
    print("\nDetailed logs saved to provider_test.log")

if __name__ == "__main__":
    main()
