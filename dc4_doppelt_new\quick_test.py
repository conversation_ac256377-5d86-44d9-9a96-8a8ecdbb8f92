#!/usr/bin/env python3
"""
Quick test script for Cohere and Codestral providers.
"""

import httpx
import json
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load configuration
def load_config(path="config.txt"):
    """Load configuration from file"""
    config = {}
    with open(path, "r") as f:
        for line in f:
            line = line.strip()
            if '=' in line:
                key, value = line.split("=", 1)
                config[key.strip()] = value.strip()
    return config

config = load_config()

def test_cohere():
    """Test Cohere API"""
    api_key = config.get("COHERE_API_KEY")
    model = config.get("COHERE_MODEL")
    
    logger.info(f"Testing Cohere with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    data = {
        "chat_history": [],
        "message": "Hello, how are you?",
        "model": model,
        "temperature": 0.7,
        "max_tokens": 100,
        "preamble": "You are a helpful assistant."
    }
    
    logger.debug(f"Cohere request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=60) as client:  # Increased timeout
            logger.debug("Sending request to Cohere API...")
            response = client.post(
                "https://api.cohere.ai/v1/chat",
                headers=headers,
                json=data
            )
            
            logger.debug(f"Cohere response status: {response.status_code}")
            logger.debug(f"Cohere response headers: {response.headers}")
            logger.debug(f"Cohere API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Cohere API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            if "text" in result:
                return True, result["text"]
            elif "reply" in result:
                return True, result["reply"]
            else:
                return False, "Could not parse Cohere response format"
    except Exception as e:
        logger.exception("Cohere API error")
        return False, f"Exception: {str(e)}"

def test_codestral():
    """Test Codestral API"""
    api_key = config.get("CODESTRAL_API_KEY")
    model = config.get("CODESTRAL_MODEL")
    
    logger.info(f"Testing Codestral with model {model}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    data = {
        "model": model,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    logger.debug(f"Codestral request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=60) as client:  # Increased timeout
            logger.debug("Sending request to Codestral API...")
            response = client.post(
                "https://api.cerebras.ai/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            logger.debug(f"Codestral response status: {response.status_code}")
            logger.debug(f"Codestral response headers: {response.headers}")
            logger.debug(f"Codestral API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Codestral API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            return True, result["choices"][0]["message"]["content"]
    except Exception as e:
        logger.exception("Codestral API error")
        return False, f"Exception: {str(e)}"

def main():
    """Main function"""
    print("\n===== TESTING COHERE =====")
    cohere_success, cohere_response = test_cohere()
    print(f"Cohere: {'SUCCESS' if cohere_success else 'FAILED'}")
    if not cohere_success:
        print(f"  Error: {cohere_response}")
    else:
        print(f"  Response: {cohere_response[:100]}...")
    
    time.sleep(2)  # Add a delay between requests
    
    print("\n===== TESTING CODESTRAL =====")
    codestral_success, codestral_response = test_codestral()
    print(f"Codestral: {'SUCCESS' if codestral_success else 'FAILED'}")
    if not codestral_success:
        print(f"  Error: {codestral_response}")
    else:
        print(f"  Response: {codestral_response[:100]}...")

if __name__ == "__main__":
    main()
