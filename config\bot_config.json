{"telegram": {"api_id": "YOUR_TELEGRAM_API_ID", "api_hash": "YOUR_TELEGRAM_API_HASH", "bot_token": "YOUR_BOT_TOKEN", "session_name": "codebase_tg_bot"}, "ai": {"mistral_api_keys": ["n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350", "AMpMVqFQPpsTTzsq9HVqUWEs2jVEXcPw"], "current_key_index": 0, "model": "mistral-large-latest", "max_tokens": 1000, "temperature": 0.7}, "groups": {"monitored_groups": [], "target_groups": [], "excluded_groups": [-1002174208542], "character_groups": {}}, "forwarding": {"enabled": true, "remove_captions": true, "remove_sender": true, "media_only": true, "duplicate_check": true}, "features": {"message_forwarding": true, "mime_analysis": true, "duplicate_detection": true, "character_ai": true, "tts_integration": true, "link_extraction": true, "database_logging": true}, "performance": {"max_concurrent_requests": 10, "request_timeout": 30, "retry_attempts": 3, "rate_limit_delay": 1}}