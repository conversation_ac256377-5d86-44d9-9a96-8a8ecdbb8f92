import asyncio
from telethon import TelegramClient

# Configuration
api_id = 22690606
api_hash = '8ae0492f12f138146675ea03b3936fec'
phone_number = input("Please enter your phone number: ")

async def main():
    # Create the client and connect
    client = TelegramClient('session_name', api_id, api_hash)
    await client.start(phone_number)

    # Get all dialogs (chats)
    dialogs = await client.get_dialogs()

    # Extract group names and IDs
    groups = []
    for dialog in dialogs:
        if dialog.is_group:
            group_id = dialog.id
            if group_id > 0:
                group_id = -1000000000000 - group_id
            groups.append({
                'name': dialog.name,
                'id': group_id
            })

    # Save the groups to a file
    with open('groups.txt', 'w') as f:
        for group in groups:
            f.write(f"Group Name: {group['name']}, Group ID: {group['id']}, Group Link: https://t.me/c/{abs(group['id'])}\n")

    # Disconnect the client
    await client.disconnect()

# Run the main function
asyncio.run(main())