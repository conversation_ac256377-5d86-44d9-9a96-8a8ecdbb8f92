2025-05-19 12:23:44,637 - INFO - Testing OpenRouter with model meta-llama/llama-3-8b-instruct
2025-05-19 12:23:44,638 - DEBUG - OpenRouter request data: {
  "model": "meta-llama/llama-3-8b-instruct",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:23:44,948 - DEBUG - Sending request to OpenRouter API...
2025-05-19 12:23:44,950 - DEBUG - connect_tcp.started host='openrouter.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:23:44,988 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23cee22bd0>
2025-05-19 12:23:44,988 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f23cea8f750> server_hostname='openrouter.ai' timeout=30
2025-05-19 12:23:45,016 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23cee3fcb0>
2025-05-19 12:23:45,017 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:23:45,021 - DEBUG - send_request_headers.complete
2025-05-19 12:23:45,021 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:23:45,022 - DEBUG - send_request_body.complete
2025-05-19 12:23:45,023 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:23:45,265 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 402, b'Payment Required', [(b'Date', b'Mon, 19 May 2025 10:23:46 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'110'), (b'Connection', b'keep-alive'), (b'Access-Control-Allow-Origin', b'*'), (b'x-clerk-auth-message', b'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)'), (b'x-clerk-auth-reason', b'token-invalid'), (b'x-clerk-auth-status', b'signed-out'), (b'Vary', b'Accept-Encoding'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422e17bdbfc453a-TXL')])
2025-05-19 12:23:45,267 - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 402 Payment Required"
2025-05-19 12:23:45,267 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:23:45,267 - DEBUG - receive_response_body.complete
2025-05-19 12:23:45,268 - DEBUG - response_closed.started
2025-05-19 12:23:45,268 - DEBUG - response_closed.complete
2025-05-19 12:23:45,268 - DEBUG - OpenRouter response status: 402
2025-05-19 12:23:45,269 - DEBUG - OpenRouter response headers: Headers({'date': 'Mon, 19 May 2025 10:23:46 GMT', 'content-type': 'application/json', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'x-clerk-auth-message': 'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)', 'x-clerk-auth-reason': 'token-invalid', 'x-clerk-auth-status': 'signed-out', 'vary': 'Accept-Encoding', 'server': 'cloudflare', 'cf-ray': '9422e17bdbfc453a-TXL'})
2025-05-19 12:23:45,270 - DEBUG - OpenRouter API response: {"error":{"message":"Insufficient credits. Add more using https://openrouter.ai/settings/credits","code":402}}
2025-05-19 12:23:45,270 - ERROR - OpenRouter API returned status 402
2025-05-19 12:23:45,270 - DEBUG - close.started
2025-05-19 12:23:45,271 - DEBUG - close.complete
2025-05-19 12:23:45,271 - INFO - Testing Cerebras with model llama-3.1-8b-instruct
2025-05-19 12:23:45,272 - DEBUG - Cerebras request data: {
  "model": "llama-3.1-8b-instruct",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:23:45,402 - DEBUG - Sending request to Cerebras API...
2025-05-19 12:23:45,402 - DEBUG - connect_tcp.started host='api.cerebras.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:23:45,434 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ceaf7560>
2025-05-19 12:23:45,555 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f23cea8f750> server_hostname='api.cerebras.ai' timeout=30
2025-05-19 12:23:45,581 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ceaf70b0>
2025-05-19 12:23:45,595 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:23:45,597 - DEBUG - send_request_headers.complete
2025-05-19 12:23:45,597 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:23:45,598 - DEBUG - send_request_body.complete
2025-05-19 12:23:45,598 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:23:46,088 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 404, b'Not Found', [(b'Date', b'Mon, 19 May 2025 10:23:47 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'referrer-policy', b'strict-origin-when-cross-origin'), (b'x-content-type-options', b'nosniff'), (b'strict-transport-security', b'max-age=3600; includeSubDomains'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=1unRBkhz8ttdjo1b23kEBahr00uw476ofAt3uwL5pCE-1747650227-*******-ZdF7W0fZH25sjHTSMenEMEmVA38apgTolowKSdHVJWVxcKtjo4ul0L2keRx0SePJXKk05UC5.M6smZhwg8FH.tGXb1QCzJJT1lWekDea9Bg; path=/; expires=Mon, 19-May-25 10:53:47 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422e17f6944e51d-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:23:46,089 - INFO - HTTP Request: POST https://api.cerebras.ai/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-05-19 12:23:46,090 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:23:46,091 - DEBUG - receive_response_body.complete
2025-05-19 12:23:46,091 - DEBUG - response_closed.started
2025-05-19 12:23:46,091 - DEBUG - response_closed.complete
2025-05-19 12:23:46,092 - DEBUG - Cerebras response status: 404
2025-05-19 12:23:46,092 - DEBUG - Cerebras response headers: Headers({'date': 'Mon, 19 May 2025 10:23:47 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-content-type-options': 'nosniff', 'strict-transport-security': 'max-age=3600; includeSubDomains', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=1unRBkhz8ttdjo1b23kEBahr00uw476ofAt3uwL5pCE-1747650227-*******-ZdF7W0fZH25sjHTSMenEMEmVA38apgTolowKSdHVJWVxcKtjo4ul0L2keRx0SePJXKk05UC5.M6smZhwg8FH.tGXb1QCzJJT1lWekDea9Bg; path=/; expires=Mon, 19-May-25 10:53:47 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9422e17f6944e51d-TXL', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
2025-05-19 12:23:46,092 - DEBUG - Cerebras API response: {"message":"Model llama-3.1-8b-instruct does not exist or you do not have access to it.","type":"not_found_error","param":"model","code":"model_not_found"}
2025-05-19 12:23:46,093 - ERROR - Cerebras API returned status 404
2025-05-19 12:23:46,093 - DEBUG - close.started
2025-05-19 12:23:46,093 - DEBUG - close.complete
2025-05-19 12:23:46,094 - INFO - Testing Cohere with model command-r
2025-05-19 12:23:46,094 - DEBUG - Cohere request data: {
  "chat_history": [],
  "message": "Hello, how are you?",
  "model": "command-r",
  "temperature": 0.7,
  "max_tokens": 100,
  "preamble": "You are a helpful assistant."
}
2025-05-19 12:23:46,194 - DEBUG - Sending request to Cohere API...
2025-05-19 12:23:46,195 - DEBUG - connect_tcp.started host='api.cohere.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:23:46,252 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ceb25340>
2025-05-19 12:23:46,272 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f23ceb017d0> server_hostname='api.cohere.ai' timeout=30
2025-05-19 12:23:46,291 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23cea9a3c0>
2025-05-19 12:23:46,292 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:23:46,292 - DEBUG - send_request_headers.complete
2025-05-19 12:23:46,293 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:23:46,293 - DEBUG - send_request_body.complete
2025-05-19 12:23:46,293 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:23:46,694 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'access-control-expose-headers', b'X-Debug-Trace-ID'), (b'cache-control', b'no-cache, no-store, no-transform, must-revalidate, private, max-age=0'), (b'content-type', b'application/json'), (b'expires', b'Thu, 01 Jan 1970 00:00:00 UTC'), (b'num_chars', b'216'), (b'num_tokens', b'39'), (b'pragma', b'no-cache'), (b'vary', b'Origin'), (b'x-accel-expires', b'0'), (b'x-debug-trace-id', b'532939931cfc4760d2a4a61473bb2068'), (b'x-endpoint-monthly-call-limit', b'1000'), (b'x-trial-endpoint-call-limit', b'10'), (b'x-trial-endpoint-call-remaining', b'9'), (b'date', b'Mon, 19 May 2025 10:23:48 GMT'), (b'Content-Length', b'586'), (b'x-envoy-upstream-service-time', b'274'), (b'server', b'envoy'), (b'Via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000')])
2025-05-19 12:23:46,695 - INFO - HTTP Request: POST https://api.cohere.ai/v1/chat "HTTP/1.1 200 OK"
2025-05-19 12:23:46,695 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:23:46,696 - DEBUG - receive_response_body.complete
2025-05-19 12:23:46,696 - DEBUG - response_closed.started
2025-05-19 12:23:46,697 - DEBUG - response_closed.complete
2025-05-19 12:23:46,697 - DEBUG - Cohere response status: 200
2025-05-19 12:23:46,698 - DEBUG - Cohere response headers: Headers({'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 UTC', 'num_chars': '216', 'num_tokens': '39', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': '532939931cfc4760d2a4a61473bb2068', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '9', 'date': 'Mon, 19 May 2025 10:23:48 GMT', 'content-length': '586', 'x-envoy-upstream-service-time': '274', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
2025-05-19 12:23:46,698 - DEBUG - Cohere API response: {"response_id":"943c1ae7-24b6-43f1-9aff-c039137e6739","text":"Hello! I'm doing well, thank you for asking! It's a pleasure to assist you today. How can I help you?","generation_id":"e91f078d-5d43-428a-bb19-115047080b8c","chat_history":[{"role":"USER","message":"Hello, how are you?"},{"role":"CHATBOT","message":"Hello! I'm doing well, thank you for asking! It's a pleasure to assist you today. How can I help you?"}],"finish_reason":"COMPLETE","meta":{"api_version":{"version":"1"},"billed_units":{"input_tokens":12,"output_tokens":27},"tokens":{"input_tokens":21,"output_tokens":27}}}
2025-05-19 12:23:46,699 - DEBUG - Parsed JSON response: {
  "response_id": "943c1ae7-24b6-43f1-9aff-c039137e6739",
  "text": "Hello! I'm doing well, thank you for asking! It's a pleasure to assist you today. How can I help you?",
  "generation_id": "e91f078d-5d43-428a-bb19-115047080b8c",
  "chat_history": [
    {
      "role": "USER",
      "message": "Hello, how are you?"
    },
    {
      "role": "CHATBOT",
      "message": "Hello! I'm doing well, thank you for asking! It's a pleasure to assist you today. How can I help you?"
    }
  ],
  "finish_reason": "COMPLETE",
  "meta": {
    "api_version": {
      "version": "1"
    },
    "billed_units": {
      "input_tokens": 12,
      "output_tokens": 27
    },
    "tokens": {
      "input_tokens": 21,
      "output_tokens": 27
    }
  }
}
2025-05-19 12:23:46,699 - DEBUG - close.started
2025-05-19 12:23:46,700 - DEBUG - close.complete
2025-05-19 12:23:46,700 - INFO - Testing Mistral with model mistral-small-latest
2025-05-19 12:23:46,701 - DEBUG - Mistral request data: {
  "model": "mistral-small-latest",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:23:46,836 - DEBUG - Sending request to Mistral API...
2025-05-19 12:23:46,837 - DEBUG - connect_tcp.started host='api.mistral.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:23:46,878 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ceb26db0>
2025-05-19 12:23:46,879 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f23cfaae450> server_hostname='api.mistral.ai' timeout=30
2025-05-19 12:23:46,908 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ceb26cc0>
2025-05-19 12:23:46,908 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:23:46,909 - DEBUG - send_request_headers.complete
2025-05-19 12:23:46,910 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:23:46,911 - DEBUG - send_request_body.complete
2025-05-19 12:23:46,911 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:23:47,187 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 19 May 2025 10:23:48 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'x-ratelimitbysize-limit-minute', b'500000'), (b'ratelimitbysize-reset', b'12'), (b'x-ratelimitbysize-limit-month', b'1000000000'), (b'ratelimitbysize-remaining', b'499887'), (b'ratelimitbysize-query-cost', b'113'), (b'x-ratelimitbysize-remaining-month', b'988022844'), (b'x-ratelimitbysize-remaining-minute', b'499887'), (b'ratelimitbysize-limit', b'500000'), (b'x-envoy-upstream-service-time', b'222'), (b'access-control-allow-origin', b'*'), (b'x-kong-upstream-latency', b'223'), (b'x-kong-proxy-latency', b'6'), (b'x-kong-request-id', b'7122dd3a410cf51055fdc307f20cb803'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=Rz7gdmggKdtvz6yaod_aaUusu7fag.S9N2taPoYpPAE-1747650228-*******-XoZHBfIb9JxZ5oSG8Jxm4ZKtFKNui6Jrn9A8oio2_p7wkebGQkFUuku0.Nu1xuBGbikdy4Ik03LSL7ObV4D0WQQB3vZ1TuvNErbNAm_96rg; path=/; expires=Mon, 19-May-25 10:53:48 GMT; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), (b'Set-Cookie', b'_cfuvid=UUSCFN_WqWylCtAQjCn7xHxA.u7.X0XPip9akJwN3Mo-1747650228687-*******-604800000; path=/; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422e187aeabe506-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:23:47,188 - INFO - HTTP Request: POST https://api.mistral.ai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-19 12:23:47,188 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:23:47,189 - DEBUG - receive_response_body.complete
2025-05-19 12:23:47,190 - DEBUG - response_closed.started
2025-05-19 12:23:47,191 - DEBUG - response_closed.complete
2025-05-19 12:23:47,191 - DEBUG - Mistral response status: 200
2025-05-19 12:23:47,191 - DEBUG - Mistral response headers: Headers([('date', 'Mon, 19 May 2025 10:23:48 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('x-ratelimitbysize-limit-minute', '500000'), ('ratelimitbysize-reset', '12'), ('x-ratelimitbysize-limit-month', '1000000000'), ('ratelimitbysize-remaining', '499887'), ('ratelimitbysize-query-cost', '113'), ('x-ratelimitbysize-remaining-month', '988022844'), ('x-ratelimitbysize-remaining-minute', '499887'), ('ratelimitbysize-limit', '500000'), ('x-envoy-upstream-service-time', '222'), ('access-control-allow-origin', '*'), ('x-kong-upstream-latency', '223'), ('x-kong-proxy-latency', '6'), ('x-kong-request-id', '7122dd3a410cf51055fdc307f20cb803'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=Rz7gdmggKdtvz6yaod_aaUusu7fag.S9N2taPoYpPAE-1747650228-*******-XoZHBfIb9JxZ5oSG8Jxm4ZKtFKNui6Jrn9A8oio2_p7wkebGQkFUuku0.Nu1xuBGbikdy4Ik03LSL7ObV4D0WQQB3vZ1TuvNErbNAm_96rg; path=/; expires=Mon, 19-May-25 10:53:48 GMT; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), ('set-cookie', '_cfuvid=UUSCFN_WqWylCtAQjCn7xHxA.u7.X0XPip9akJwN3Mo-1747650228687-*******-604800000; path=/; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9422e187aeabe506-TXL'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-05-19 12:23:47,192 - DEBUG - Mistral API response: {"id":"8af9b877cd9b4aa28fbcc5587f8e3b9b","object":"chat.completion","created":1747650228,"model":"mistral-small-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Hello! I'm functioning as intended, thank you. I'm here to help. How can I assist you today?"},"finish_reason":"stop","logprobs":null}],"usage":{"prompt_tokens":17,"total_tokens":42,"completion_tokens":25}}
2025-05-19 12:23:47,192 - DEBUG - Parsed JSON response: {
  "id": "8af9b877cd9b4aa28fbcc5587f8e3b9b",
  "object": "chat.completion",
  "created": 1747650228,
  "model": "mistral-small-latest",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "tool_calls": null,
        "content": "Hello! I'm functioning as intended, thank you. I'm here to help. How can I assist you today?"
      },
      "finish_reason": "stop",
      "logprobs": null
    }
  ],
  "usage": {
    "prompt_tokens": 17,
    "total_tokens": 42,
    "completion_tokens": 25
  }
}
2025-05-19 12:23:47,192 - DEBUG - close.started
2025-05-19 12:23:47,193 - DEBUG - close.complete
2025-05-19 12:23:47,193 - INFO - Testing Codestral with model codestral-latest
2025-05-19 12:23:47,194 - DEBUG - Codestral request data: {
  "model": "codestral-latest",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:23:47,308 - DEBUG - Sending request to Codestral API...
2025-05-19 12:23:47,309 - DEBUG - connect_tcp.started host='api.cerebras.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:23:47,331 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ce938800>
2025-05-19 12:23:47,332 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f23cfaae850> server_hostname='api.cerebras.ai' timeout=30
2025-05-19 12:23:47,350 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f23ce938710>
2025-05-19 12:23:47,351 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:23:47,352 - DEBUG - send_request_headers.complete
2025-05-19 12:23:47,352 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:23:47,353 - DEBUG - send_request_body.complete
2025-05-19 12:23:47,353 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:23:47,536 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 401, b'Unauthorized', [(b'Date', b'Mon, 19 May 2025 10:23:49 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'99'), (b'Connection', b'keep-alive'), (b'referrer-policy', b'strict-origin-when-cross-origin'), (b'x-content-type-options', b'nosniff'), (b'strict-transport-security', b'max-age=3600; includeSubDomains'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=mMuAFcudbTCikZaYLF92Ah4btgnAftgePTG0Yz6SYyw-1747650229-*******-hpzZa2Tg1sMgZfcqh5q4wD5G1jR_0l92cGt0tsXwERk6MQ8nwNnYcq__N8ArVTzi4Ggl37tHG63I1_IwMDhjDYd6Wd4KuTc4tgWAknROc_c; path=/; expires=Mon, 19-May-25 10:53:49 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422e18a6d1de519-TXL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:23:47,538 - INFO - HTTP Request: POST https://api.cerebras.ai/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-05-19 12:23:47,538 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:23:47,539 - DEBUG - receive_response_body.complete
2025-05-19 12:23:47,539 - DEBUG - response_closed.started
2025-05-19 12:23:47,539 - DEBUG - response_closed.complete
2025-05-19 12:23:47,539 - DEBUG - Codestral response status: 401
2025-05-19 12:23:47,540 - DEBUG - Codestral response headers: Headers({'date': 'Mon, 19 May 2025 10:23:49 GMT', 'content-type': 'application/json', 'content-length': '99', 'connection': 'keep-alive', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-content-type-options': 'nosniff', 'strict-transport-security': 'max-age=3600; includeSubDomains', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=mMuAFcudbTCikZaYLF92Ah4btgnAftgePTG0Yz6SYyw-1747650229-*******-hpzZa2Tg1sMgZfcqh5q4wD5G1jR_0l92cGt0tsXwERk6MQ8nwNnYcq__N8ArVTzi4Ggl37tHG63I1_IwMDhjDYd6Wd4KuTc4tgWAknROc_c; path=/; expires=Mon, 19-May-25 10:53:49 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9422e18a6d1de519-TXL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-05-19 12:23:47,540 - DEBUG - Codestral API response: {"message":"Wrong API Key","type":"invalid_request_error","param":"api_key","code":"wrong_api_key"}
2025-05-19 12:23:47,540 - ERROR - Codestral API returned status 401
2025-05-19 12:23:47,541 - DEBUG - close.started
2025-05-19 12:23:47,541 - DEBUG - close.complete
2025-05-19 12:35:45,693 - INFO - Testing OpenRouter with model meta-llama/llama-3-8b-instruct
2025-05-19 12:35:45,694 - DEBUG - OpenRouter request data: {
  "model": "meta-llama/llama-3-8b-instruct",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:35:46,170 - DEBUG - Sending request to OpenRouter API...
2025-05-19 12:35:46,171 - DEBUG - connect_tcp.started host='openrouter.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:35:46,209 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6b6f00>
2025-05-19 12:35:46,209 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fcd9c64f750> server_hostname='openrouter.ai' timeout=30
2025-05-19 12:35:46,233 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c8acce0>
2025-05-19 12:35:46,234 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:35:46,234 - DEBUG - send_request_headers.complete
2025-05-19 12:35:46,235 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:35:46,236 - DEBUG - send_request_body.complete
2025-05-19 12:35:46,238 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:35:46,529 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 402, b'Payment Required', [(b'Date', b'Mon, 19 May 2025 10:35:48 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'110'), (b'Connection', b'keep-alive'), (b'Access-Control-Allow-Origin', b'*'), (b'x-clerk-auth-message', b'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)'), (b'x-clerk-auth-reason', b'token-invalid'), (b'x-clerk-auth-status', b'signed-out'), (b'Vary', b'Accept-Encoding'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f3176ab044f8-TXL')])
2025-05-19 12:35:46,573 - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 402 Payment Required"
2025-05-19 12:35:46,574 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:35:46,574 - DEBUG - receive_response_body.complete
2025-05-19 12:35:46,574 - DEBUG - response_closed.started
2025-05-19 12:35:46,575 - DEBUG - response_closed.complete
2025-05-19 12:35:46,575 - DEBUG - OpenRouter response status: 402
2025-05-19 12:35:46,580 - DEBUG - OpenRouter response headers: Headers({'date': 'Mon, 19 May 2025 10:35:48 GMT', 'content-type': 'application/json', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'x-clerk-auth-message': 'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)', 'x-clerk-auth-reason': 'token-invalid', 'x-clerk-auth-status': 'signed-out', 'vary': 'Accept-Encoding', 'server': 'cloudflare', 'cf-ray': '9422f3176ab044f8-TXL'})
2025-05-19 12:35:46,580 - DEBUG - OpenRouter API response: {"error":{"message":"Insufficient credits. Add more using https://openrouter.ai/settings/credits","code":402}}
2025-05-19 12:35:46,580 - ERROR - OpenRouter API returned status 402
2025-05-19 12:35:46,582 - DEBUG - close.started
2025-05-19 12:35:46,582 - DEBUG - close.complete
2025-05-19 12:35:46,583 - INFO - Testing Cerebras with model llama-3.1-8b
2025-05-19 12:35:46,583 - DEBUG - Cerebras request data: {
  "model": "llama-3.1-8b",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:35:46,706 - DEBUG - Sending request to Cerebras API...
2025-05-19 12:35:46,707 - DEBUG - connect_tcp.started host='api.cerebras.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:35:46,748 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6b5e20>
2025-05-19 12:35:46,749 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fcd9c64f750> server_hostname='api.cerebras.ai' timeout=30
2025-05-19 12:35:46,773 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6b74a0>
2025-05-19 12:35:46,774 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:35:46,774 - DEBUG - send_request_headers.complete
2025-05-19 12:35:46,775 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:35:46,775 - DEBUG - send_request_body.complete
2025-05-19 12:35:46,779 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:35:47,101 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 19 May 2025 10:35:48 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'x-request-id', b'9422f31adf92e52f-TXL'), (b'x-ratelimit-limit-requests-day', b'14400'), (b'x-ratelimit-limit-tokens-minute', b'60000'), (b'x-ratelimit-remaining-requests-day', b'14400'), (b'x-ratelimit-remaining-tokens-minute', b'60000'), (b'x-ratelimit-reset-requests-day', b'48251.496599435806'), (b'x-ratelimit-reset-tokens-minute', b'11.496599435806274'), (b'inference-id', b'chatcmpl-e02ca3ff-f9fd-41d2-9259-6a7fa65618e8'), (b'referrer-policy', b'strict-origin-when-cross-origin'), (b'x-content-type-options', b'nosniff'), (b'strict-transport-security', b'max-age=3600; includeSubDomains'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=jd.ONupgS3boXRvX0P61WtURZkrLTRfJCnRfW2GMn2Q-1747650948-*******-vdB3leFr2wlbjBgsQptt_6jSdOW337ujlD22.cnL6CEijBEZPMtZog0U6qM95MQv2n_wcJZr2zEf96GdVpJyrM1xWljHDXcvlYB1_iLvZs4; path=/; expires=Mon, 19-May-25 11:05:48 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f31adf92e52f-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:35:47,101 - INFO - HTTP Request: POST https://api.cerebras.ai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-19 12:35:47,102 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:35:47,103 - DEBUG - receive_response_body.complete
2025-05-19 12:35:47,103 - DEBUG - response_closed.started
2025-05-19 12:35:47,104 - DEBUG - response_closed.complete
2025-05-19 12:35:47,104 - DEBUG - Cerebras response status: 200
2025-05-19 12:35:47,104 - DEBUG - Cerebras response headers: Headers({'date': 'Mon, 19 May 2025 10:35:48 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'x-request-id': '9422f31adf92e52f-TXL', 'x-ratelimit-limit-requests-day': '14400', 'x-ratelimit-limit-tokens-minute': '60000', 'x-ratelimit-remaining-requests-day': '14400', 'x-ratelimit-remaining-tokens-minute': '60000', 'x-ratelimit-reset-requests-day': '48251.496599435806', 'x-ratelimit-reset-tokens-minute': '11.496599435806274', 'inference-id': 'chatcmpl-e02ca3ff-f9fd-41d2-9259-6a7fa65618e8', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-content-type-options': 'nosniff', 'strict-transport-security': 'max-age=3600; includeSubDomains', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=jd.ONupgS3boXRvX0P61WtURZkrLTRfJCnRfW2GMn2Q-1747650948-*******-vdB3leFr2wlbjBgsQptt_6jSdOW337ujlD22.cnL6CEijBEZPMtZog0U6qM95MQv2n_wcJZr2zEf96GdVpJyrM1xWljHDXcvlYB1_iLvZs4; path=/; expires=Mon, 19-May-25 11:05:48 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9422f31adf92e52f-TXL', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
2025-05-19 12:35:47,105 - DEBUG - Cerebras API response: {"id":"chatcmpl-e02ca3ff-f9fd-41d2-9259-6a7fa65618e8","choices":[{"finish_reason":"stop","index":0,"message":{"content":"I'm functioning properly, thank you for asking. I'm here to assist you with any questions, tasks, or information you need. How can I help you today?","role":"assistant"}}],"created":1747650948,"model":"llama3.1-8b","system_fingerprint":"fp_fd530e96020e4e77c6b2","object":"chat.completion","usage":{"prompt_tokens":47,"completion_tokens":35,"total_tokens":82},"time_info":{"queue_time":0.00021753,"prompt_time":0.002441082,"completion_time":0.016099014,"total_time":0.021010637283325195,"created":1747650948}}
2025-05-19 12:35:47,105 - DEBUG - Parsed JSON response: {
  "id": "chatcmpl-e02ca3ff-f9fd-41d2-9259-6a7fa65618e8",
  "choices": [
    {
      "finish_reason": "stop",
      "index": 0,
      "message": {
        "content": "I'm functioning properly, thank you for asking. I'm here to assist you with any questions, tasks, or information you need. How can I help you today?",
        "role": "assistant"
      }
    }
  ],
  "created": 1747650948,
  "model": "llama3.1-8b",
  "system_fingerprint": "fp_fd530e96020e4e77c6b2",
  "object": "chat.completion",
  "usage": {
    "prompt_tokens": 47,
    "completion_tokens": 35,
    "total_tokens": 82
  },
  "time_info": {
    "queue_time": 0.00021753,
    "prompt_time": 0.002441082,
    "completion_time": 0.016099014,
    "total_time": 0.021010637283325195,
    "created": 1747650948
  }
}
2025-05-19 12:35:47,105 - DEBUG - close.started
2025-05-19 12:35:47,106 - DEBUG - close.complete
2025-05-19 12:35:47,106 - INFO - Testing Cohere with model command-r
2025-05-19 12:35:47,108 - DEBUG - Cohere request data: {
  "chat_history": [],
  "message": "Hello, how are you?",
  "model": "command-r",
  "temperature": 0.7,
  "max_tokens": 100,
  "preamble": "You are a helpful assistant."
}
2025-05-19 12:35:47,234 - DEBUG - Sending request to Cohere API...
2025-05-19 12:35:47,235 - DEBUG - connect_tcp.started host='api.cohere.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:35:47,273 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6e5a00>
2025-05-19 12:35:47,285 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fcd9c6c1e50> server_hostname='api.cohere.ai' timeout=30
2025-05-19 12:35:47,306 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6e5910>
2025-05-19 12:35:47,307 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:35:47,308 - DEBUG - send_request_headers.complete
2025-05-19 12:35:47,308 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:35:47,309 - DEBUG - send_request_body.complete
2025-05-19 12:35:47,309 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:35:47,909 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'access-control-expose-headers', b'X-Debug-Trace-ID'), (b'cache-control', b'no-cache, no-store, no-transform, must-revalidate, private, max-age=0'), (b'content-type', b'application/json'), (b'expires', b'Thu, 01 Jan 1970 00:00:00 UTC'), (b'num_chars', b'216'), (b'num_tokens', b'61'), (b'pragma', b'no-cache'), (b'vary', b'Origin'), (b'x-accel-expires', b'0'), (b'x-debug-trace-id', b'e1fefca1b973ae819da0619b165aedb7'), (b'x-endpoint-monthly-call-limit', b'1000'), (b'x-trial-endpoint-call-limit', b'10'), (b'x-trial-endpoint-call-remaining', b'9'), (b'date', b'Mon, 19 May 2025 10:35:49 GMT'), (b'Content-Length', b'800'), (b'x-envoy-upstream-service-time', b'467'), (b'server', b'envoy'), (b'Via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000')])
2025-05-19 12:35:47,911 - INFO - HTTP Request: POST https://api.cohere.ai/v1/chat "HTTP/1.1 200 OK"
2025-05-19 12:35:47,911 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:35:47,912 - DEBUG - receive_response_body.complete
2025-05-19 12:35:47,913 - DEBUG - response_closed.started
2025-05-19 12:35:47,914 - DEBUG - response_closed.complete
2025-05-19 12:35:47,915 - DEBUG - Cohere response status: 200
2025-05-19 12:35:47,921 - DEBUG - Cohere response headers: Headers({'access-control-expose-headers': 'X-Debug-Trace-ID', 'cache-control': 'no-cache, no-store, no-transform, must-revalidate, private, max-age=0', 'content-type': 'application/json', 'expires': 'Thu, 01 Jan 1970 00:00:00 UTC', 'num_chars': '216', 'num_tokens': '61', 'pragma': 'no-cache', 'vary': 'Origin', 'x-accel-expires': '0', 'x-debug-trace-id': 'e1fefca1b973ae819da0619b165aedb7', 'x-endpoint-monthly-call-limit': '1000', 'x-trial-endpoint-call-limit': '10', 'x-trial-endpoint-call-remaining': '9', 'date': 'Mon, 19 May 2025 10:35:49 GMT', 'content-length': '800', 'x-envoy-upstream-service-time': '467', 'server': 'envoy', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000'})
2025-05-19 12:35:47,921 - DEBUG - Cohere API response: {"response_id":"074e7f65-568e-4459-b0da-a61b1fd86230","text":"Hello! I'm doing well, thank you for asking! It's a pleasure to assist you. How can I help you today? Whether it's answering questions, providing information, or just having a friendly chat, I'm here for you!","generation_id":"c89d3a1f-89da-461c-b096-b01b138a4de5","chat_history":[{"role":"USER","message":"Hello, how are you?"},{"role":"CHATBOT","message":"Hello! I'm doing well, thank you for asking! It's a pleasure to assist you. How can I help you today? Whether it's answering questions, providing information, or just having a friendly chat, I'm here for you!"}],"finish_reason":"COMPLETE","meta":{"api_version":{"version":"1"},"billed_units":{"input_tokens":12,"output_tokens":49},"tokens":{"input_tokens":21,"output_tokens":49}}}
2025-05-19 12:35:47,923 - DEBUG - Parsed JSON response: {
  "response_id": "074e7f65-568e-4459-b0da-a61b1fd86230",
  "text": "Hello! I'm doing well, thank you for asking! It's a pleasure to assist you. How can I help you today? Whether it's answering questions, providing information, or just having a friendly chat, I'm here for you!",
  "generation_id": "c89d3a1f-89da-461c-b096-b01b138a4de5",
  "chat_history": [
    {
      "role": "USER",
      "message": "Hello, how are you?"
    },
    {
      "role": "CHATBOT",
      "message": "Hello! I'm doing well, thank you for asking! It's a pleasure to assist you. How can I help you today? Whether it's answering questions, providing information, or just having a friendly chat, I'm here for you!"
    }
  ],
  "finish_reason": "COMPLETE",
  "meta": {
    "api_version": {
      "version": "1"
    },
    "billed_units": {
      "input_tokens": 12,
      "output_tokens": 49
    },
    "tokens": {
      "input_tokens": 21,
      "output_tokens": 49
    }
  }
}
2025-05-19 12:35:47,924 - DEBUG - close.started
2025-05-19 12:35:47,925 - DEBUG - close.complete
2025-05-19 12:35:47,926 - INFO - Testing Mistral with model mistral-small-latest
2025-05-19 12:35:47,928 - DEBUG - Mistral request data: {
  "model": "mistral-small-latest",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:35:48,069 - DEBUG - Sending request to Mistral API...
2025-05-19 12:35:48,070 - DEBUG - connect_tcp.started host='api.mistral.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:35:48,101 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6e74d0>
2025-05-19 12:35:48,109 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fcd9c6c1850> server_hostname='api.mistral.ai' timeout=30
2025-05-19 12:35:48,144 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c6e73e0>
2025-05-19 12:35:48,144 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:35:48,145 - DEBUG - send_request_headers.complete
2025-05-19 12:35:48,145 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:35:48,145 - DEBUG - send_request_body.complete
2025-05-19 12:35:48,146 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:35:48,632 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 19 May 2025 10:35:50 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'ratelimitbysize-reset', b'11'), (b'x-ratelimitbysize-remaining-minute', b'499887'), (b'ratelimitbysize-limit', b'500000'), (b'x-ratelimitbysize-limit-minute', b'500000'), (b'ratelimitbysize-query-cost', b'113'), (b'x-ratelimitbysize-remaining-month', b'988732469'), (b'ratelimitbysize-remaining', b'499887'), (b'x-ratelimitbysize-limit-month', b'1000000000'), (b'x-envoy-upstream-service-time', b'421'), (b'access-control-allow-origin', b'*'), (b'x-kong-upstream-latency', b'422'), (b'x-kong-proxy-latency', b'5'), (b'x-kong-request-id', b'e46a3156c20d9633feddd6465132cd55'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=uipA3fTHvuKlW_CTQNVvJGHTppRKF66fa60crAeIiG4-1747650950-*******-jSJuw.B5lJYvZr992rXmMrjseL5H9gNMPOmobuwSX8wxNQ5gX.IXrhG6_0DeqMitMPRaZ8sUF15nbvEeZJuDHdXVr8SPN_m7NArp5P54ATg; path=/; expires=Mon, 19-May-25 11:05:50 GMT; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), (b'Set-Cookie', b'_cfuvid=_ctAUOlmz2yZgEVGlPUGz_ZO9OWgqmPCxtkeUu72nLo-1747650950136-*******-604800000; path=/; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f3236ba44541-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:35:48,634 - INFO - HTTP Request: POST https://api.mistral.ai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-19 12:35:48,635 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:35:48,638 - DEBUG - receive_response_body.complete
2025-05-19 12:35:48,640 - DEBUG - response_closed.started
2025-05-19 12:35:48,641 - DEBUG - response_closed.complete
2025-05-19 12:35:48,641 - DEBUG - Mistral response status: 200
2025-05-19 12:35:48,642 - DEBUG - Mistral response headers: Headers([('date', 'Mon, 19 May 2025 10:35:50 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('ratelimitbysize-reset', '11'), ('x-ratelimitbysize-remaining-minute', '499887'), ('ratelimitbysize-limit', '500000'), ('x-ratelimitbysize-limit-minute', '500000'), ('ratelimitbysize-query-cost', '113'), ('x-ratelimitbysize-remaining-month', '988732469'), ('ratelimitbysize-remaining', '499887'), ('x-ratelimitbysize-limit-month', '1000000000'), ('x-envoy-upstream-service-time', '421'), ('access-control-allow-origin', '*'), ('x-kong-upstream-latency', '422'), ('x-kong-proxy-latency', '5'), ('x-kong-request-id', 'e46a3156c20d9633feddd6465132cd55'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=uipA3fTHvuKlW_CTQNVvJGHTppRKF66fa60crAeIiG4-1747650950-*******-jSJuw.B5lJYvZr992rXmMrjseL5H9gNMPOmobuwSX8wxNQ5gX.IXrhG6_0DeqMitMPRaZ8sUF15nbvEeZJuDHdXVr8SPN_m7NArp5P54ATg; path=/; expires=Mon, 19-May-25 11:05:50 GMT; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), ('set-cookie', '_cfuvid=_ctAUOlmz2yZgEVGlPUGz_ZO9OWgqmPCxtkeUu72nLo-1747650950136-*******-604800000; path=/; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9422f3236ba44541-TXL'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-05-19 12:35:48,643 - DEBUG - Mistral API response: {"id":"2d8b292828c446cd8d0ba9b241db6743","object":"chat.completion","created":1747650949,"model":"mistral-small-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Hello! I'm functioning well, thank you. I'm here and ready to assist you. How can I help you today?"},"finish_reason":"stop","logprobs":null}],"usage":{"prompt_tokens":17,"total_tokens":44,"completion_tokens":27}}
2025-05-19 12:35:48,644 - DEBUG - Parsed JSON response: {
  "id": "2d8b292828c446cd8d0ba9b241db6743",
  "object": "chat.completion",
  "created": 1747650949,
  "model": "mistral-small-latest",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "tool_calls": null,
        "content": "Hello! I'm functioning well, thank you. I'm here and ready to assist you. How can I help you today?"
      },
      "finish_reason": "stop",
      "logprobs": null
    }
  ],
  "usage": {
    "prompt_tokens": 17,
    "total_tokens": 44,
    "completion_tokens": 27
  }
}
2025-05-19 12:35:48,647 - DEBUG - close.started
2025-05-19 12:35:48,648 - DEBUG - close.complete
2025-05-19 12:35:48,650 - INFO - Testing Codestral with model codestral-latest
2025-05-19 12:35:48,650 - DEBUG - Codestral request data: {
  "model": "codestral-latest",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:35:48,751 - DEBUG - Sending request to Codestral API...
2025-05-19 12:35:48,752 - DEBUG - connect_tcp.started host='api.cerebras.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:35:48,772 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c4f8f20>
2025-05-19 12:35:48,772 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7fcd9c6c27d0> server_hostname='api.cerebras.ai' timeout=30
2025-05-19 12:35:48,827 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fcd9c4f8e30>
2025-05-19 12:35:48,828 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:35:48,829 - DEBUG - send_request_headers.complete
2025-05-19 12:35:48,829 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:35:48,830 - DEBUG - send_request_body.complete
2025-05-19 12:35:48,831 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:35:49,369 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 401, b'Unauthorized', [(b'Date', b'Mon, 19 May 2025 10:35:50 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'99'), (b'Connection', b'keep-alive'), (b'referrer-policy', b'strict-origin-when-cross-origin'), (b'x-content-type-options', b'nosniff'), (b'strict-transport-security', b'max-age=3600; includeSubDomains'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=YGK6_uNWmknVonXluj0Gzr7vAtiQhsKC5ZX4We5hwns-1747650950-*******-2GDj6lAPpZ.A1LjaiziIBEiL3HUVFHUgJvkFGWf033wX_7K8lRAlSl9ouHyhMX4koVux690kZQiLFbmTDceTuQOzFq522SpHpoEuHFSp3qg; path=/; expires=Mon, 19-May-25 11:05:50 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f327ac87e505-TXL'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:35:49,370 - INFO - HTTP Request: POST https://api.cerebras.ai/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-05-19 12:35:49,370 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:35:49,371 - DEBUG - receive_response_body.complete
2025-05-19 12:35:49,371 - DEBUG - response_closed.started
2025-05-19 12:35:49,371 - DEBUG - response_closed.complete
2025-05-19 12:35:49,372 - DEBUG - Codestral response status: 401
2025-05-19 12:35:49,372 - DEBUG - Codestral response headers: Headers({'date': 'Mon, 19 May 2025 10:35:50 GMT', 'content-type': 'application/json', 'content-length': '99', 'connection': 'keep-alive', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-content-type-options': 'nosniff', 'strict-transport-security': 'max-age=3600; includeSubDomains', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=YGK6_uNWmknVonXluj0Gzr7vAtiQhsKC5ZX4We5hwns-1747650950-*******-2GDj6lAPpZ.A1LjaiziIBEiL3HUVFHUgJvkFGWf033wX_7K8lRAlSl9ouHyhMX4koVux690kZQiLFbmTDceTuQOzFq522SpHpoEuHFSp3qg; path=/; expires=Mon, 19-May-25 11:05:50 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9422f327ac87e505-TXL', 'alt-svc': 'h3=":443"; ma=86400'})
2025-05-19 12:35:49,373 - DEBUG - Codestral API response: {"message":"Wrong API Key","type":"invalid_request_error","param":"api_key","code":"wrong_api_key"}
2025-05-19 12:35:49,373 - ERROR - Codestral API returned status 401
2025-05-19 12:35:49,374 - DEBUG - close.started
2025-05-19 12:35:49,374 - DEBUG - close.complete
2025-05-19 12:38:00,994 - INFO - Testing OpenRouter with model meta-llama/llama-3-8b-instruct
2025-05-19 12:38:00,994 - DEBUG - OpenRouter request data: {
  "model": "meta-llama/llama-3-8b-instruct",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:38:01,342 - DEBUG - Sending request to OpenRouter API...
2025-05-19 12:38:01,345 - DEBUG - connect_tcp.started host='openrouter.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:38:01,381 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b26cf0>
2025-05-19 12:38:01,381 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f7dd1abf750> server_hostname='openrouter.ai' timeout=30
2025-05-19 12:38:01,410 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd2d331a0>
2025-05-19 12:38:01,411 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:38:01,412 - DEBUG - send_request_headers.complete
2025-05-19 12:38:01,412 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:38:01,412 - DEBUG - send_request_body.complete
2025-05-19 12:38:01,413 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:38:01,635 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 402, b'Payment Required', [(b'Date', b'Mon, 19 May 2025 10:38:03 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'110'), (b'Connection', b'keep-alive'), (b'Access-Control-Allow-Origin', b'*'), (b'x-clerk-auth-message', b'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)'), (b'x-clerk-auth-reason', b'token-invalid'), (b'x-clerk-auth-status', b'signed-out'), (b'Vary', b'Accept-Encoding'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f6644d79e525-TXL')])
2025-05-19 12:38:01,641 - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 402 Payment Required"
2025-05-19 12:38:01,642 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:38:01,688 - DEBUG - receive_response_body.complete
2025-05-19 12:38:01,688 - DEBUG - response_closed.started
2025-05-19 12:38:01,690 - DEBUG - response_closed.complete
2025-05-19 12:38:01,690 - DEBUG - OpenRouter response status: 402
2025-05-19 12:38:01,691 - DEBUG - OpenRouter response headers: Headers({'date': 'Mon, 19 May 2025 10:38:03 GMT', 'content-type': 'application/json', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'x-clerk-auth-message': 'Invalid JWT form. A JWT consists of three parts separated by dots. (reason=token-invalid, token-carrier=header)', 'x-clerk-auth-reason': 'token-invalid', 'x-clerk-auth-status': 'signed-out', 'vary': 'Accept-Encoding', 'server': 'cloudflare', 'cf-ray': '9422f6644d79e525-TXL'})
2025-05-19 12:38:01,691 - DEBUG - OpenRouter API response: {"error":{"message":"Insufficient credits. Add more using https://openrouter.ai/settings/credits","code":402}}
2025-05-19 12:38:01,692 - ERROR - OpenRouter API returned status 402
2025-05-19 12:38:01,692 - DEBUG - close.started
2025-05-19 12:38:01,693 - DEBUG - close.complete
2025-05-19 12:38:01,693 - INFO - Testing Cerebras with model llama-3.1-8b
2025-05-19 12:38:01,694 - DEBUG - Cerebras request data: {
  "model": "llama-3.1-8b",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:38:01,905 - DEBUG - Sending request to Cerebras API...
2025-05-19 12:38:01,908 - DEBUG - connect_tcp.started host='api.cerebras.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:38:01,969 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b26db0>
2025-05-19 12:38:01,969 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f7dd1abf750> server_hostname='api.cerebras.ai' timeout=30
2025-05-19 12:38:01,995 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b27170>
2025-05-19 12:38:01,995 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:38:01,999 - DEBUG - send_request_headers.complete
2025-05-19 12:38:02,002 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:38:02,003 - DEBUG - send_request_body.complete
2025-05-19 12:38:02,003 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:38:02,324 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 19 May 2025 10:38:03 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'x-request-id', b'9422f667fe6ce505-TXL'), (b'x-ratelimit-limit-requests-day', b'14400'), (b'x-ratelimit-limit-tokens-minute', b'60000'), (b'x-ratelimit-remaining-requests-day', b'14399'), (b'x-ratelimit-remaining-tokens-minute', b'60000'), (b'x-ratelimit-reset-requests-day', b'48116.29392004013'), (b'x-ratelimit-reset-tokens-minute', b'56.293920040130615'), (b'inference-id', b'chatcmpl-c4658203-acbb-4018-9fdf-1a454ac47413'), (b'referrer-policy', b'strict-origin-when-cross-origin'), (b'x-content-type-options', b'nosniff'), (b'strict-transport-security', b'max-age=3600; includeSubDomains'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=0z3sXFxKtuPXgol7y7P_alK3puosqAPejBg1FOUTF.g-1747651083-*******-j4caLf798_nQwOzan7se9wUQUnHdM8iSBr8SavtgJ2F7FDRjS5L8.aOYwD02ZS883lAJRbssjFMchxbwuOl_4bzC47cgJ8nL1iKk0JcTg0Q; path=/; expires=Mon, 19-May-25 11:08:03 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f667fe6ce505-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:38:02,363 - INFO - HTTP Request: POST https://api.cerebras.ai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-19 12:38:02,363 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:38:02,364 - DEBUG - receive_response_body.complete
2025-05-19 12:38:02,365 - DEBUG - response_closed.started
2025-05-19 12:38:02,365 - DEBUG - response_closed.complete
2025-05-19 12:38:02,365 - DEBUG - Cerebras response status: 200
2025-05-19 12:38:02,366 - DEBUG - Cerebras response headers: Headers({'date': 'Mon, 19 May 2025 10:38:03 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'x-request-id': '9422f667fe6ce505-TXL', 'x-ratelimit-limit-requests-day': '14400', 'x-ratelimit-limit-tokens-minute': '60000', 'x-ratelimit-remaining-requests-day': '14399', 'x-ratelimit-remaining-tokens-minute': '60000', 'x-ratelimit-reset-requests-day': '48116.29392004013', 'x-ratelimit-reset-tokens-minute': '56.293920040130615', 'inference-id': 'chatcmpl-c4658203-acbb-4018-9fdf-1a454ac47413', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-content-type-options': 'nosniff', 'strict-transport-security': 'max-age=3600; includeSubDomains', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=0z3sXFxKtuPXgol7y7P_alK3puosqAPejBg1FOUTF.g-1747651083-*******-j4caLf798_nQwOzan7se9wUQUnHdM8iSBr8SavtgJ2F7FDRjS5L8.aOYwD02ZS883lAJRbssjFMchxbwuOl_4bzC47cgJ8nL1iKk0JcTg0Q; path=/; expires=Mon, 19-May-25 11:08:03 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9422f667fe6ce505-TXL', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
2025-05-19 12:38:02,366 - DEBUG - Cerebras API response: {"id":"chatcmpl-c4658203-acbb-4018-9fdf-1a454ac47413","choices":[{"finish_reason":"stop","index":0,"message":{"content":"I'm doing well, thank you for asking. I'm a helpful assistant here to provide information and assist with any questions or tasks you may have. How can I help you today?","role":"assistant"}}],"created":1747651083,"model":"llama3.1-8b","system_fingerprint":"fp_fd530e96020e4e77c6b2","object":"chat.completion","usage":{"prompt_tokens":47,"completion_tokens":38,"total_tokens":85},"time_info":{"queue_time":0.0002934,"prompt_time":0.002440425,"completion_time":0.017462833,"total_time":0.021190404891967773,"created":1747651083}}
2025-05-19 12:38:02,368 - DEBUG - Parsed JSON response: {
  "id": "chatcmpl-c4658203-acbb-4018-9fdf-1a454ac47413",
  "choices": [
    {
      "finish_reason": "stop",
      "index": 0,
      "message": {
        "content": "I'm doing well, thank you for asking. I'm a helpful assistant here to provide information and assist with any questions or tasks you may have. How can I help you today?",
        "role": "assistant"
      }
    }
  ],
  "created": 1747651083,
  "model": "llama3.1-8b",
  "system_fingerprint": "fp_fd530e96020e4e77c6b2",
  "object": "chat.completion",
  "usage": {
    "prompt_tokens": 47,
    "completion_tokens": 38,
    "total_tokens": 85
  },
  "time_info": {
    "queue_time": 0.0002934,
    "prompt_time": 0.002440425,
    "completion_time": 0.017462833,
    "total_time": 0.021190404891967773,
    "created": 1747651083
  }
}
2025-05-19 12:38:02,368 - DEBUG - close.started
2025-05-19 12:38:02,369 - DEBUG - close.complete
2025-05-19 12:38:02,369 - INFO - Testing Cohere with model command-r
2025-05-19 12:38:02,369 - DEBUG - Cohere request data: {
  "chat_history": [],
  "message": "Hello, how are you?",
  "model": "command-r",
  "temperature": 0.7,
  "max_tokens": 100,
  "preamble": "You are a helpful assistant."
}
2025-05-19 12:38:02,529 - DEBUG - Sending request to Cohere API...
2025-05-19 12:38:02,529 - DEBUG - connect_tcp.started host='api.cohere.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:38:02,557 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b55850>
2025-05-19 12:38:02,558 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f7dd1b31e50> server_hostname='api.cohere.ai' timeout=30
2025-05-19 12:38:02,588 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b55760>
2025-05-19 12:38:02,590 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:38:02,591 - DEBUG - send_request_headers.complete
2025-05-19 12:38:02,591 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:38:02,591 - DEBUG - send_request_body.complete
2025-05-19 12:38:02,592 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:38:32,592 - DEBUG - receive_response_headers.failed exception=ReadTimeout(TimeoutError('The read operation timed out'))
2025-05-19 12:38:32,593 - DEBUG - response_closed.started
2025-05-19 12:38:32,594 - DEBUG - response_closed.complete
2025-05-19 12:38:32,595 - ERROR - Cohere API error
Traceback (most recent call last):
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/connection.py", line 103, in handle_request
    return self._connection.handle_request(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/http11.py", line 136, in handle_request
    raise exc
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/http11.py", line 106, in handle_request
    ) = self._receive_response_headers(**kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/http11.py", line 177, in _receive_response_headers
    event = self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_sync/http11.py", line 217, in _receive_event
    data = self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_backends/sync.py", line 126, in read
    with map_exceptions(exc_map):
  File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadTimeout: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/colestart/dc4_doppelt_new/test_providers.py", line 165, in test_cohere
    response = client.post(
               ^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_client.py", line 1144, in post
    return self.request(
           ^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/usr/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "/home/<USER>/litellm-env/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadTimeout: The read operation timed out
2025-05-19 12:38:32,992 - INFO - Testing Mistral with model mistral-small-latest
2025-05-19 12:38:32,992 - DEBUG - Mistral request data: {
  "model": "mistral-small-latest",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:38:33,111 - DEBUG - Sending request to Mistral API...
2025-05-19 12:38:33,112 - DEBUG - connect_tcp.started host='api.mistral.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:38:33,145 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b577d0>
2025-05-19 12:38:33,270 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f7dd198f850> server_hostname='api.mistral.ai' timeout=30
2025-05-19 12:38:33,300 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd1b576e0>
2025-05-19 12:38:33,328 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:38:33,329 - DEBUG - send_request_headers.complete
2025-05-19 12:38:33,329 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:38:33,330 - DEBUG - send_request_body.complete
2025-05-19 12:38:33,330 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:38:33,935 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 19 May 2025 10:38:35 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'ratelimitbysize-reset', b'25'), (b'ratelimitbysize-remaining', b'499887'), (b'ratelimitbysize-limit', b'500000'), (b'x-ratelimitbysize-limit-minute', b'500000'), (b'ratelimitbysize-query-cost', b'113'), (b'x-ratelimitbysize-limit-month', b'1000000000'), (b'x-ratelimitbysize-remaining-month', b'985406732'), (b'x-ratelimitbysize-remaining-minute', b'499887'), (b'x-envoy-upstream-service-time', b'355'), (b'access-control-allow-origin', b'*'), (b'x-kong-upstream-latency', b'355'), (b'x-kong-proxy-latency', b'8'), (b'x-kong-request-id', b'57903f0a9902cd2269af4119fa997331'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=2RQTN_turEln2VxlMsygNUfJA0s5J4.W5qTuz7mcqMk-1747651115-*******-XeEQvhTfWTlO3HVWLTC.WBM7scFE0dk6R_KJLgsXI.fIRaLKRBekCnobH4DWtAAMZKPnzbfRCu2ltxOw5oWceuxagP13Aq6XKkttI8tYTy4; path=/; expires=Mon, 19-May-25 11:08:35 GMT; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), (b'Set-Cookie', b'_cfuvid=GEOa0K6wyw7b.rqoSZjgoOggUo.2v0Qqg3CyH6Xp6Wo-1747651115440-*******-604800000; path=/; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f72bc99f4534-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:38:33,937 - INFO - HTTP Request: POST https://api.mistral.ai/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-19 12:38:33,938 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:38:33,939 - DEBUG - receive_response_body.complete
2025-05-19 12:38:33,939 - DEBUG - response_closed.started
2025-05-19 12:38:33,940 - DEBUG - response_closed.complete
2025-05-19 12:38:33,940 - DEBUG - Mistral response status: 200
2025-05-19 12:38:33,940 - DEBUG - Mistral response headers: Headers([('date', 'Mon, 19 May 2025 10:38:35 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('ratelimitbysize-reset', '25'), ('ratelimitbysize-remaining', '499887'), ('ratelimitbysize-limit', '500000'), ('x-ratelimitbysize-limit-minute', '500000'), ('ratelimitbysize-query-cost', '113'), ('x-ratelimitbysize-limit-month', '1000000000'), ('x-ratelimitbysize-remaining-month', '985406732'), ('x-ratelimitbysize-remaining-minute', '499887'), ('x-envoy-upstream-service-time', '355'), ('access-control-allow-origin', '*'), ('x-kong-upstream-latency', '355'), ('x-kong-proxy-latency', '8'), ('x-kong-request-id', '57903f0a9902cd2269af4119fa997331'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=2RQTN_turEln2VxlMsygNUfJA0s5J4.W5qTuz7mcqMk-1747651115-*******-XeEQvhTfWTlO3HVWLTC.WBM7scFE0dk6R_KJLgsXI.fIRaLKRBekCnobH4DWtAAMZKPnzbfRCu2ltxOw5oWceuxagP13Aq6XKkttI8tYTy4; path=/; expires=Mon, 19-May-25 11:08:35 GMT; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), ('set-cookie', '_cfuvid=GEOa0K6wyw7b.rqoSZjgoOggUo.2v0Qqg3CyH6Xp6Wo-1747651115440-*******-604800000; path=/; domain=.mistral.ai; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9422f72bc99f4534-TXL'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-05-19 12:38:33,941 - DEBUG - Mistral API response: {"id":"27b2cd814bce4a50a9395a00b9579690","object":"chat.completion","created":1747651115,"model":"mistral-small-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Hello! I'm just a computer program, so I don't have feelings, but I'm here and ready to help you. How can I assist you today?"},"finish_reason":"stop","logprobs":null}],"usage":{"prompt_tokens":17,"total_tokens":51,"completion_tokens":34}}
2025-05-19 12:38:33,941 - DEBUG - Parsed JSON response: {
  "id": "27b2cd814bce4a50a9395a00b9579690",
  "object": "chat.completion",
  "created": 1747651115,
  "model": "mistral-small-latest",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "tool_calls": null,
        "content": "Hello! I'm just a computer program, so I don't have feelings, but I'm here and ready to help you. How can I assist you today?"
      },
      "finish_reason": "stop",
      "logprobs": null
    }
  ],
  "usage": {
    "prompt_tokens": 17,
    "total_tokens": 51,
    "completion_tokens": 34
  }
}
2025-05-19 12:38:33,942 - DEBUG - close.started
2025-05-19 12:38:33,942 - DEBUG - close.complete
2025-05-19 12:38:33,943 - INFO - Testing Codestral with model codestral-latest
2025-05-19 12:38:33,943 - DEBUG - Codestral request data: {
  "model": "codestral-latest",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
2025-05-19 12:38:34,045 - DEBUG - Sending request to Codestral API...
2025-05-19 12:38:34,091 - DEBUG - connect_tcp.started host='api.cerebras.ai' port=443 local_address=None timeout=30 socket_options=None
2025-05-19 12:38:34,126 - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd19c51f0>
2025-05-19 12:38:34,127 - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x7f7dd1b31dd0> server_hostname='api.cerebras.ai' timeout=30
2025-05-19 12:38:34,148 - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7f7dd19c5100>
2025-05-19 12:38:34,149 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-05-19 12:38:34,150 - DEBUG - send_request_headers.complete
2025-05-19 12:38:34,152 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-05-19 12:38:34,153 - DEBUG - send_request_body.complete
2025-05-19 12:38:34,154 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-05-19 12:38:34,371 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 404, b'Not Found', [(b'Date', b'Mon, 19 May 2025 10:38:35 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'referrer-policy', b'strict-origin-when-cross-origin'), (b'x-content-type-options', b'nosniff'), (b'strict-transport-security', b'max-age=3600; includeSubDomains'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=KDrXy8Tn4Mqu9dIEQNu53QvPypEH6cCjU0OMiMuBZA8-1747651115-*******-hT7r6aIU5_aLwK1zz_ADCRPaYe96pwx_2rufgY2R9p8eVf2hU7iNwGnNxVmaTwHZLAhWbZWq.HEuFiuP9T46zoh55wdkl2BkPnTwGj6qmn0; path=/; expires=Mon, 19-May-25 11:08:35 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9422f730e9d3e50b-TXL'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-05-19 12:38:34,372 - INFO - HTTP Request: POST https://api.cerebras.ai/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-05-19 12:38:34,373 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-05-19 12:38:34,373 - DEBUG - receive_response_body.complete
2025-05-19 12:38:34,374 - DEBUG - response_closed.started
2025-05-19 12:38:34,374 - DEBUG - response_closed.complete
2025-05-19 12:38:34,374 - DEBUG - Codestral response status: 404
2025-05-19 12:38:34,375 - DEBUG - Codestral response headers: Headers({'date': 'Mon, 19 May 2025 10:38:35 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-content-type-options': 'nosniff', 'strict-transport-security': 'max-age=3600; includeSubDomains', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=KDrXy8Tn4Mqu9dIEQNu53QvPypEH6cCjU0OMiMuBZA8-1747651115-*******-hT7r6aIU5_aLwK1zz_ADCRPaYe96pwx_2rufgY2R9p8eVf2hU7iNwGnNxVmaTwHZLAhWbZWq.HEuFiuP9T46zoh55wdkl2BkPnTwGj6qmn0; path=/; expires=Mon, 19-May-25 11:08:35 GMT; domain=.api.cerebras.ai; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9422f730e9d3e50b-TXL', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
2025-05-19 12:38:34,375 - DEBUG - Codestral API response: {"message":"Model codestral-latest does not exist or you do not have access to it.","type":"not_found_error","param":"model","code":"model_not_found"}
2025-05-19 12:38:34,375 - ERROR - Codestral API returned status 404
2025-05-19 12:38:34,375 - DEBUG - close.started
2025-05-19 12:38:34,376 - DEBUG - close.complete
