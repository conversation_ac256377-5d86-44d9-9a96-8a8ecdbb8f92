#!/usr/bin/env python3
"""
Test script for Piper TTS
"""
import sys
import os
from pathlib import Path

try:
    from piper import PiperVoice
except ImportError:
    print("Error: Could not import Piper. Make sure it's installed correctly.")
    sys.exit(1)

def main():
    # Path to the voice model
    voice_model = Path("voices/en_US-libritts-high.onnx")
    voice_config = Path("voices/en_US-libritts-high.onnx.json")
    
    if not voice_model.exists() or not voice_config.exists():
        print(f"Error: Voice model or config not found at {voice_model} or {voice_config}")
        sys.exit(1)
    
    # Initialize Piper
    print("Initializing Piper TTS...")
    try:
        voice = PiperVoice.load(str(voice_model), config_path=str(voice_config))
    except Exception as e:
        print(f"Error loading voice model: {e}")
        sys.exit(1)
    
    # Text to synthesize
    text = "Hello, this is a test of Piper Text-to-Speech running on Python 3.10 in WSL."
    
    # Output file
    output_file = Path("test_output.wav")
    
    # Synthesize speech
    print(f"Synthesizing speech: '{text}'")
    try:
        with open(output_file, "wb") as f:
            voice.synthesize(text, f)
        print(f"Speech synthesized successfully and saved to {output_file}")
    except Exception as e:
        print(f"Error synthesizing speech: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
