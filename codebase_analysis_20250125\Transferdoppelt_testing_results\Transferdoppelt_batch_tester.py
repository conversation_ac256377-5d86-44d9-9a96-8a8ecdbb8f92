#!/usr/bin/env python3
"""
🚀 TRANSFERDOPPELT FOLDER COMPREHENSIVE BATCH TESTER
Systematically tests all Python scripts in the Transferdoppelt folder
"""

import os
import subprocess
import sys
from pathlib import Path
import time

def find_python_scripts(directory):
    """Find all Python scripts in directory and subdirectories"""
    python_scripts = []
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                full_path = os.path.join(root, file)
                python_scripts.append(full_path)
    
    return sorted(python_scripts)

def test_script_syntax(script_path):
    """Test if a Python script has valid syntax"""
    try:
        result = subprocess.run(
            ['python3', '-m', 'py_compile', script_path],
            capture_output=True,
            text=True,
            timeout=15
        )
        return result.returncode == 0, result.stderr
    except subprocess.TimeoutExpired:
        return False, "Timeout during compilation"
    except Exception as e:
        return False, str(e)

def categorize_script(script_path):
    """Categorize script by subfolder"""
    if 'Greg/' in script_path:
        return 'Greg'
    elif 'Kurznew/' in script_path:
        return 'Kurznew'
    elif 'Python/' in script_path:
        return 'Python'
    elif 'Mannmannmann/' in script_path:
        return 'Mannmannmann'
    elif 'Neumodularisierung7files/' in script_path:
        return 'Neumodularisierung7files'
    elif 'Neumoduldoppelt/' in script_path:
        return 'Neumoduldoppelt'
    elif 'Newfiles/' in script_path:
        return 'Newfiles'
    elif 'Testzero/' in script_path:
        return 'Testzero'
    else:
        return 'Root'

def main():
    print("🎯 TRANSFERDOPPELT FOLDER COMPREHENSIVE TESTING STARTED")
    print("=" * 70)
    
    # Change to the correct directory
    base_dir = "/home/<USER>/colestart"
    os.chdir(base_dir)
    
    # Find all Python scripts in Transferdoppelt folder
    transferdoppelt_dir = "Transferdoppelt"
    if not os.path.exists(transferdoppelt_dir):
        print(f"❌ Directory {transferdoppelt_dir} not found!")
        return
    
    scripts = find_python_scripts(transferdoppelt_dir)
    total_scripts = len(scripts)
    
    print(f"📊 Found {total_scripts} Python scripts to test")
    print("=" * 70)
    
    # Test results
    valid_scripts = []
    invalid_scripts = []
    categories = {}
    
    # Test each script
    for i, script in enumerate(scripts, 1):
        relative_path = os.path.relpath(script, base_dir)
        category = categorize_script(relative_path)
        
        print(f"[{i}/{total_scripts}] Testing: {relative_path}")
        print(f"  Category: {category}")
        
        is_valid, error_msg = test_script_syntax(script)
        
        if is_valid:
            print(f"  ✅ Syntax: VALID")
            valid_scripts.append(relative_path)
            if category not in categories:
                categories[category] = {'valid': 0, 'invalid': 0}
            categories[category]['valid'] += 1
        else:
            print(f"  ❌ Syntax: INVALID - {error_msg}")
            invalid_scripts.append((relative_path, error_msg))
            if category not in categories:
                categories[category] = {'valid': 0, 'invalid': 0}
            categories[category]['invalid'] += 1
        
        # Progress update every 25 scripts
        if i % 25 == 0:
            print(f"\n📊 Progress: {i}/{total_scripts} scripts tested")
            print(f"   ✅ Valid: {len(valid_scripts)}")
            print(f"   ❌ Invalid: {len(invalid_scripts)}")
            print()
    
    # Final results
    print("\n" + "=" * 70)
    print("🎯 TRANSFERDOPPELT FOLDER TESTING COMPLETED")
    print("=" * 70)
    print(f"📊 Total Scripts: {total_scripts}")
    print(f"✅ Valid Scripts: {len(valid_scripts)} ({len(valid_scripts)/total_scripts*100:.1f}%)")
    print(f"❌ Invalid Scripts: {len(invalid_scripts)} ({len(invalid_scripts)/total_scripts*100:.1f}%)")
    
    # Save detailed results
    results_file = "codebase_analysis_20250125/Transferdoppelt_testing_results/Transferdoppelt_detailed_results.txt"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    
    with open(results_file, 'w') as f:
        f.write("🚀 TRANSFERDOPPELT FOLDER DETAILED TESTING RESULTS\n")
        f.write("=" * 70 + "\n\n")
        f.write(f"Total Scripts: {total_scripts}\n")
        f.write(f"Valid Scripts: {len(valid_scripts)}\n")
        f.write(f"Invalid Scripts: {len(invalid_scripts)}\n\n")
        
        f.write("✅ VALID SCRIPTS:\n")
        f.write("-" * 40 + "\n")
        for script in valid_scripts:
            f.write(f"  {script}\n")
        
        f.write("\n❌ INVALID SCRIPTS:\n")
        f.write("-" * 40 + "\n")
        for script, error in invalid_scripts:
            f.write(f"  {script}\n")
            f.write(f"    Error: {error}\n\n")
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    # Summary by category
    print("\n📊 RESULTS BY CATEGORY:")
    for category, stats in categories.items():
        total_cat = stats['valid'] + stats['invalid']
        success_rate = (stats['valid'] / total_cat * 100) if total_cat > 0 else 0
        print(f"  {category}: {stats['valid']}/{total_cat} valid ({success_rate:.1f}%)")

if __name__ == "__main__":
    main()
