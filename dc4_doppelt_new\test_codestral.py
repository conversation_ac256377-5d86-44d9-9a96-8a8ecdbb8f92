#!/usr/bin/env python3
"""
Test script specifically for the Codestral provider.
"""

import httpx
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Codestral configuration
API_KEY = "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"
MODEL = "codestral-latest"
ENDPOINT = "https://codestral.mistral.ai/v1/chat/completions"

def test_codestral():
    """Test Codestral API"""
    logger.info(f"Testing Codestral with model {MODEL}")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    data = {
        "model": MODEL,
        "messages": messages,
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    logger.debug(f"Codestral request data: {json.dumps(data, indent=2)}")
    
    try:
        with httpx.Client(timeout=30) as client:
            logger.debug("Sending request to Codestral API...")
            response = client.post(
                ENDPOINT,
                headers=headers,
                json=data
            )
            
            logger.debug(f"Codestral response status: {response.status_code}")
            logger.debug(f"Codestral response headers: {response.headers}")
            logger.debug(f"Codestral API response: {response.text}")
            
            if response.status_code != 200:
                logger.error(f"Codestral API returned status {response.status_code}")
                return False, f"Error: {response.status_code} - {response.text}"
            
            result = response.json()
            logger.debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")
            
            return True, result["choices"][0]["message"]["content"]
    except Exception as e:
        logger.exception("Codestral API error")
        return False, f"Exception: {str(e)}"

if __name__ == "__main__":
    success, response = test_codestral()
    print(f"\n===== CODESTRAL TEST RESULT =====")
    print(f"Status: {'SUCCESS' if success else 'FAILED'}")
    if not success:
        print(f"Error: {response}")
    else:
        print(f"Response: {response}")
