import asyncio
import litellm
import traceback

# Configuration
API_KEY = "278fdf4b6ed54926ae4f1d4c6a062425"
CHAT_BASE_URL = "https://api.aimlapi.com/v2"
EMBEDDING_BASE_URL = "https://api.aimlapi.com/v1"
TEST_MESSAGE = "Hello, this is a test message."

# List of models to test (assumed to be available in free trial)
MODELS = [
    {
        "name": "openai/meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
        "type": "chat",
        "base_url": CHAT_BASE_URL
    },
    {
        "name": "openai/Qwen/Qwen2-72B-Instruct",
        "type": "chat",
        "base_url": CHAT_BASE_URL
    },
    {
        "name": "openai/text-embedding-3-small",
        "type": "embedding",
        "base_url": EMBEDDING_BASE_URL
    }
]

async def test_chat_model(model_name, base_url):
    try:
        response = await litellm.acompletion(
            model=model_name,
            api_key=API_KEY,
            api_base=base_url,
            messages=[{"role": "user", "content": TEST_MESSAGE}],
            max_tokens=50
        )
        print(f"Success for {model_name}: {response.choices[0].message.content}")
    except Exception as e:
        print(f"Error for {model_name}: {str(e)}")

async def test_embedding_model(model_name, base_url):
    try:
        response = await litellm.aembedding(
            model=model_name,
            api_key=API_KEY,
            api_base=base_url,
            input=TEST_MESSAGE
        )
        print(f"Success for {model_name}: Embedding generated (length: {len(response.data[0].embedding)})")
    except Exception as e:
        print(f"Error for {model_name}: {str(e)}")

async def main():
    print("Testing AIMLAPI models with provided API key...")
    for model in MODELS:
        print(f"\nTesting model: {model['name']}")
        if model["type"] == "chat":
            await test_chat_model(model["name"], model["base_url"])
        elif model["type"] == "embedding":
            await test_embedding_model(model["name"], model["base_url"])
        await asyncio.sleep(1)  # Brief pause to avoid rate limits

if __name__ == "__main__":
    asyncio.run(main())