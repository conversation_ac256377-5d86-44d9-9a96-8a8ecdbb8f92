# Keinplan Project Index

This document provides a summary of the scripts in the `/home/<USER>/colestart/keinplan` folder, their dependencies, and their functionality.

## Overview

The keinplan folder contains a collection of Python scripts primarily focused on Telegram automation. The scripts can be categorized into several related projects:

1. **Telegram Link Extractor** - Scripts that monitor Telegram messages and extract t.me links and @mentions
2. **Telegram Chat Lister** - Scripts that list all chats/dialogs a user is part of
3. **Telegram Channel Joiner** - Scripts that automatically join Telegram channels from extracted links

## Script Count

Total number of Python scripts: 14

## Dependencies

### External Python Libraries
- telethon - Telegram client library
- asyncio - For asynchronous programming
- logging - For logging information and errors
- bs4 (BeautifulSoup) - For HTML parsing
- re - For regular expression operations

### Configuration Files
- config.txt - Contains API_ID and API_HASH for Telegram API access

### Data Files
- tme.txt - Stores extracted t.me links
- at.txt / at_mentions.txt - Stores extracted @mentions
- joined_groups.txt - Tracks status of joined Telegram groups/channels
- chat_list.txt - Stores list of chats/dialogs
- processed_links.txt - Tracks processed links
- session_name.session - Telethon session file

## Script Details

### Telegram Link Extractor Scripts

#### omg3.py (22,057 bytes)
**Purpose**: Advanced Telegram link extractor with channel joining capabilities
**Dependencies**: telethon, bs4, asyncio, logging, re, datetime
**Files Used**: config.txt, tme.txt, at.txt, joined_groups.txt, session_name.session, telegram_link_extraction.log
**Functionality**:
- Monitors Telegram messages for t.me links and @mentions
- Extracts and saves links to tme.txt and mentions to at.txt
- Implements rate-limited joining of Telegram channels from extracted links
- Handles flood wait errors and implements daily join limits
- Tracks joined groups and their status

#### omg4.py (7,649 bytes)
**Purpose**: Simplified version of omg3.py without channel joining
**Dependencies**: telethon, bs4, asyncio, logging, re
**Files Used**: config.txt, tme.txt, at.txt, session_name.session, telegram_link_extraction.log
**Functionality**:
- Monitors Telegram messages for t.me links and @mentions
- Extracts and saves links to tme.txt and mentions to at.txt
- Does not include channel joining functionality

#### fag.py, fag1.py, fag99.py (5,275 bytes each)
**Purpose**: Earlier versions of the link extractor
**Dependencies**: telethon, asyncio, logging, re
**Files Used**: config.txt, tme.txt, at_mentions.txt, telegram_link_extraction.log, session_name.session
**Functionality**:
- Monitors Telegram messages for t.me links and @mentions
- Extracts and saves links to tme.txt and mentions to at_mentions.txt
- Uses different regex patterns for extraction

#### omg.py (7,522 bytes), omg1.py (7,303 bytes), omg2.py (5,519 bytes)
**Purpose**: Iterations of the link extractor with varying features
**Dependencies**: telethon, asyncio, logging, re, bs4
**Files Used**: config.txt, tme.txt, at.txt, session_name.session, telegram_link_extraction.log
**Functionality**:
- Similar to omg3.py and omg4.py but with different implementations
- Extracts t.me links and @mentions from Telegram messages

### Telegram Chat Lister Scripts

#### newlife.py (3,486 bytes)
**Purpose**: Lists all Telegram chats/dialogs a user is part of
**Dependencies**: telethon, asyncio, logging
**Files Used**: config.txt, chat_list.txt, telegram_chat_list.log, session_name.session
**Functionality**:
- Connects to Telegram using API credentials
- Retrieves all dialogs (chats) the user is part of
- Extracts chat ID, username, t.me link, and chat name
- Sorts the list alphabetically by t.me link
- Saves the sorted list to chat_list.txt

#### nl.py (4,896 bytes), nl1.py (5,084 bytes), nl2.py (5,415 bytes)
**Purpose**: Variations of the chat lister with different features
**Dependencies**: telethon, asyncio, logging
**Files Used**: config.txt, chat_list.txt, telegram_chat_list.log, session_name.session
**Functionality**:
- Similar to newlife.py but with different implementations
- Lists and saves Telegram chats/dialogs

#### n1.py (5,415 bytes), ngay.py (4,545 bytes), nig.py (5,461 bytes), nigger.py (5,455 bytes)
**Purpose**: Additional variations of the chat lister
**Dependencies**: telethon, asyncio, logging
**Files Used**: config.txt, chat_list.txt, telegram_chat_list.log, session_name.session
**Functionality**:
- Similar to other chat lister scripts with minor variations
- Lists and saves Telegram chats/dialogs

## Empty/Placeholder Files

- stupid.py (0 bytes) - Empty file

## Summary

The keinplan folder contains a collection of related Telegram automation scripts that focus on:

1. Extracting and saving t.me links and @mentions from Telegram messages
2. Listing and saving information about Telegram chats/dialogs
3. Automatically joining Telegram channels from extracted links

The scripts appear to be iterations and variations of the same core functionality, with newer versions (omg3.py, omg4.py) having more advanced features like rate limiting, error handling, and better organization.

All scripts rely on the Telegram API through the telethon library and require API credentials stored in config.txt.
