"""
Stability AI Image Generation Script with Cost Optimization

Features:
- Supports all Stability AI models with automatic cost calculation
- Smart model selection based on quality/speed requirements
- Detailed cost tracking
- Fallback strategies for budget constraints
"""

import os
import requests
import json
from typing import Dict, List, Optional

# Configuration
STABILITY_API_KEY = os.getenv("sk-7hCVC3kwCOYffr842crao7cTDQ65PJ22cyhXnyFrMgrCEyqF")  # Set your API key in environment
BASE_URL = "https://api.stability.ai/v1/"

MODELS = {
    # Model ID: {config}
    "stable-image-ultra": {
        "name": "Stable Image Ultra",
        "type": "image",
        "parameters": "8B",
        "cost_per_image": 8.0,
        "endpoint": "generation/stable-image-ultra/image-to-image",
        "recommended_for": "highest quality commercial work",
        "strengths": ["photorealism", "detail", "prompt adherence"]
    },
    "sd3.5-large": {
        "name": "Stable Diffusion 3.5 Large",
        "type": "image",
        "parameters": "8B",
        "cost_per_image": 6.5,
        "endpoint": "generation/sd3.5-large/text-to-image",
        "recommended_for": "high-quality general purpose",
        "strengths": ["balance", "complex prompts"]
    },
    "sd3.5-large-turbo": {
        "name": "Stable Diffusion 3.5 Large Turbo",
        "type": "image",
        "parameters": "8B",
        "cost_per_image": 4.0,
        "endpoint": "generation/sd3.5-large-turbo/text-to-image",
        "recommended_for": "fast high-quality generations",
        "strengths": ["speed", "iterations"]
    },
    "sd3.5-medium": {
        "name": "Stable Diffusion 3.5 Medium",
        "type": "image",
        "parameters": "2.5B",
        "cost_per_image": 3.5,
        "endpoint": "generation/sd3.5-medium/text-to-image",
        "recommended_for": "balanced cost/quality",
        "strengths": ["efficiency", "simple prompts"]
    },
    "stable-image-core": {
        "name": "Stable Image Core",
        "type": "image",
        "parameters": "1.5B",
        "cost_per_image": 3.0,
        "endpoint": "generation/stable-image-core/text-to-image",
        "recommended_for": "rapid prototyping",
        "strengths": ["speed", "low cost"]
    },
    "sdxl-1.0": {
        "name": "SDXL 1.0",
        "type": "image",
        "parameters": "3.5B",
        "cost_per_image": 0.9,
        "endpoint": "generation/sdxl-1.0/text-to-image",
        "recommended_for": "legacy projects",
        "strengths": ["flexibility", "artistic styles"]
    },
    "sd1.6": {
        "name": "SD 1.6",
        "type": "image",
        "parameters": "1.2B",
        "cost_per_image": 0.9,
        "endpoint": "generation/sd1.6/text-to-image",
        "recommended_for": "non-standard aspect ratios",
        "strengths": ["aspect flexibility", "abstract art"]
    }
}

class StabilityGenerator:
    """
    Cost-optimized image generator for Stability AI models
    
    Features:
    - Automatic model selection based on requirements
    - Cost tracking
    - Fallback strategies
    - Batch processing
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.total_cost = 0.0
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Accept": "application/json"
        })

    def get_cheapest_model(self, requirements: Dict) -> Optional[str]:
        """
        Select the most cost-effective model meeting requirements
        
        Args:
            requirements: Dictionary containing:
                - min_quality: "low"|"medium"|"high"|"ultra"
                - max_time: Maximum generation time in seconds
                - require_features: List of required features
        
        Returns:
            Model ID or None if no match
        """
        quality_order = ["ultra", "high", "medium", "low"]
        models = sorted(
            MODELS.items(),
            key=lambda x: x[1]["cost_per_image"]
        )

        for model_id, config in models:
            # Quality check
            model_quality = self._get_quality_level(config["name"])
            req_quality = quality_order.index(requirements.get("min_quality", "low"))
            
            if quality_order.index(model_quality) < req_quality:
                continue
                
            # Feature check
            if all(feature in config["strengths"] 
                   for feature in requirements.get("require_features", [])):
                return model_id
                
        return None

    def generate_image(
        self,
        prompt: str,
        model_id: str = "auto",
        **kwargs
    ) -> Optional[bytes]:
        """
        Generate image with cost tracking
        
        Args:
            prompt: Text prompt for generation
            model_id: Specific model ID or "auto" for automatic selection
            **kwargs: Additional parameters (width, height, etc.)
            
        Returns:
            Image bytes or None on failure
        """
        if model_id == "auto":
            model_id = self.get_cheapest_model({
                "min_quality": kwargs.get("quality", "low"),
                "require_features": kwargs.get("require_features", [])
            })
            
        if model_id not in MODELS:
            raise ValueError(f"Invalid model ID: {model_id}")
            
        endpoint = BASE_URL + MODELS[model_id]["endpoint"]
        params = self._get_default_params(model_id)
        params.update(kwargs)
        params["text_prompts"] = [{"text": prompt}]
        
        try:
            response = self.session.post(endpoint, json=params)
            response.raise_for_status()
            self.total_cost += MODELS[model_id]["cost_per_image"]
            return response.content
        except Exception as e:
            print(f"Generation failed: {str(e)}")
            return None

    def _get_default_params(self, model_id: str) -> Dict:
        """Get safe default parameters for each model"""
        base_params = {
            "cfg_scale": 7,
            "steps": 30,
            "samples": 1
        }
        
        if "ultra" in model_id:
            return {**base_params, "width": 1024, "height": 1024}
        if "sdxl" in model_id:
            return {**base_params, "width": 768, "height": 768}
        return base_params

    def _get_quality_level(self, model_name: str) -> str:
        """Map model names to quality levels"""
        if "Ultra" in model_name: return "ultra"
        if "Large" in model_name: return "high"
        if "Medium" in model_name: return "medium"
        return "low"

    def get_cost_estimate(self, num_images: int, model_id: str) -> float:
        """Calculate estimated cost for batch generation"""
        return MODELS[model_id]["cost_per_image"] * num_images

# Example Usage
if __name__ == "__main__":
    generator = StabilityGenerator(STABILITY_API_KEY)
    
    # 1. Basic generation with cheapest model
    basic_image = generator.generate_image(
        "A cute corgi puppy in a basket",
        quality="medium"
    )
    print(f"Basic generation cost: ${generator.total_cost:.2f}")
    
    # 2. High-quality generation with cost control
    premium_image = generator.generate_image(
        "Fashion model wearing futuristic armor, 8k resolution",
        require_features=["photorealism", "detail"],
        quality="high"
    )
    print(f"Total cost after premium: ${generator.total_cost:.2f}")
    
    # 3. Batch generation estimate
    batch_size = 10
    estimated_cost = generator.get_cost_estimate(
        batch_size,
        "stable-image-core"
    )
    print(f"Estimated cost for {batch_size} images: ${estimated_cost:.2f}")