🌐 APITEST FOLDER DETAILED TESTING RESULTS
============================================================

Total Scripts: 39
Valid Scripts: 39
Invalid Scripts: 0

✅ VALID SCRIPTS:
------------------------------
  apitest/21_SHIT_STREET/s21.py
  apitest/21_SHIT_STREET/s21_2.py
  apitest/______hyperbolic.py
  apitest/__clarifai.py
  apitest/__nlpcloud.py
  apitest/__openr.py
  apitest/__premai_test.py
  apitest/__replicate.py
  apitest/_deepinfra.py
  apitest/_deepseek.py
  apitest/_fal.py
  apitest/_togetherai.py
  apitest/ai_ml.py
  apitest/ai_ml2.py
  apitest/bigm.py
  apitest/cerebras.py
  apitest/cohereai.py
  apitest/firew.py
  apitest/forefront.py
  apitest/gooseai.py
  apitest/groq.py
  apitest/groq2.py
  apitest/groqtts.py
  apitest/laminiai.py
  apitest/listallmodels.py
  apitest/mistral.py
  apitest/naas/naas.py
  apitest/naas/naas2.py
  apitest/naas/naas3.py
  apitest/naas/naas4.py
  apitest/naas/naas5.py
  apitest/naas/naas6.py
  apitest/naas/naas_comprehensive.py
  apitest/naas/test_naas.py
  apitest/openIA.py
  apitest/samban.py
  apitest/stable.py
  apitest/stable2.py
  apitest/textcortex.py

❌ INVALID SCRIPTS:
------------------------------
