#!/usr/bin/env python3
"""
🔧 REMAINING FOLDERS COMPREHENSIVE BATCH TESTER
Systematically tests all Python scripts in remaining folders
"""

import os
import subprocess
import sys
from pathlib import Path
import time

def find_python_scripts(directory):
    """Find all Python scripts in directory and subdirectories"""
    python_scripts = []
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                full_path = os.path.join(root, file)
                python_scripts.append(full_path)
    
    return sorted(python_scripts)

def test_script_syntax(script_path):
    """Test if a Python script has valid syntax"""
    try:
        result = subprocess.run(
            ['python3', '-m', 'py_compile', script_path],
            capture_output=True,
            text=True,
            timeout=15
        )
        return result.returncode == 0, result.stderr
    except subprocess.TimeoutExpired:
        return False, "Timeout during compilation"
    except Exception as e:
        return False, str(e)

def categorize_script(script_path):
    """Categorize script by folder"""
    if 'text-picture-mime/' in script_path:
        return 'text-picture-mime'
    elif 'text-picture-mime copy/' in script_path:
        return 'text-picture-mime_copy'
    elif 'wowomg/' in script_path and 'copy' not in script_path:
        return 'wowomg'
    elif 'wowomg copy' in script_path:
        return 'wowomg_copy'
    elif 'tts/' in script_path:
        return 'tts'
    elif 'telegram_reporter_bot/' in script_path:
        return 'telegram_reporter_bot'
    elif 'coester/' in script_path:
        return 'coester'
    else:
        return 'other'

def main():
    print("🎯 REMAINING FOLDERS COMPREHENSIVE TESTING STARTED")
    print("=" * 70)
    
    # Change to the correct directory
    base_dir = "/home/<USER>/colestart"
    os.chdir(base_dir)
    
    # Define remaining folders to test
    remaining_folders = [
        "text-picture-mime",
        "text-picture-mime copy",
        "wowomg",
        "wowomg copy",
        "wowomg copy 2", 
        "wowomg copy 3",
        "tts",
        "telegram_reporter_bot",
        "coester"
    ]
    
    print(f"📁 Testing {len(remaining_folders)} remaining folders:")
    for folder in remaining_folders:
        print(f"  - {folder}")
    
    # Find all Python scripts in remaining folders
    all_scripts = []
    
    for folder in remaining_folders:
        if os.path.exists(folder):
            scripts = find_python_scripts(folder)
            all_scripts.extend(scripts)
            print(f"📁 {folder}: {len(scripts)} scripts found")
        else:
            print(f"📁 {folder}: NOT FOUND")
    
    total_scripts = len(all_scripts)
    print(f"📊 Total scripts across remaining folders: {total_scripts}")
    print("=" * 70)
    
    # Test results
    valid_scripts = []
    invalid_scripts = []
    categories = {}
    
    # Test each script
    for i, script in enumerate(all_scripts, 1):
        relative_path = os.path.relpath(script, base_dir)
        category = categorize_script(relative_path)
        
        print(f"[{i}/{total_scripts}] Testing: {relative_path}")
        print(f"  Category: {category}")
        
        is_valid, error_msg = test_script_syntax(script)
        
        if is_valid:
            print(f"  ✅ Syntax: VALID")
            valid_scripts.append(relative_path)
            if category not in categories:
                categories[category] = {'valid': 0, 'invalid': 0}
            categories[category]['valid'] += 1
        else:
            print(f"  ❌ Syntax: INVALID - {error_msg}")
            invalid_scripts.append((relative_path, error_msg))
            if category not in categories:
                categories[category] = {'valid': 0, 'invalid': 0}
            categories[category]['invalid'] += 1
        
        # Progress update every 10 scripts
        if i % 10 == 0:
            print(f"\n📊 Progress: {i}/{total_scripts} scripts tested")
            print(f"   ✅ Valid: {len(valid_scripts)}")
            print(f"   ❌ Invalid: {len(invalid_scripts)}")
            print()
    
    # Final results
    print("\n" + "=" * 70)
    print("🎯 REMAINING FOLDERS TESTING COMPLETED")
    print("=" * 70)
    print(f"📊 Total Scripts: {total_scripts}")
    print(f"✅ Valid Scripts: {len(valid_scripts)} ({len(valid_scripts)/total_scripts*100:.1f}%)")
    print(f"❌ Invalid Scripts: {len(invalid_scripts)} ({len(invalid_scripts)/total_scripts*100:.1f}%)")
    
    # Save detailed results
    results_file = "codebase_analysis_20250125/remaining_folders_testing_results/remaining_folders_detailed_results.txt"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    
    with open(results_file, 'w') as f:
        f.write("🔧 REMAINING FOLDERS DETAILED TESTING RESULTS\n")
        f.write("=" * 70 + "\n\n")
        f.write(f"Total Scripts: {total_scripts}\n")
        f.write(f"Valid Scripts: {len(valid_scripts)}\n")
        f.write(f"Invalid Scripts: {len(invalid_scripts)}\n\n")
        
        f.write("✅ VALID SCRIPTS:\n")
        f.write("-" * 40 + "\n")
        for script in valid_scripts:
            f.write(f"  {script}\n")
        
        f.write("\n❌ INVALID SCRIPTS:\n")
        f.write("-" * 40 + "\n")
        for script, error in invalid_scripts:
            f.write(f"  {script}\n")
            f.write(f"    Error: {error}\n\n")
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    # Summary by category
    print("\n📊 RESULTS BY FOLDER CATEGORY:")
    for category, stats in categories.items():
        total_cat = stats['valid'] + stats['invalid']
        success_rate = (stats['valid'] / total_cat * 100) if total_cat > 0 else 0
        print(f"  {category}: {stats['valid']}/{total_cat} valid ({success_rate:.1f}%)")

if __name__ == "__main__":
    main()
