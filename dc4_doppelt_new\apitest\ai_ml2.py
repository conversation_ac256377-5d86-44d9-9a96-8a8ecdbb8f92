import asyncio
import traceback
import aiohttp

# Configuration
API_KEY = "278fdf4b6ed54926ae4f1d4c6a062425"
CHAT_BASE_URL = "https://api.aimlapi.com/v1/chat/completions"
TEST_MESSAGE = "Hello, this is a test message."
DELAY_SECONDS = 10  # You can adjust this value

# List of models to test
MODELS = [
    "abab6.5s-chat",
    "Qwen/Qwen2-72B-Instruct",
    "mistralai/Mistral-7B-Instruct-v0.1",
    "mistralai/Mistral-7B-Instruct-v0.2",
    "mistralai/Mistral-7B-Instruct-v0.3",
    "mistralai/Mixtral-8x22B-Instruct-v0.1",
    "mistralai/Mixtral-8x7B-Instruct-v0.1",
    "mistralai/codestral-2501",
    "mistralai/mistral-nemo",
    "mistralai/mistral-tiny"
    "abab6.5s-chat",
    "chatgpt-4o-latest",
    "claude-3-5-haiku-20241022",
    "claude-3-5-sonnet-20240620",
    "claude-3-5-sonnet-20241022",
    "claude-3-7-sonnet-20250219",
    "claude-3-haiku-20240307",
    "claude-3-opus-20240229",
    "claude-3-sonnet-20240229"
    # Add other chat models compatible with AIMLAPI
]

async def test_model(model_name):
    try:
        async with aiohttp.ClientSession() as session:
            payload = {
                "model": model_name,
                "messages": [{"role": "user", "content": TEST_MESSAGE}]
            }
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json"
            }
            async with session.post(CHAT_BASE_URL, json=payload, headers=headers) as response:
                response.raise_for_status()
                data = await response.json()
                print(f"  Response from {model_name}: {data['choices'][0]['message']['content']}")
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")

async def main():
    print("Testing models with AIMLAPI...")
    for model_name in MODELS:
        print(f"\nTesting model: {model_name}")
        await test_model(model_name)
        await asyncio.sleep(DELAY_SECONDS)  # Pause for the specified number of seconds

if __name__ == "__main__":
    asyncio.run(main())
