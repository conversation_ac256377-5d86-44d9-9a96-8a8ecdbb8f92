import os
import traceback
import time
import openai

# Configuration
BASE_URL = "https://api.hyperbolic.xyz/v1"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************.m40mztyd4VFmBDXKlrOzC1R827LH1FMwZupF9fAEbKE"
DELAY_SECONDS = 10  # Adjust as needed
TEST_MESSAGE = "only say a number between 1 and 10"
MAX_TOKENS = 100

# DeepInfra models to test
DEEPINFRA_MODELS = [
    "Qwen/Qwen2.5-72B-Instruct",
    "Qwen/Qwen2.5-VL-72B-Instruct",
    "deepseek-ai/DeepSeek-V3-0324",
    "meta-llama/Meta-Llama-3.1-70B-Instruct",
    "Qwen/QwQ-32B-Preview",
    "meta-llama/Llama-3.3-70B-Instruct",
    "meta-llama/Llama-3.2-3B-Instruct",
    "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "deepseek-ai/DeepSeek-R1",
    "meta-llama/Meta-Llama-3-70B-Instruct",
    "meta-llama/Meta-Llama-3.1-405B-FP8",
    "Qwen/Qwen2.5-VL-7B-Instruct",
    "meta-llama/Meta-Llama-3.1-405B-Instruct",
    "Qwen/QwQ-32B",
    "deepseek-ai/DeepSeek-V3",
    "NousResearch/Hermes-3-Llama-3.1-70B",
    "meta-llama/Meta-Llama-3.1-405B",
    "mistralai/Pixtral-12B-2409",
    "Qwen/Qwen2.5-Coder-32B-Instruct"
]

def test_model(model_name):
    try:
        client = openai.OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": TEST_MESSAGE}],
            max_tokens=MAX_TOKENS,
        )
        output = response.choices[0].message.content
        print(f"  Response from {model_name}: {output}")
        return True
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

def main():
    print("Testing DeepInfra models...")
    for model_name in DEEPINFRA_MODELS:
        print(f"\nTesting model: {model_name}")
        success = test_model(model_name)
        if success:
            print(f"{model_name} is accessible")
        else:
            print(f"{model_name} is not accessible")
        time.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    main()
