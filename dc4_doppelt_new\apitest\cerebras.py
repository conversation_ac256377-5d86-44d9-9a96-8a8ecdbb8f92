import requests
import json
import time

# Replace with your actual Cerebras API key
CEREBRAS_API_KEY = "csk-wd222j2mmd84wc36dvxer6tnrck5kfmnfxdyvp2t26mp5tce"

# Define the Cerebras API endpoint
CEREBRAS_API_URL = "https://api.cerebras.ai/v1/chat/completions"

# Define all available Cerebras models
models_to_test = [
    "llama-3.1-8b",
    "llama-3.3-70b",
    "llama-4-scout-17b-16e-instruct", 
]

# Define the test message
test_message = "What is the capital of France?"

# Function to send a request to the Cerebras API
def send_request(model_name, message):
    headers = {
        "Authorization": f"Bearer {CEREBRAS_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": model_name,
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ]
    }

    try:
        response = requests.post(CEREBRAS_API_URL, headers=headers, data=json.dumps(payload))
        response.raise_for_status()  # Raise an exception for bad status codes
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error for model {model_name}: {e}")
        return None
    except json.JSONDecodeError:
        print(f"Error decoding JSON response from model {model_name}")
        return None

# Iterate over the models and send the test message
for model_name in models_to_test:
    print(f"Testing model: {model_name}")
    response_data = send_request(model_name, test_message)

    if response_data:
        try:
            # Print the entire response for debugging
            print(f"Full Response from {model_name}: {response_data}")
            # Extract the generated text.  Added error handling.
            if 'choices' in response_data and len(response_data['choices']) > 0 and 'message' in response_data['choices'][0] and 'content' in response_data['choices'][0]['message']:
                generated_text = response_data['choices'][0]['message']['content']
                print(f"Response from {model_name}: {generated_text}")
            else:
                print(f"Error: Unexpected response format from {model_name}")

        except KeyError:
            print(f"Error: \'choices\' or \'message\' or \'content\' key not found in response from {model_name}")

    print("-" * 20)
    time.sleep(20)  # Add a 20-second delay

