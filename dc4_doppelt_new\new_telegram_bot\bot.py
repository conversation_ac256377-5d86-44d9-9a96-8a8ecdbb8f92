import json
from telethon import TelegramClient, events
from telethon.tl.types import InputPeerChannel

# Load configuration
with open('config.json') as config_file:
    config = json.load(config_file)

api_id = config['api_id']
api_hash = config['api_hash']
group_ids = config['group_ids']

# Initialize the client
client = TelegramClient('session_name', api_id, api_hash, sequential_updates=True)

async def preload_entities():
    """Preload all target group entities at startup"""
    entities = {}
    for target_group in group_ids['to']:
        try:
            entities[target_group] = await client.get_entity(int(target_group))
            print(f"Preloaded entity for group {target_group}")
        except Exception as e:
            print(f"Failed to preload group {target_group}: {str(e)}")
    return entities

@client.on(events.NewMessage)
async def handler(event):
    if event.is_private:
        return

    print(f"Received message from chat_id: {event.chat_id}")
    if (group_ids['from'] == 'all' or str(event.chat_id) in group_ids['from']) and str(event.chat_id) not in group_ids['to'] and not event.message.media:
        print(f"Message from allowed group: {event.chat_id}")
        print(f"Forwarding message to target groups: {group_ids['to']}")
        
        for target_group in group_ids['to']:
            print(f"Forwarding to: {target_group}")
            try:
                # Try to get entity from preloaded ones first
                entity = await client.get_entity(int(target_group))
                await client.send_message(entity, event.message)
            except Exception as e:
                print(f"Error forwarding to {target_group}: {str(e)}")

async def main():
    await client.start()
    print("Bot is running...")
    
    # Preload target group entities at startup
    await preload_entities()
    
    await client.run_until_disconnected()

if __name__ == '__main__':
    client.loop.run_until_complete(main())