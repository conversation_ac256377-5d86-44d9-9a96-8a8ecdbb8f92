import requests
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Direct API key
API_KEY = "Oimh9SgRhHM3cW7bf430YQnXEN14tiBN"

# Base URL - using the studio endpoint
BASE_URL = "https://api.ai21.com/studio/v1"

# Define models with correct names
MODELS = [
    "j2-ultra",
    "j2-mid",
    "j2-light"
]

# Test prompt
TEST_PROMPT = "Say hello in one word."

def test_model(model_name: str, prompt: str) -> None:
    """
    Test a single model with the given prompt.
    """
    try:
        logger.info(f"Testing model: {model_name}")
        
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "prompt": prompt,
            "numResults": 1,
            "maxTokens": 50,
            "temperature": 0.7,
            "topP": 1
        }
        
        response = requests.post(
            f"{BASE_URL}/{model_name}/complete",
            headers=headers,
            json=data
        )
        
        if response.status_code == 200:
            result = response.json()
            completion = result.get('completions', [{}])[0].get('data', {}).get('text', '')
            logger.info(f"Response from {model_name}:\n{completion}")
        else:
            logger.warning(f"Error response from {model_name}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing {model_name}: {str(e)}")

def main():
    """
    Main function to test all available AI21 models.
    """
    logger.info("Starting AI21 models test")
    
    for model in MODELS:
        test_model(model, TEST_PROMPT)
        logger.info("-" * 50)
    
    logger.info("Completed testing all models")

if __name__ == "__main__":
    main()
