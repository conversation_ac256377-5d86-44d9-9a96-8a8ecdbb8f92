import os
import requests
import logging
from quick_test import test_codestral

# Set the API key as an environment variable
os.environ['MISTRAL_API_KEY'] = 'AMpMVqFQPpsTTzsq9HVqUWEs2jVEXcPw'

# Set up logging for debugging
logging.basicConfig(filename='model_check.log', level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# Use the Mistral API endpoint to get the list of working models
api_key = os.getenv('MISTRAL_API_KEY')
if not api_key:
    print("Error: MISTRAL_API_KEY environment variable is not set.")
    exit(1)

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

logging.debug(f"Request headers: {headers}")

response = requests.get("https://api.mistral.ai/v1/models", headers=headers)
logging.debug(f"Response status code: {response.status_code}")
logging.debug(f"Response text: {response.text}")

if response.status_code != 200:
    print(f"Error: {response.status_code} - {response.text}")
    exit(1)

models = response.json().get("models", [])

# Extract the list of model IDs from the response
models = [model["id"] for model in response.json()["data"]]

# Check if each model is working
for model in models:
    print(f"Checking model: {model}")
    # Make an API call to check if the model is working
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Are you working?"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        logging.debug(f"Checking model: {model}")
        retries = 3
        for attempt in range(retries):
            try:
                response = requests.post(f"https://api.mistral.ai/v1/chat/completions", headers=headers, json=data)
                logging.debug(f"Request: POST https://api.mistral.ai/v1/chat/completions")
                logging.debug(f"Response: {response.status_code} - {response.text}")
                if response.status_code == 200:
                    response_data = response.json()
                    if "choices" in response_data and len(response_data["choices"]) > 0:
                        message = response_data["choices"][0]["message"]["content"]
                        if "yes" in message.lower() or "working" in message.lower():
                            print(f"Model {model} is working")
                            with open('working_models.txt', 'a') as f:
                                f.write(f"{model}\n")
                        else:
                            print(f"Model {model} is not working. Response: {message}")
                    else:
                        print(f"Model {model} did not provide a valid response.")
                    break
                elif response.status_code == 404:
                    print(f"Model {model} not found. Status code: {response.status_code}")
                    break
                elif response.status_code == 429:
                    if attempt < retries - 1:
                        wait_time = 2 ** attempt
                        print(f"Rate limit exceeded. Retrying in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        print(f"Model {model} is not working. Status code: {response.status_code}")
                        break
                else:
                    print(f"Model {model} is not working. Status code: {response.status_code}")
                    break
            except requests.exceptions.RequestException as e:
                if attempt < retries - 1:
                    wait_time = 2 ** attempt
                    print(f"Error checking model {model}: {e}. Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    print(f"Error checking model {model}: {e}")
                    break
