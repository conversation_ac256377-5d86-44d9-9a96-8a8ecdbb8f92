import requests
import time
import json
from typing import Optional, List, Dict, Any

class NaasAPIError(Exception):
    """Base exception for Naas API errors"""
    def __init__(self, message: str, code: int = None, context: str = None):
        self.message = message
        self.code = code
        self.context = context
        super().__init__(self.message)

class NaasAIClient:
    """Client for interacting with the Naas AI Models API"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.naas.ai/v1"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def _handle_response(self, response: requests.Response) -> Dict:
        """Handle API response and raise appropriate exceptions"""
        try:
            response.raise_for_status()
            data = response.json()
            
            # Check for API-level errors in successful responses
            if "error" in data and data["error"].get("code"):
                raise NaasAPIError(
                    message=data["error"].get("message", "Unknown error"),
                    code=data["error"].get("code"),
                    context=data["error"].get("context")
                )
            return data
        except requests.exceptions.HTTPError as e:
            error_msg = str(e)
            try:
                error_data = response.json()
                if "error" in error_data:
                    error_msg = error_data["error"].get("message", str(e))
            except:
                pass
            raise NaasAPIError(f"HTTP Error: {error_msg}", response.status_code)
        except requests.exceptions.RequestException as e:
            raise NaasAPIError(f"Request failed: {str(e)}")

    def list_models(self, page_size: int = 20, page_number: int = 0) -> Dict:
        """List available AI models"""
        url = f"{self.base_url}/aimodels"
        params = {
            "page_size": page_size,
            "page_number": page_number
        }
        response = requests.get(url, headers=self.headers, params=params)
        return self._handle_response(response)

    def get_model(self, model_id: str) -> Dict:
        """Get details for a specific AI model"""
        url = f"{self.base_url}/aimodels"
        payload = {"id": model_id}
        response = requests.post(url, headers=self.headers, json=payload)
        return self._handle_response(response)

    def create_completion(
        self,
        model_id: str,
        messages: List[Dict[str, str]],
        max_tokens: int = 50,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict:
        """Create a completion using the specified model"""
        url = f"{self.base_url}/chat/completions"
        payload = {
            "model": model_id,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            **kwargs
        }
        response = requests.post(url, headers=self.headers, json=payload)
        return self._handle_response(response)

def test_models(api_key: str, test_message: str = "Hello world") -> None:
    """Test available models with a sample message"""
    client = NaasAIClient(api_key)
    
    print("Fetching available models...")
    try:
        models_response = client.list_models()
        models = models_response.get("aimodels", [])
        
        if not models:
            print("No models found")
            return
            
        print(f"\nFound {len(models)} models:")
        results = {}
        
        for model in models:
            model_id = model.get("id")
            if not model_id:
                continue
                
            print(f"\nTesting model: {model_id}")
            try:
                completion = client.create_completion(
                    model_id=model_id,
                    messages=[{"role": "user", "content": test_message}]
                )
                
                # Extract response text
                response_text = ""
                if "completion" in completion:
                    if "choices" in completion["completion"]:
                        response_text = completion["completion"]["choices"][0].get("message", {}).get("content", "")
                    elif "completions" in completion["completion"]:
                        response_text = completion["completion"]["completions"][0] if completion["completion"]["completions"] else ""
                
                results[model_id] = {
                    "success": True,
                    "response": response_text,
                    "usage": completion.get("completion", {}).get("usage", {})
                }
                print(f"Success! Response: {response_text[:200]}...")
                
            except NaasAPIError as e:
                print(f"Error testing {model_id}: {e.message}")
                results[model_id] = {
                    "success": False,
                    "error": e.message
                }
            except Exception as e:
                print(f"Unexpected error testing {model_id}: {str(e)}")
                results[model_id] = {
                    "success": False,
                    "error": str(e)
                }
            
            time.sleep(2)  # Rate limiting precaution
            
        # Save results
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"naas_model_results_{timestamp}.json"
        with open(filename, "w") as f:
            json.dump(results, f, indent=2)
        print(f"\nResults saved to {filename}")
        
    except NaasAPIError as e:
        print(f"API Error: {e.message}")
        if e.context:
            print(f"Context: {e.context}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")

if __name__ == "__main__":
    # Your API key
    API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzNDk4YWE5MC1kNTc4LTRkOGUtOWRkOS0wYjAxMmQxYjU1ZWIiLCJhcGlfa2V5X2lkIjoiMzE0OTcxZWEtNzU2NC00M2NjLTlhMTctMGVlMzRjNTA3MTQwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDQtMTJUMDk6MjE6NTMuODUwODA2KzAwOjAwIn0.0irR8V6GseNppTqfhDnENiJB54kpvXD0eF5sXDPxRjM"
    
    # Test the models
    test_models(API_KEY, "Once upon a time")