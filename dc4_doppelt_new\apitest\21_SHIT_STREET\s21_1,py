import requests
import os

# Replace with your actual AI21 API key
API_KEY = os.environ.get("Oimh9SgRhHM3cW7bf430YQnXEN14tiBN")
if not API_KEY:
    print("Error: AI21_API_KEY environment variable not set.")
    exit()

headers = {
    'Authorization': f'Bearer {API_KEY}',
    'Content-Type': 'application/json'
}

models_and_endpoints = {
    "jamba-large": "https://api.ai21.com/v1/chat/completions",
    "jamba-mini": "https://api.ai21.com/v1/chat/completions"
    # Add other models and their corresponding chat completion endpoints here if available
}

def test_model_endpoint(model_name, endpoint_url):
    """Tests a given AI21 model endpoint with a simple request."""
    print(f"Testing model: {model_name} at endpoint: {endpoint_url}")
    payload = {
        "model": model_name,
        "messages": [
            {"role": "user", "content": "Hello, AI!"}
        ],
        "max_tokens": 20,
        "temperature": 0.7
    }
    try:
        response = requests.post(endpoint_url, headers=headers, json=payload)
        response.raise_for_status()  # Raise an exception for bad status codes
        result = response.json()
        if 'choices' in result and result['choices']:
            print(f"  Success! Response received from {model_name}:")
            print(f"  {result['choices'][0]['message']['content'].strip()}")
        else:
            print(f"  Warning: No 'choices' found in the response from {model_name}.")
            print(f"  Response details: {result}")
    except requests.exceptions.RequestException as e:
        print(f"  Error: Request failed for {model_name} - {e}")
    except ValueError as e:
        print(f"  Error: Could not decode JSON response for {model_name} - {e}")
    print("-" * 30)

if __name__ == "__main__":
    print("Starting AI21 Studio Model Endpoint Tests...")
    for model, endpoint in models_and_endpoints.items():
        test_model_endpoint(model, endpoint)
    print("All tests completed.")