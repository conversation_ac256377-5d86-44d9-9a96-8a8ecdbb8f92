import requests
import time
# Set your API key (replace with your actual key or set as environment variable)
api_key = "de7f1714-90fa-4228-b1c6-0356aed00c4a:6f1666b3a94c64ef18a298f20dd7837b"

# List of available models
models = [
    # Large Language Models (alphabetically ordered)
    "anthropic/claude-3-5-haiku",
    "anthropic/claude-3-haiku",
    "anthropic/claude-3.5-sonnet",
    "deepseek/deepseek-r1",
    "google/gemini-flash-1.5",
    "google/gemini-flash-1.5-8b",
    "google/gemini-pro-1.5",
    "gpt-3.5-turbo",
    "gpt-4",
    "hermes-3-70b",
    "inflection-2.5",
    "llama-2-70b-chat",
    "meta-llama/llama-3.1-70b-instruct",
    "meta-llama/llama-3.1-8b-instruct",
    "meta-llama/llama-3.2-1b-instruct",
    "meta-llama/llama-3.2-3b-instruct",
    "meta-llama/llama-3.2-90b-vision-instruct",
    "openai/gpt-4o",
    "openai/gpt-4o-mini",
    "qwen-2.5-72b-instruct",
    "fal-ai/any-llm",
    "fal-ai/cartoonify",
    "fal-ai/dia-tts",
    "fal-ai/fast-lcm-diffusion",
    "fal-ai/fast-sdxl",
    "fal-ai/fast-turbo-diffusion",
    "fal-ai/finegrain-eraser",
    "fal-ai/flux-lora",
    "fal-ai/flux-lora-portrait-trainer",
    "fal-ai/flux-pro/v1.1-ultra",
    "fal-ai/flux/dev",
    "fal-ai/framepack",
    "fal-ai/hidream-i1-dev",
    "fal-ai/hidream-i1-fast",
    "fal-ai/hidream-i1-full",
    "fal-ai/hyper-sdxl",
    "fal-ai/ideogram/v2",
    "fal-ai/kling-video/v2/master/image-to-video",
    "fal-ai/magi-distilled",
    "fal-ai/minimax-image/subject-reference",
    "fal-ai/recraft-v3",
    "fal-ai/smart-turn",
    "fal-ai/stable-diffusion-v35-large",
    "fal-ai/turbo-flux-trainer",
    "fal-ai/wan-effects",
    "fal-ai/wan-flf2v",
    "fal-ai/wan-pro/image-to-video"
]

# Test message
test_message = {
    "prompt": "only say a number between 1 and 10"
}

# Iterate through each model and send a request
for i, model in enumerate(models):
    print(f"\nTesting model: {model}")
    data = {
        "input": test_message,
        "model": model,
        "max_tokens": 50,  # Limit response to save tokens
        "temperature": 0.7,
        "top_p": 0.9
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Key {api_key}"
    }
    try:
        response = requests.post(f"https://api.fal.ai/v1/models/{model}", headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"Response from {model}: {result['data']['output']}")
    except Exception as e:
        print(f"Error with model {model}: {e}")
    if i < len(models) - 1:  # Avoid delay after the last model
        print("Waiting 20 seconds before next model...")
        time.sleep(20)
