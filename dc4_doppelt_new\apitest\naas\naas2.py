import requests
import time

API_KEY = "your_api_key_here"
BASE_URL = "https://api.naas.ai"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# Correct endpoint paths
MODELS_ENDPOINT = "/api/v1/models"  # Verified endpoint for model listing
CHAT_ENDPOINT = "/api/v1/chat/completions"  # Correct chat endpoint

KNOWN_MODELS = [
    "gpt-4", "gpt-3.5-turbo", "claude-2", "gemini-pro",
    "mistral-7b", "llama-2-70b", "palm-2", "j2-light"
]

def fetch_models():
    """Fetch available models from Naas.ai using correct endpoint"""
    try:
        response = requests.get(
            f"{BASE_URL}{MODELS_ENDPOINT}",
            headers=headers
        )
        response.raise_for_status()
        data = response.json()
        
        # Extract model IDs from response (adjust based on actual response structure)
        return [model["id"] for model in data.get("data", [])] or KNOWN_MODELS
        
    except requests.exceptions.RequestException as e:
        print(f"Error fetching models: {e}")
        return KNOWN_MODELS

def test_model(model_id):
    """Test a model using the correct chat endpoint"""
    payload = {
        "model": model_id,
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Complete this story: Once upon a time"}
        ],
        "max_tokens": 50
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}{CHAT_ENDPOINT}",
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        result = response.json()
        
        # Correct response parsing for Naas API
        return result.get("choices", [{}])[0].get("message", {}).get("content", "No response generated")
        
    except requests.exceptions.HTTPError as e:
        print(f"HTTP Error with model '{model_id}': {e}")
        if response.status_code == 404:
            print("Model not found or endpoint incorrect")
        return None
    except Exception as e:
        print(f"General error testing model {model_id}: {e}")
        return None

def main():
    print("Fetching available models...")
    models = fetch_models()

    if not models:
        print("No models found. Exiting.")
        return

    print(f"Found {len(models)} models:")
    for model in models:
        print(f"- {model}")

    print("\nTesting models with sample prompt...")
    results = {}
    for model in models:
        print(f"\nTesting model: {model}")
        result = test_model(model)
        if result:
            print(f"Response: {result[:200]}...")  # Show first 200 chars
            results[model] = result
        time.sleep(1)  # Respect rate limits

    # Save results
    with open("model_results.json", "w") as f:
        import json
        json.dump(results, f, indent=2)
    print("\nResults saved to model_results.json")

if __name__ == "__main__":
    main()