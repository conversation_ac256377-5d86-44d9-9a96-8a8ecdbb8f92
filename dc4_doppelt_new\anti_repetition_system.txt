# Anti-Repetition System for Telegram Bot

## Problem Analysis

After implementing the enhanced context handling and response quality improvements, we identified several issues with repetitive responses:

1. **Repetitive Phrases**: The bot was repeatedly using the same phrases like "I ain't got time for this"
2. **Duplicate Topic Triggers**: The bot sometimes sent the same frequency-triggered message twice
3. **Similar Responses**: The bot's responses were often too similar to previous ones
4. **Character Catchphrases**: Character catchphrases were being overused

## Solution Implemented

We've implemented a comprehensive anti-repetition system with several components:

### 1. Response Tracking

- Added a `recent_responses` dictionary to track recent responses by group
- Implemented a maximum of 10 recent responses per group
- Created functions to add and check responses against this history

### 2. Similarity Detection

- Implemented `is_similar_to_recent_responses()` function to detect similar responses
- Added specific checks for common repetitive phrases
- Implemented Jaccard similarity calculation to detect overall response similarity
- Set a configurable threshold (0.7) for considering responses similar

### 3. Trigger Word Cooldown

- Added a `recent_triggers` dictionary to track recently triggered words
- Implemented a 5-minute cooldown period for each trigger word
- Created functions to check and update trigger timestamps
- Prevents the same word from triggering multiple responses in quick succession

### 4. Response Regeneration

- When a repetitive response is detected, the system attempts to regenerate it
- Uses a higher temperature setting (0.9 instead of 0.7) for more variety
- If still repetitive after regeneration, either skips the response (for frequency triggers) or uses it with a warning (for direct responses)

### 5. Phrase Blacklisting

- Added specific checks for common repetitive phrases:
  - "i ain't got time for this"
  - "i'm on a mission"
  - "if you ain't got nothing important to say"
  - "beat it"
  - "what's your problem"
  - "i ain't here to play"
  - "justice is my game"
  - "i'll make them pay"
  - "redemption is a bitch"

## Implementation Details

### Key Functions Added

1. **is_similar_to_recent_responses(group_id, response)**
   - Checks if a response is similar to recently sent responses
   - Uses both phrase matching and overall similarity calculation
   - Returns True if similar, False otherwise

2. **add_to_recent_responses(group_id, response)**
   - Adds a response to the list of recent responses for a group
   - Maintains a maximum of 10 recent responses per group

3. **is_trigger_on_cooldown(group_id, trigger_word)**
   - Checks if a trigger word is on cooldown for a specific group
   - Uses timestamps to implement a 5-minute cooldown period

4. **update_trigger_timestamp(group_id, trigger_word)**
   - Updates the timestamp for a trigger word in a specific group
   - Used to start the cooldown period for a trigger word

### Changes to Response Generation

1. **Word Frequency Response Handler**
   - Added cooldown check before generating responses
   - Added similarity check after generating responses
   - Implemented response regeneration with higher temperature
   - Added tracking of recent responses and triggers

2. **Direct Response Handler**
   - Added similarity check after generating responses
   - Implemented response regeneration with higher temperature
   - Added tracking of recent responses

3. **Response Generation Function**
   - Added temperature parameter for more control over response variety
   - Updated API calls to use the provided temperature

## Expected Benefits

These improvements should result in:

1. **More Varied Responses**: The bot will avoid repeating the same phrases
2. **No Duplicate Triggers**: The cooldown system prevents the same word from triggering multiple responses
3. **Less Repetitive Character Portrayal**: Character catchphrases will be used more sparingly
4. **More Natural Conversation Flow**: Responses will be more varied and less predictable
5. **Reduced Spam**: The bot won't send multiple similar messages in quick succession

## Configuration Options

The anti-repetition system includes several configurable parameters:

- `MAX_RECENT_RESPONSES`: Number of recent responses to remember per group (default: 10)
- `RESPONSE_SIMILARITY_THRESHOLD`: Threshold for considering responses similar (default: 0.7)
- `TRIGGER_COOLDOWN`: Seconds to wait before responding to the same trigger word again (default: 300)

These parameters can be adjusted to fine-tune the system's behavior based on user preferences.

## Conclusion

The anti-repetition system significantly enhances the bot's conversation quality by preventing repetitive responses and ensuring more natural, varied interactions. Combined with the previous improvements to context handling and response quality, this creates a much more engaging and natural conversational experience.
