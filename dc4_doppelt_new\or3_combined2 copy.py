"""
SIMPLIFIED TELEGRAM BOT
======================

This script implements a Telegram bot that:
1. Responds to messages in configured groups
2. Maintains conversation context
3. Supports character profiles for different personalities
4. Uses multiple API providers with fallback and retry logic
5. Preserves the original message across provider attempts

Features:
- Direct message processing
- Character switching based on triggers
- Conversation memory for context
- Fallback to next API provider on failure
- Retry logic for transient errors
- Minimal typing indicators

Dependencies:
- telethon
- httpx
- json
- asyncio
- time
- re

Usage:
1. Configure in config.txt with API keys, group IDs, and provider settings
2. Run the script
3. <PERSON><PERSON> responds in specified groups

Version: 2.2
"""

from telethon import TelegramClient, events
import httpx
import json
import asyncio
import time
import re
import os
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Any, Optional, List
try:
    import openai
except ImportError:
    print("[WARNING] OpenAI package not installed. Some features may not work properly.")

# Configure logging
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Create a logger
logger = logging.getLogger('telegram_bot')
logger.setLevel(logging.DEBUG)

# Create console handler with a higher log level
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(log_formatter)
logger.addHandler(console_handler)

# Create file handler which logs even debug messages
# Use rotating file handler to prevent logs from growing too large
file_handler = RotatingFileHandler('bot_debug.log', maxBytes=10*1024*1024, backupCount=5)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(log_formatter)
logger.addHandler(file_handler)

# Create a separate file for API request/response logs
api_logger = logging.getLogger('api_logs')
api_logger.setLevel(logging.DEBUG)
api_file_handler = RotatingFileHandler('api_debug.log', maxBytes=10*1024*1024, backupCount=5)
api_file_handler.setLevel(logging.DEBUG)
api_file_handler.setFormatter(log_formatter)
api_logger.addHandler(api_file_handler)
api_logger.addHandler(console_handler)  # Also show API logs in console

# Function to log to both console and file
def log_info(message):
    print(f"[INFO] {message}")
    logger.info(message)

def log_warning(message):
    print(f"[WARNING] {message}")
    logger.warning(message)

def log_error(message):
    print(f"[ERROR] {message}")
    logger.error(message)

def log_debug(message):
    print(f"[DEBUG] {message}")
    logger.debug(message)

def log_api_debug(message):
    print(f"[DEBUG] {message}")
    api_logger.debug(message)

# ===== GLOBAL VARIABLES AND SETTINGS =====

conversation_memory = []
max_memory_items = 10
current_character = "default"
character_profiles = {}
runtime_system_prompt = None

log_info("Starting simplified Telegram bot...")

# ===== API PROVIDER CLASSES =====

class APIProvider:
    """Base class for API providers"""
    def __init__(self, api_key: str, model: str):
        self.api_key = api_key
        self.model = model

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        raise NotImplementedError("Each API provider must implement this method")

    @staticmethod
    def get_providers(config: Dict[str, str]) -> List['APIProvider']:
        """Return a list of initialized API providers"""
        provider_order = config.get('PROVIDER_ORDER', 'openrouter').lower().split(',')
        providers = []

        for provider_name in provider_order:
            provider_name = provider_name.strip()
            api_key = None
            model = config.get('MODEL', '')

            if provider_name == 'openrouter':
                api_key = config.get('OPENROUTER_API_KEY', '')
                model = config.get('OPENROUTER_MODEL', '')
            elif provider_name == 'cerebras':
                api_key = config.get('CEREBRAS_API_KEY', '')
                model = config.get('CEREBRAS_MODEL', '')
            elif provider_name == 'cohere':
                api_key = config.get('COHERE_API_KEY', '')
                model = config.get('COHERE_MODEL', '')
            elif provider_name == 'mistral':
                api_key = config.get('MISTRAL_API_KEY', '')
                model = config.get('MISTRAL_MODEL', '')
            elif provider_name == 'codestral':
                api_key = config.get('CODESTRAL_API_KEY', '')
                model = config.get('CODESTRAL_MODEL', '')
            elif provider_name == 'lamini':
                api_key = config.get('LAMINI_API_KEY', '')
                model = config.get('LAMINI_MODEL', '')
            else:
                log_warning(f"Unknown API provider: {provider_name}")
                continue

            if not api_key:
                log_warning(f"API key missing for provider: {provider_name}")
                continue

            try:
                provider_instance = _get_provider_instance(provider_name, api_key, model)
                providers.append(provider_instance)
                log_info(f"Initialized provider: {provider_name}")
            except Exception as e:
                log_error(f"Failed to initialize provider {provider_name}: {e}")

        if not providers:
            raise ValueError("No API providers could be initialized.")
        return providers

def _get_provider_instance(provider_name: str, api_key: str, model: str) -> 'APIProvider':
    if provider_name == 'openrouter':
        return OpenRouterProvider(api_key, model)
    elif provider_name == 'cerebras':
        return CerebrasProvider(api_key, model)
    elif provider_name == 'cohere':
        return CohereProvider(api_key, model)
    elif provider_name == 'mistral':
        return MistralProvider(api_key, model)
    elif provider_name == 'codestral':
        return CodestralProvider(api_key, model)
    elif provider_name == 'lamini':
        return LaminiProvider(api_key, model)
    raise ValueError(f"Unknown API provider: {provider_name}")

class OpenRouterProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"OpenRouter request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=30) as client:
                log_api_debug(f"Sending request to OpenRouter API...")
                response = client.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                log_api_debug(f"OpenRouter response status: {response.status_code}")
                log_api_debug(f"OpenRouter response headers: {response.headers}")

                # Always log the complete response content for debugging
                log_api_debug(f"OpenRouter API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"OpenRouter API returned status {response.status_code}")
                    return f"__ERROR__: OpenRouter API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"OpenRouter API error: {str(e)}")
            return f"__ERROR__: OpenRouter API error: {str(e)}"

class CerebrasProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"Cerebras request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=30) as client:
                log_api_debug(f"Sending request to Cerebras API...")
                response = client.post(
                    "https://api.cerebras.ai/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Cerebras response status: {response.status_code}")
                log_api_debug(f"Cerebras response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Cerebras API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Cerebras API returned status {response.status_code}")
                    return f"__ERROR__: Cerebras API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Cerebras API error: {str(e)}")
            return f"__ERROR__: Cerebras API error: {str(e)}"

class CohereProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # Cohere API v2 expects different parameter names than OpenAI-compatible APIs
        data = {
            "chat_history": [],  # We'll build this from system prompt and context
            "message": prompt,
            "model": self.model,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        # Add system prompt as a preamble message if provided
        if system_prompt.strip():
            data["preamble"] = system_prompt

        log_api_debug(f"Cohere request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=30) as client:
                log_api_debug(f"Sending request to Cohere API...")
                response = client.post(
                    "https://api.cohere.ai/v1/chat",  # Using v1 endpoint which is more stable
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Cohere response status: {response.status_code}")
                log_api_debug(f"Cohere response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Cohere API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Cohere API returned status {response.status_code}")
                    return f"__ERROR__: Cohere API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            # Extract response based on Cohere's v1 API response format
            if "text" in result:
                return result["text"]
            elif "reply" in result:
                return result["reply"]
            elif "response" in result and "text" in result["response"]:
                return result["response"]["text"]
            elif "generations" in result and len(result["generations"]) > 0:
                return result["generations"][0]["text"]

            # If we can't find the response in the expected fields, return an error
            log_error(f"Could not parse Cohere response format: {json.dumps(result)}")
            return f"__ERROR__: Could not parse Cohere response format: {json.dumps(result)}"

        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Cohere API error: {str(e)}")
            return f"__ERROR__: Cohere API error: {str(e)}"

class MistralProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"Mistral request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=30) as client:
                log_api_debug(f"Sending request to Mistral API...")
                response = client.post(
                    "https://api.mistral.ai/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Mistral response status: {response.status_code}")
                log_api_debug(f"Mistral response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Mistral API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Mistral API returned status {response.status_code}")
                    return f"__ERROR__: Mistral API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Mistral API error: {str(e)}")
            return f"__ERROR__: Mistral API error: {str(e)}"

class CodestralProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"Codestral request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=30) as client:
                log_api_debug(f"Sending request to Codestral API...")
                response = client.post(
                    "https://codestral.mistral.ai/v1/chat/completions",  # Correct endpoint for Codestral
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Codestral response status: {response.status_code}")
                log_api_debug(f"Codestral response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Codestral API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Codestral API returned status {response.status_code}")
                    return f"__ERROR__: Codestral API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Codestral API error: {str(e)}")
            return f"__ERROR__: Codestral API error: {str(e)}"

class LaminiProvider(APIProvider):
    def __init__(self, api_key: str, model: str, api_url: str = "https://api.lamini.ai/inf"):
        self.api_key = api_key
        self.model = model
        self.api_url = api_url

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        log_api_debug(f"Lamini messages: {json.dumps(messages, indent=2)}")

        # Try using OpenAI client first (preferred method)
        try:
            import openai
            client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.api_url,
            )

            log_api_debug(f"Using OpenAI client for Lamini API")
            log_api_debug(f"Lamini request parameters: model={self.model}, temperature={temperature}, max_tokens={max_tokens}")

            try:
                log_api_debug(f"Sending request to Lamini API via OpenAI client...")
                response = client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                log_api_debug(f"Lamini API response via OpenAI client: {response}")
                return response.choices[0].message.content
            except Exception as e:
                import traceback
                tb = traceback.format_exc()
                log_api_debug(f"Exception traceback (OpenAI client): {tb}")
                log_error(f"Lamini API error (OpenAI client): {str(e)}")
                return f"__ERROR__: Lamini API error (OpenAI client): {str(e)}"

        # Fall back to httpx if OpenAI client is not available
        except ImportError:
            log_info("OpenAI package not installed. Using httpx fallback for Lamini API.")
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "OpenAI-Python/1.0",
                "Accept": "application/json"
            }

            data = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            endpoint = f"{self.api_url}/v1/chat/completions"

            log_api_debug(f"Lamini request data (httpx fallback): {json.dumps(data, indent=2)}")

            try:
                with httpx.Client(timeout=30, follow_redirects=True) as client:
                    log_api_debug(f"Sending request to Lamini API via httpx...")
                    response = client.post(endpoint, headers=headers, json=data)

                    log_api_debug(f"Lamini response status: {response.status_code}")
                    log_api_debug(f"Lamini response headers: {response.headers}")

                    # Always log the complete response content for debugging without truncation
                    log_api_debug(f"Lamini API response: {response.text}")

                    if response.status_code != 200:
                        log_error(f"Lamini API returned status {response.status_code}")
                        return f"__ERROR__: Lamini API returned status {response.status_code}: {response.text}"

                    response.raise_for_status()

                result = response.json()
                log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

                return result["choices"][0]["message"]["content"]
            except Exception as e:
                import traceback
                tb = traceback.format_exc()
                log_api_debug(f"Exception traceback (httpx): {tb}")
                log_error(f"Lamini API error (httpx): {str(e)}")
                return f"__ERROR__: Lamini API error (httpx): {str(e)}"

# ===== CONFIGURATION FUNCTIONS =====

def load_config(path="config.txt"):
    config = {}
    current_key = None
    multi_line_value = ""
    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""
                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():
                multi_line_value += " " + line.strip()
    if current_key and multi_line_value:
        config[current_key] = multi_line_value.strip()
    return config

# Load configuration
config = load_config()
log_info("Using config.txt for configuration")

api_id = int(config["API_ID"])
api_hash = config["API_HASH"]
group_ids = [int(x.strip()) for x in config["GROUP_IDS"].split(",")]
system_prompt = config.get("SYSTEM_PROMPT", "").strip()
runtime_system_prompt = system_prompt

log_info(f"System prompt length: {len(system_prompt)} characters")

# ===== CHARACTER PROFILE MANAGEMENT =====

def load_character_profiles(profiles_file="characters.json"):
    global character_profiles
    try:
        if os.path.exists(profiles_file):
            with open(profiles_file, "r") as f:
                character_profiles = json.load(f)
            log_info(f"Loaded {len(character_profiles)} character profiles from {profiles_file}")
        else:
            character_profiles = {}
            with open(profiles_file, "w") as f:
                json.dump(character_profiles, f, indent=4)
            log_info(f"Created new empty character profiles file")
    except Exception as e:
        log_error(f"Failed to load character profiles: {e}")
        character_profiles = {}
    for character_id, profile in character_profiles.items():
        log_info(f"Loaded profile: {profile['name']} ({character_id}) - {len(profile['triggers'])} triggers")

def switch_character(character_id):
    global current_character, runtime_system_prompt, system_prompt
    if character_id in character_profiles:
        current_character = character_id
        if system_prompt:
            runtime_system_prompt = f"{system_prompt}\n\nCHARACTER PROFILE:\n{character_profiles[character_id]['prompt']}"
        else:
            runtime_system_prompt = character_profiles[character_id]["prompt"]
        log_info(f"Switched to character profile: {character_profiles[character_id]['name']} ({character_id})")
        return True
    log_warning(f"Character profile not found: {character_id}")
    return False

def detect_character_trigger(message):
    message_lower = message.lower()
    if "be a " in message_lower or "act like a " in message_lower or "talk like a " in message_lower:
        for prefix in ["be a ", "act like a ", "talk like a ", "be an ", "act like an "]:
            if prefix in message_lower:
                match = re.search(f"{prefix}([^.!?]+)[.!?]*", message_lower)
                if match:
                    full_description = match.group(1).strip()
                    words = full_description.split()
                    core_type = ' '.join(words[:min(2, len(words))])
                    log_debug(f"Detected character request: '{full_description}' (core type: '{core_type}')")
                    for char_id, profile in character_profiles.items():
                        if core_type in profile["name"].lower() or core_type in profile["description"].lower():
                            return char_id
                    log_info(f"No existing character matches '{core_type}'")
                    return None
    for char_id, profile in character_profiles.items():
        for trigger in profile["triggers"]:
            if re.search(r'\b' + re.escape(trigger) + r'\b', message_lower):
                return char_id
    return None

def create_character(description, created_by="user", api_providers=None):
    global character_profiles
    api_providers = api_providers or globals().get('api_providers', [])
    if not api_providers:
        log_error(f"No API providers available for character creation")
        return None
    try:
        words = description.lower().split()
        character_type = ' '.join(words[:min(3, len(words))])
        character_id = re.sub(r'[^a-z0-9]', '_', character_type)
        if character_id in character_profiles:
            character_id = f"{character_id}_{int(time.time())}"
        log_info(f"Creating new character: {description} (ID: {character_id})")
        character_prompt = f"""
Create a CONCISE character profile for '{description}' using bullet points and minimal text.

Include:
1. NAME: A fitting name (short and memorable).
2. BACKSTORY: 1-2 sentences maximum about origin and key events.
3. PERSONALITY: 3-5 key traits or quirks (bullet points).
4. APPEARANCE: 2-3 distinctive features (bullet points).
5. BEHAVIORS: 2-3 specific habits (bullet points).
6. CATCHPHRASES: 1-3 short, distinctive phrases.
7. VOICE: 2-3 speech characteristics (bullet points).

IMPORTANT: Be extremely concise. Use bullet points. Avoid lengthy descriptions and narratives.
Focus on distinctive traits that define the character's speech and behavior.
"""
        detailed_profile = None
        for provider in api_providers:
            log_info(f"Trying provider for character creation: {provider.__class__.__name__}")
            try:
                detailed_profile = provider.generate_response(
                    prompt=character_prompt,
                    system_prompt="You are a character profile creator specializing in detailed, immersive profiles.",
                    temperature=0.8,
                    max_tokens=2000
                )
                if not detailed_profile.startswith("__ERROR__"):
                    log_info(f"Successfully generated character profile with {provider.__class__.__name__}")
                    break
                else:
                    log_warning(f"Provider {provider.__class__.__name__} failed: {detailed_profile}")
            except Exception as e:
                log_error(f"Provider {provider.__class__.__name__} failed: {e}")
        if not detailed_profile or detailed_profile.startswith("__ERROR__"):
            log_error(f"All providers failed to create character profile")
            return None
        name_match = re.search(r'NAME:?\s*([^\n]+)', detailed_profile)
        character_name = name_match.group(1).strip() if name_match else description.split()[0].title()
        prompt = f"""You are {description}.

{detailed_profile}

ROLEPLAY GUIDELINES:
1. Act as {description} with described traits and speech patterns
2. Use character's catchphrases and speech style
3. Stay in character while being helpful
4. If asked about being AI, acknowledge briefly but remain in character"""
        triggers = []
        for word in character_name.lower().split():
            if len(word) > 2 and word not in triggers:
                triggers.append(word)
        for word in description.lower().split():
            if len(word) > 3 and word not in ["the", "and", "with", "that", "this", "from", "like", "have"] and word not in triggers:
                triggers.append(word)
        if character_id not in triggers:
            triggers.append(character_id)
        if len(triggers) < 2 and description.split():
            for word in description.split()[:2]:
                clean_word = re.sub(r'[^a-z0-9]', '', word.lower())
                if clean_word and clean_word not in triggers:
                    triggers.append(clean_word)
        character_profiles[character_id] = {
            "name": character_name,
            "description": description,
            "prompt": prompt,
            "triggers": triggers,
            "created_by": created_by
        }
        with open("characters.json", "w") as f:
            json.dump(character_profiles, f, indent=4)
        log_info(f"Successfully created character: {character_name} (ID: {character_id})")
        log_info(f"Triggers: {', '.join(triggers)}")
        return character_id
    except Exception as e:
        log_error(f"Failed to create character: {e}")
        return None

# ===== SYSTEM PROMPT MANAGEMENT =====

def update_system_prompt(new_content):
    global runtime_system_prompt
    runtime_system_prompt = new_content
    log_info(f"Updated runtime system prompt: {len(runtime_system_prompt)} characters")

def extract_topics(message):
    topics = []
    if len(message) < 10:
        return topics
    words = message.split()
    for word in words:
        if (len(word) > 4 and word[0].isupper() and
            word.lower() not in ['this', 'that', 'these', 'those', 'there', 'their', 'about']):
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word and len(clean_word) > 4:
                topics.append(clean_word)
    return topics

# ===== INITIALIZATION =====

# Initialize API providers
try:
    api_providers = APIProvider.get_providers(config)
    provider_names = [p.__class__.__name__ for p in api_providers]
    log_info(f"Initialized {len(api_providers)} providers: {', '.join(provider_names)}")
except Exception as e:
    log_error(f"Failed to initialize API providers: {e}")
    exit(1)

# Initialize Telegram client
log_info(f"Listening to groups: {group_ids}")
client = TelegramClient('user_session', api_id, api_hash)

# ===== MESSAGE HANDLER =====

@client.on(events.NewMessage(chats=group_ids))
async def on_new_message(event):
    """Handle new messages in monitored groups"""
    try:
        # Skip non-text messages, messages from self, and empty messages
        if event.message.media or event.message.action:
            return

        me = await client.get_me()
        if event.sender_id == me.id:
            return

        user_msg = event.raw_text.strip() if event.raw_text else ""
        if not user_msg:
            return

        log_info(f"Message from {event.sender.username or event.sender_id}: {user_msg}")

        # Check for character switching
        new_character = detect_character_trigger(user_msg)
        if new_character and new_character != current_character:
            switch_character(new_character)

        # Check for character creation requests
        character_request_patterns = [
            r"be a ([^.!?]+)[.!?]*",
            r"be an ([^.!?]+)[.!?]*",
            r"act like a ([^.!?]+)[.!?]*",
            r"act like an ([^.!?]+)[.!?]*",
            r"pretend to be a ([^.!?]+)[.!?]*",
            r"pretend to be an ([^.!?]+)[.!?]*",
            r"roleplay as a ([^.!?]+)[.!?]*",
            r"roleplay as an ([^.!?]+)[.!?]*"
        ]

        for pattern in character_request_patterns:
            match = re.search(pattern, user_msg.lower())
            if match:
                description = match.group(1).strip()
                log_info(f"Creating character: {description}")
                async with client.action(event.chat_id, 'typing'):
                    new_character_id = create_character(description, api_providers=api_providers)
                    if new_character_id:
                        switch_character(new_character_id)
                return

        # Add message to conversation memory
        conversation_memory.append({"role": "user", "content": user_msg})
        if len(conversation_memory) > max_memory_items:
            conversation_memory.pop(0)

        # Limit conversation context for character profiles to prevent token limit issues
        limited_memory = conversation_memory
        if runtime_system_prompt and len(conversation_memory) > 5:
            limited_memory = conversation_memory[-5:]

        # Prepare context and system prompt
        context = "\n".join([f"{item['role']}: {item['content']}" for item in limited_memory])
        system_prompt_text = f"{runtime_system_prompt}\n\nRecent conversation context: {context}" if runtime_system_prompt else f"Recent conversation context: {context}"

        # Generate response using available providers
        async with client.action(event.chat_id, 'typing'):
            await client.send_read_acknowledge(event.chat_id, event.message)
            response = None

            for provider in api_providers:
                provider_name = provider.__class__.__name__
                log_info(f"Trying provider: {provider_name}")
                try:
                    response = provider.generate_response(
                        prompt=user_msg,
                        system_prompt=system_prompt_text,
                        temperature=0.7,
                        max_tokens=1000
                    )
                    if not response.startswith("__ERROR__"):
                        log_info(f"Success with {provider_name}")
                        break
                    else:
                        log_warning(f"{provider_name} failed: {response}")
                except Exception as e:
                    log_error(f"{provider_name} error: {str(e)}")

            # Send response if successful
            if response and not response.startswith("__ERROR__"):
                conversation_memory.append({"role": "assistant", "content": response})
                if len(conversation_memory) > max_memory_items:
                    conversation_memory.pop(0)
                await event.reply(response)
                log_info(f"Sent: {response}")
            else:
                log_error(f"All providers failed")

    except Exception as e:
        log_error(f"Message processing error: {e}")

# ===== MAIN EXECUTION =====

async def main():
    """Main execution function"""
    log_info("Starting Telegram bot...")

    # Start client
    await client.start()

    # Verify group connections
    try:
        if group_ids:
            for group_id in group_ids:
                await client.get_entity(group_id)
            log_info(f"Connected to {len(group_ids)} groups")
        else:
            log_warning("No group IDs configured")
    except Exception as e:
        log_error(f"Group connection error: {e}")

    # Load character profiles
    load_character_profiles()

    # Set initial character or default mode
    global runtime_system_prompt
    if character_profiles:
        character_keys = sorted(list(character_profiles.keys()))
        first_character = character_keys[0]
        switch_character(first_character)
        log_info(f"Starting with character: {character_profiles[first_character]['name']}")
    else:
        log_info("No characters found. Use 'be a [character]' to create one.")
        runtime_system_prompt = ""  # Empty system prompt for default behavior

    log_info("Bot is running. Press Ctrl+C to stop.")

# Run the bot
client.loop.run_until_complete(main())
client.run_until_disconnected()