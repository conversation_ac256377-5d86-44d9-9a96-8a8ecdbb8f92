import requests
import json
import time

API_KEY = "sUb5T64gZ8Xco6ChsO417KhW0uSp5zdwRY"
BASE_URL = "https://app.premai.io/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

def fetch_models():
    """
    Attempt to fetch the list of available models from Prem AI,
    handling different possible endpoint paths and response structures.
    """
    model_endpoints = [
        f"{BASE_URL}/models",
        f"{BASE_URL}/engines",
        f"{BASE_URL}/v1/models",
        f"{BASE_URL}/v1/engines"
    ]

    for url in model_endpoints:
        try:
            print(f"Trying to fetch models from: {url}")
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # Raise for bad status
            data = response.json()

            if isinstance(data, list):
                model_names = [
                    model.get("name") for model in data if isinstance(model, dict) and "name" in model
                ]
                if model_names:
                    print(f"Successfully fetched models from {url}")
                    return model_names
            elif isinstance(data, dict):
                if "data" in data and isinstance(data["data"], list):
                    model_names = [
                        model.get("name") for model in data["data"] if isinstance(model, dict) and "name" in model
                    ]
                    if model_names:
                        print(f"Successfully fetched models from {url}")
                        return model_names
                elif "models" in data and isinstance(data["models"], list):
                    model_names = [
                        model.get("id") for model in data["models"] if isinstance(model, dict) and "id" in model
                    ]
                    if model_names:
                        print(f"Successfully fetched models from {url}")
                        return model_names
                elif "objects" in data and isinstance(data["objects"], list):
                    model_names = [
                        model.get("id") for obj in data["objects"] if isinstance(obj, dict) and "id" in obj
                    ]
                    if model_names:
                        print(f"Successfully fetched models from {url}")
                        return model_names

            print(f"Unexpected response from {url}: {data}")
        except requests.exceptions.RequestException as e:
            print(f"Error fetching models from {url}: {e}")
            #  Don't return here, try the next URL

    print("Failed to retrieve models from all endpoints.")
    return None  # Return None if all attempts fail


def test_model(model_id):
    """
    Test the given model with a sample message.
    """
    payload = {
        "model": model_id,
        "messages": [{"role": "user", "content": "Once upon a time"}],  # Use messages for chat/completions
        "max_tokens": 10  # Token cap to save costs
    }
    try:
        response = requests.post(f"{BASE_URL}/chat/completions", headers=headers, json=payload) # Corrected endpoint
        response.raise_for_status()  # Raise an error for bad status codes
        result = response.json()
        # Check for 'choices' which is the standard chat completions response structure
        if "choices" in result and len(result["choices"]) > 0:
            return result["choices"][0]["message"]["content"]  # Extract the text
        else:
            return "No content generated"
    except requests.exceptions.HTTPError as e:
        print(f"Error with model '{model_id}': {e}")
        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Error testing model {model_id}: {e}")
        return None



def main():
    print("Fetching available models...")
    models = fetch_models()

    if not models:
        print("No models found. Exiting.")
        return

    print(f"Found {len(models)} models:")
    for model in models:
        print(f"- {model}")

    print("\nTesting models...")
    for model in models:
        print(f"\nTesting model: {model}")
        result = test_model(model)
        if result:
            print(f"Result: {result}")
        time.sleep(20)  # Wait 20 seconds between requests to avoid rate limits



if __name__ == "__main__":
    main()
