import requests
import json
import time
from typing import Dict, List, Optional, Tuple, Any
import jwt
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='naas_api_test.log'
)

class NaasAPITester:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_urls = [
            "https://api.naas.ai",
            "https://api.naas.ai/v1",
            "https://api.naas.ai/api/v1",
            "https://api.naas.ai/api"
        ]
        self.model_endpoints = [
            "models",
            "aimodels",
            "ai_models",
            "models/list",
            "available_models"
        ]
        self.completion_endpoints = [
            "chat/completions",
            "completions",
            "chat",
            "generate",
            "inference"
        ]
        self.successful_config = None
        self.session = requests.Session()
        
    def validate_jwt(self) -> bool:
        """Validate JWT token format and expiration"""
        try:
            decoded = jwt.decode(self.api_key, options={"verify_signature": False})
            if 'exp' in decoded:
                exp_time = datetime.fromtimestamp(decoded['exp'])
                if exp_time < datetime.now():
                    logging.warning(f"Token expired on {exp_time}")
                    return False
            logging.info("JWT token format is valid")
            return True
        except jwt.InvalidTokenError as e:
            logging.error(f"Invalid JWT token: {e}")
            return False

    def try_auth_methods(self) -> List[Dict[str, str]]:
        """Generate different authentication header combinations"""
        return [
            {"Authorization": f"Bearer {self.api_key}"},
            {"Authorization": self.api_key},
            {"X-API-Key": self.api_key},
            {"api_key": self.api_key},
            {"token": self.api_key}
        ]

    def test_endpoint(self, url: str, auth_header: Dict[str, str], 
                     method: str = "GET", payload: Optional[Dict] = None) -> Tuple[bool, Any]:
        """Test an endpoint with specific authentication method"""
        headers = {**auth_header, "Content-Type": "application/json"}
        print(f"Testing {method} {url}")
        
        try:
            if method == "GET":
                response = self.session.get(url, headers=headers, timeout=10)
            else:
                response = self.session.post(url, headers=headers, json=payload, timeout=10)
            
            response.raise_for_status()
            return True, response.json()
        except requests.exceptions.RequestException as e:
            print(f"Failed: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.status_code} - {e.response.text}")
            return False, str(e)

    def find_working_configuration(self) -> Optional[Dict]:
        """Find a working API configuration"""
        if not self.validate_jwt():
            print("Invalid JWT token format")
            return None

        print("\nTrying different endpoint combinations...")
        for base_url in self.base_urls:
            for model_endpoint in self.model_endpoints:
                url = f"{base_url}/{model_endpoint}"
                
                for auth_header in self.try_auth_methods():
                    auth_type = list(auth_header.keys())[0]
                    print(f"\nTrying {url}")
                    print(f"Auth method: {auth_type}")
                    
                    success, result = self.test_endpoint(url, auth_header)
                    
                    if success:
                        print(f"Success! Found working configuration")
                        self.successful_config = {
                            "base_url": base_url,
                            "model_endpoint": model_endpoint,
                            "auth_header": auth_header,
                            "models": result
                        }
                        return self.successful_config
                    
                    time.sleep(1)  # Rate limiting
        
        print("No working configuration found")
        return None

    def test_completion(self, model_id: str) -> Optional[str]:
        """Test completion with the model"""
        if not self.successful_config:
            print("No working configuration found. Cannot test completion.")
            return None

        base_url = self.successful_config["base_url"]
        auth_header = self.successful_config["auth_header"]

        print(f"\nTesting completion endpoints for model {model_id}...")
        for completion_endpoint in self.completion_endpoints:
            url = f"{base_url}/{completion_endpoint}"
            payload = {
                "model": model_id,
                "messages": [{"role": "user", "content": "Hello, can you hear me?"}],
                "max_tokens": 50,
                "temperature": 0.7
            }

            success, result = self.test_endpoint(url, auth_header, "POST", payload)
            if success:
                print(f"Successfully got completion from {url}")
                return result
            
            time.sleep(1)  # Rate limiting

        print(f"No working completion endpoint found for model {model_id}")
        return None

    def save_results(self, results: Dict) -> None:
        """Save test results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"naas_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\nResults saved to {filename}")

def main():
    # Your JWT token
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzNDk4YWE5MC1kNTc4LTRkOGUtOWRkOS0wYjAxMmQxYjU1ZWIiLCJhcGlfa2V5X2lkIjoiMzE0OTcxZWEtNzU2NC00M2NjLTlhMTctMGVlMzRjNTA3MTQwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDQtMTJUMDk6MjE6NTMuODUwODA2KzAwOjAwIn0.0irR8V6GseNppTqfhDnENiJB54kpvXD0eF5sXDPxRjM"
    
    print("Starting Naas API testing...")
    tester = NaasAPITester(api_key)
    
    config = tester.find_working_configuration()
    if not config:
        print("Could not find working API configuration")
        return

    results = {
        "configuration": config,
        "completions": {}
    }

    if isinstance(config.get("models"), list):
        for model in config["models"]:
            model_id = model.get("id") if isinstance(model, dict) else model
            completion_result = tester.test_completion(model_id)
            results["completions"][model_id] = completion_result
            time.sleep(2)  # Rate limiting
    
    tester.save_results(results)
    print("Testing completed")

if __name__ == "__main__":
    main()