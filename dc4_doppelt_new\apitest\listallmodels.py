from openai import OpenAI
import os

# Replace with your actual Groq API key
GROQ_API_KEY = "********************************************************"

client = OpenAI(
    api_key=GROQ_API_KEY,
    base_url="https://api.groq.com/openai/v1"
)

def list_groq_models():
    try:
        response = client.models.list()
        print("Available Groq Models:")
        for model in response.data:
            print(f"- ID: {model.id}, Object: {model.object}, Created: {model.created}")
    except Exception as e:
        print(f"Error listing models: {e}")

if __name__ == "__main__":
    list_groq_models()