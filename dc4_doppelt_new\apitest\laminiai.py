import os
import traceback
import time
import openai

# Configuration
API_KEY = "71bbfe28b53767ee1a4b6b280097d717649f7b53e535cbd981d37f0d17139ebd"
BASE_URL = "https://api.lamini.ai/inf"
DELAY_SECONDS = 20  # Adjust as needed
TEST_MESSAGE = "only say a number between 1 and 10"
MAX_TOKENS = 100

# Lamini AI models to test (from user's feedback)
LAMINIAI_MODELS = [
    "deepseek-ai/DeepSeek-R1",
    "deepseek-ai/DeepSeek-V3",
    "meta-llama/Llama-3.1-405B-Instruct",
    "meta-llama/Llama-3.3-70B-Instruct",
    "meta-llama/Llama-4-Scout-17B-16E-Instruct",
    "meta-llama/Llama-4-Maverick-17B-128E-Instruct",
    "gpt-4o",
    "gpt-4o-mini",
    "chatgpt-4o-latest",
    "meta-llama/Llama-3.2-3B-Instruct",
    "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
    "meta-llama/Llama-3.1-8B-Instruct",
    "mistralai/Mistral-7B-Instruct-v0.3"
]

def test_model(model_name):
    try:
        client = openai.OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        start_time = time.time()
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": TEST_MESSAGE}],
            max_tokens=MAX_TOKENS,
        )
        end_time = time.time()
        elapsed_time = end_time - start_time
        output = response.choices[0].message.content
        print(f"  Response from {model_name}: {output}")
        print(f"  Time taken: {elapsed_time:.2f} seconds")
        print(f"  Token usage: {response.usage.total_tokens}")
        return True
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

def main():
    for model in LAMINIAI_MODELS:
        print(f"\nTesting model: {model}")
        success = test_model(model)
        print(f"  {model} is {'accessible' if success else 'NOT accessible'}")
        time.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    main()
