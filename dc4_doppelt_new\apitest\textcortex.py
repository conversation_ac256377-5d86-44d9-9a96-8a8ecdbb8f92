import requests
import json

# Replace with your actual TextCortex API key
API_KEY = "gAAAAABn-kBvXX4x5cVtOs0cnXL4N4h-ShFdZoB2JHVyoOp7Jw7t9JgwqI0PRrwyJ6AHfqTqQAxvvFdbINzdpQcIhNJfayUC0F78fZeFfvZ6nRay6mrq3wzCrBcqz88RaZYXBy5K-AvOECPB_g1UxvPo5sOBu5euZ6KI1_WNPHXPCHuKwIGaH1k="
BASE_URL = "https://api.textcortex.com/v1"
COMPLETIONS_ENDPOINT = f"{BASE_URL}/texts/completions"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}",
}

def send_test_message(model_name, test_message="Hello, TextCortex!"):
    """Sends a test message to the specified TextCortex model."""
    payload = json.dumps({
        "text": test_message,  # Changed 'prompt' to 'text'
        "model": model_name,
        "max_tokens": 50  # Adjust as needed
    })
    try:
        response = requests.post(COMPLETIONS_ENDPOINT, headers=headers, data=payload)
        response.raise_for_status()
        result = response.json()
        print(f"Test with model '{model_name}': Success!")
        print(f"Response: {json.dumps(result, indent=2)}")
        return result
    except requests.exceptions.RequestException as e:
        print(f"Error with model '{model_name}': {e}")
        if response is not None:
            print(f"Response status code: {response.status_code}")
            try:
                print(f"Response body: {response.json()}")
            except json.JSONDecodeError:
                print(f"Response body (non-JSON): {response.text}")
        return None

if __name__ == "__main__":
    # Hardcoded list of valid models based on the API response
    available_models = [
        "gpt-4o",
        "gpt-4o-mini",
        "claude-3-haiku",
        "claude-3-sonnet",
        "claude-3-5-sonnet",
        "claude-3-7-sonnet",
        "claude-3-7-sonnet-thinking",
        "claude-3-5-haiku",
        "mistral-small",
        "mistral-large",
        "deepseek-r1",
        "gemini-2-0-flash",
        "gemini-2-5-pro",
        "grok-2"
    ]

    print("\nAttempting to send a test message to the following valid models (with 'text' field):")
    for model in available_models:
        print(f"- {model}")
        send_test_message(model)