import requests
import time
import json

API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzNDk4YWE5MC1kNTc4LTRkOGUtOWRkOS0wYjAxMmQxYjU1ZWIiLCJhcGlfa2V5X2lkIjoiMzE0OTcxZWEtNzU2NC00M2NjLTlhMTctMGVlMzRjNTA3MTQwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDQtMTJUMDk6MjE6NTMuODUwODA2KzAwOjAwIn0.0irR8V6GseNppTqfhDnENiJB54kpvXD0eF5sXDPxRjM"
BASE_URL = "https://api.naas.ai"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# Reduced fallback list to common models (to be updated after fetching actual models)
KNOWN_MODELS = ["gpt-3.5-turbo", "mistral-7b"]

def try_endpoints(method, paths, payload=None):
    """
    Try multiple endpoint variations and return the first successful response.
    """
    for path in paths:
        url = f"{BASE_URL}/{path}"
        print(f"Trying {method} {url}")
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, json=payload, timeout=10)
            response.raise_for_status()
            print(f"Success: {method} {url}")
            return response.json(), path
        except requests.exceptions.RequestException as e:
            print(f"Failed: {method} {url} - {e}")
            if hasattr(e, "response") and e.response:
                print(f"Response: {e.response.status_code} {e.response.text}")
    return None, None

def fetch_models():
    """
    Fetch available models from Naas.ai using possible endpoints.
    """
    paths = ["aimodels", "v1/aimodels", "models", "v1/models"]
    data, successful_path = try_endpoints("GET", paths)
    
    if data:
        # Extract model IDs (assuming response is a list of objects with 'id' or 'name')
        models = [model.get("id", model.get("name")) for model in data if isinstance(data, list)]
        if models:
            print(f"Models fetched from {successful_path}: {models}")
            return models
    print("Error fetching models. Falling back to predefined list.")
    return KNOWN_MODELS

def test_model(model_id):
    """
    Test the given model with a sample message using possible chat endpoints.
    """
    payload = {
        "model_id": model_id,  # Primary key
        "model": model_id,     # Fallback key for compatibility
        "messages": [
            {"role": "user", "content": "Once upon a time"}
        ],
        "max_tokens": 50
    }
    paths = ["v1/chat/completions", "v1/completions", "chat", "completions"]
    data, successful_path = try_endpoints("POST", paths, payload)
    
    if data:
        return data.get("choices", [{}])[0].get("message", {}).get("content", "No response generated")
    print(f"No valid endpoint found for model {model_id}")
    return None

def main():
    print("Fetching available models...")
    models = fetch_models()

    if not models:
        print("No models found. Exiting.")
        return

    print(f"Found {len(models)} models:")
    for model in models:
        print(f"- {model}")

    print("\nTesting models with the message: Once upon a time")
    results = {}
    for model in models:
        print(f"\nTesting model: {model}")
        result = test_model(model)
        if result:
            print(f"Result: {result}")
            results[model] = result
        time.sleep(5)  # Wait 5 seconds to avoid rate limits

    # Save results to a file
    with open("model_results.json", "w") as f:
        json.dump(results, f, indent=4)
    print("\nAll results saved to 'model_results.json'.")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Script failed: {e}")