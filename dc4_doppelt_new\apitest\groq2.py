import asyncio
from openai import OpenAI
import os
import base64

# Get your Groq API key from https://console.groq.com/keys
GROQ_API_KEY = "********************************************************"

client = OpenAI(
    api_key=GROQ_API_KEY,
    base_url="https://api.groq.com/openai/v1"
)

def test_chat_model(model_name, responses):
    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": "Tell me something interesting."}
            ]
        )
        content = response.choices[0].message.content
        print(f"Success for chat model {model_name}: {content}")
        responses.append(f"{model_name} says: {content}")
    except Exception as e:
        print(f"Error for chat model {model_name}: {e}")

async def test_tts_model(model_name, test_text=""):
    try:
        response = await client.audio.speech.create(
            model=model_name,
            voice="alloy",  # You can experiment with other voices if supported
            input=test_text
        )
        with open("combined_output.mp3", "wb") as f:
            f.write(response.content)
        print(f"Success for TTS model {model_name}: Combined audio saved to combined_output.mp3")
    except Exception as e:
        print(f"Error for TTS model {model_name}: {e}")

async def main():
    free_chat_models = [
        "llama-3.3-70b-versatile",
        "llama-3.1-8b-instant",
        "gemma2-9b-it",
        "deepseek-r1-distill-llama-70b",
        "qwen-qwq-32b",
        "mistral-saba-24b",
        "llama-guard-3-8b",
        "allam-2-7b",
        "compound-beta",
        "compound-beta-mini",
        "meta-llama/llama-4-maverick-17b-128e-instruct",
        "meta-llama/llama-4-scout-17b-16e-instruct",
        "llama3-70b-8192",
        "llama3-8b-8192",
    ]

    tts_model = "playai-tts"
    all_responses = []

    print("Testing free chat models on Groq...")
    for model in free_chat_models:
        print(f"\nTesting chat model: {model}")
        test_chat_model(model, all_responses)  # Call synchronously
        await asyncio.sleep(30)  # Be mindful of rate limits

    combined_text = " ".join(all_responses)
    print(f"\nCombined text for TTS: {combined_text}")
    print(f"\nTesting TTS model: {tts_model} with combined responses")
    await test_tts_model(tts_model, combined_text)

if __name__ == "__main__":
    asyncio.run(main())