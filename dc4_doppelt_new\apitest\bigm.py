import requests
import json

# Configuration
api_key = "a9fcf83c47f14d1bb7daf20b001c74da.oZ0gQa91iq2Qj65h"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}
message = "Hello, can you tell me about Beijing?"
image_url = "https://upload.wikimedia.org/wikipedia/commons/thumb/9/9c/Forbidden_City_Beijing_China.jpg/800px-Forbidden_City_Beijing_China.jpg"

# GLM-4-Flash Request (Text-based)
glm4_flash_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
glm4_flash_payload = {
    "model": "glm-4-flash",
    "messages": [
        {"role": "user", "content": message}
    ],
    "max_tokens": 200,
    "temperature": 0.7
}

# GLM-4V-Flash Request (Vision-based)
glm4v_flash_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
glm4v_flash_payload = {
    "model": "glm-4v-flash",
    "messages": [
        {"role": "user", "content": message}
    ],
    "image_url": image_url,
    "max_tokens": 200,
    "temperature": 0.7
}

def send_request(url, payload, model_name):
    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as http_err:
        return {
            "error": f"HTTP error for {model_name}: {str(http_err)}",
            "status_code": response.status_code,
            "response_text": response.text
        }
    except requests.exceptions.RequestException as req_err:
        return {"error": f"Request error for {model_name}: {str(req_err)}"}
    except ValueError as val_err:
        return {"error": f"JSON decode error for {model_name}: {str(val_err)}"}

# Send requests to both models
glm4_flash_response = send_request(glm4_flash_url, glm4_flash_payload, "GLM-4-Flash")
glm4v_flash_response = send_request(glm4v_flash_url, glm4v_flash_payload, "GLM-4V-Flash")

# Print responses
print("GLM-4-Flash Response:")
print(json.dumps(glm4_flash_response, indent=2, ensure_ascii=False))
print("\nGLM-4V-Flash Response:")
print(json.dumps(glm4v_flash_response, indent=2, ensure_ascii=False))