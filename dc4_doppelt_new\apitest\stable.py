# Stability AI Models Script with Money-Saving Prioritization

# Define the models with their details
MODELS = [
    {
        "name": "Stable Image Ultra",
        "description": "Flagship image service based on Stable Diffusion 3.5 Large, offering the highest quality and detail.",
        "cost": 8.0,
        "features": ["highest quality", "photorealistic", "professional print media"],
        "use_case": "High-end projects requiring maximum detail."
    },
    {
        "name": "Stable Diffusion 3.5 Large",
        "description": "SD 3.5 is our most powerful 8 billion parameter base model with superior quality and prompt adherence.",
        "cost": 6.5,
        "features": ["8B parameters", "superior quality", "prompt adherence"],
        "use_case": "High-quality image generation with advanced features."
    },
    {
        "name": "Stable Diffusion 3.5 Large Turbo",
        "description": "Turbo variant of Stable Diffusion 3.5 Large, for fast high-quality images.",
        "cost": 4.0,
        "features": ["fast generation", "high-quality"],
        "use_case": "Quick generation of high-quality images at a lower cost."
    },
    {
        "name": "Stable Diffusion 3.5 Medium",
        "description": "The 2 and 2.5 billion parameter variants of Stable Diffusion 3.5 respectively.",
        "cost": 3.5,
        "features": ["medium quality", "cost-effective"],
        "use_case": "Balanced quality and cost for general-purpose use."
    },
    {
        "name": "Stable Image Core",
        "description": "Optimized for fast and cost-effective image generation.",
        "cost": 3.0,
        "features": ["fast", "cost-effective"],
        "use_case": "Budget-friendly projects requiring quick results."
    },
    {
        "name": "SDXL 1.0",
        "description": "Legacy base model for straightforward image generation.",
        "cost": 0.9,
        "features": ["legacy model", "straightforward generation"],
        "use_case": "Basic image generation needs."
    },
    {
        "name": "SD 1.6",
        "description": "Legacy flexible-resolution base model to generate non-standard aspect ratios.",
        "cost": 0.9,
        "features": ["legacy model", "flexible resolutions"],
        "use_case": "Projects requiring non-standard aspect ratios."
    }
]

# Sort models by cost (ascending) for money-saving prioritization
MODELS.sort(key=lambda x: x["cost"])

def display_models():
    """
    Displays all models sorted by cost (cheapest first).
    """
    print("Available Stability AI Models (Sorted by Cost):")
    for model in MODELS:
        print(f"\nModel Name: {model['name']}")
        print(f"Description: {model['description']}")
        print(f"Cost: ${model['cost']}")
        print(f"Features: {', '.join(model['features'])}")
        print(f"Use Case: {model['use_case']}")

def recommend_model(budget, requirements=None):
    """
    Recommends a model based on budget and optional requirements.
    
    Args:
        budget (float): Maximum cost per request.
        requirements (list, optional): List of required features.
    
    Returns:
        dict: Recommended model or None if no match found.
    """
    for model in MODELS:
        if model["cost"] <= budget:
            if requirements:
                if all(feature in model["features"] for feature in requirements):
                    return model
            else:
                return model
    return None

def main():
    # Display all models sorted by cost
    display_models()
    
    # Example usage: Recommend a model based on budget and requirements
    print("\n--- Model Recommendation ---")
    budget = float(input("Enter your budget per request ($): "))
    requirements_input = input("Enter required features (comma-separated, e.g., 'fast,high-quality'): ")
    requirements = [req.strip() for req in requirements_input.split(",")] if requirements_input else None
    
    recommended_model = recommend_model(budget, requirements)
    if recommended_model:
        print("\nRecommended Model:")
        print(f"Name: {recommended_model['name']}")
        print(f"Description: {recommended_model['description']}")
        print(f"Cost: ${recommended_model['cost']}")
        print(f"Features: {', '.join(recommended_model['features'])}")
        print(f"Use Case: {recommended_model['use_case']}")
    else:
        print("\nNo model matches your criteria within the given budget.")

if __name__ == "__main__":
    main()