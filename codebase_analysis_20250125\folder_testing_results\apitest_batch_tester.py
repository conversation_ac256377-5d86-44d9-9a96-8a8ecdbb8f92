#!/usr/bin/env python3
"""
🌐 APITEST FOLDER COMPREHENSIVE BATCH TESTER
Systematically tests all Python scripts in the apitest folder
"""

import os
import subprocess
import sys
from pathlib import Path
import time

def find_python_scripts(directory):
    """Find all Python scripts in directory and subdirectories"""
    python_scripts = []
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                full_path = os.path.join(root, file)
                python_scripts.append(full_path)
    
    return sorted(python_scripts)

def test_script_syntax(script_path):
    """Test if a Python script has valid syntax"""
    try:
        result = subprocess.run(
            ['python3', '-m', 'py_compile', script_path],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0, result.stderr
    except subprocess.TimeoutExpired:
        return False, "Timeout during compilation"
    except Exception as e:
        return False, str(e)

def main():
    print("🎯 APITEST FOLDER COMPREHENSIVE TESTING STARTED")
    print("=" * 60)
    
    # Change to the correct directory
    base_dir = "/home/<USER>/colestart"
    os.chdir(base_dir)
    
    # Find all Python scripts in apitest folder
    apitest_dir = "apitest"
    if not os.path.exists(apitest_dir):
        print(f"❌ Directory {apitest_dir} not found!")
        return
    
    scripts = find_python_scripts(apitest_dir)
    total_scripts = len(scripts)
    
    print(f"📊 Found {total_scripts} Python scripts to test")
    print("=" * 60)
    
    # Test results
    valid_scripts = []
    invalid_scripts = []
    
    # Test each script
    for i, script in enumerate(scripts, 1):
        relative_path = os.path.relpath(script, base_dir)
        print(f"[{i}/{total_scripts}] Testing: {relative_path}")
        
        is_valid, error_msg = test_script_syntax(script)
        
        if is_valid:
            print(f"  ✅ Syntax: VALID")
            valid_scripts.append(relative_path)
        else:
            print(f"  ❌ Syntax: INVALID - {error_msg}")
            invalid_scripts.append((relative_path, error_msg))
    
    # Final results
    print("\n" + "=" * 60)
    print("🎯 APITEST FOLDER TESTING COMPLETED")
    print("=" * 60)
    print(f"📊 Total Scripts: {total_scripts}")
    print(f"✅ Valid Scripts: {len(valid_scripts)} ({len(valid_scripts)/total_scripts*100:.1f}%)")
    print(f"❌ Invalid Scripts: {len(invalid_scripts)} ({len(invalid_scripts)/total_scripts*100:.1f}%)")
    
    # Save detailed results
    results_file = "codebase_analysis_20250125/folder_testing_results/apitest_detailed_results.txt"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    
    with open(results_file, 'w') as f:
        f.write("🌐 APITEST FOLDER DETAILED TESTING RESULTS\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Total Scripts: {total_scripts}\n")
        f.write(f"Valid Scripts: {len(valid_scripts)}\n")
        f.write(f"Invalid Scripts: {len(invalid_scripts)}\n\n")
        
        f.write("✅ VALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script in valid_scripts:
            f.write(f"  {script}\n")
        
        f.write("\n❌ INVALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script, error in invalid_scripts:
            f.write(f"  {script}\n")
            f.write(f"    Error: {error}\n\n")
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    # Summary by category
    print("\n📊 RESULTS BY CATEGORY:")
    categories = {
        'main_providers': [s for s in valid_scripts if any(provider in s for provider in ['cerebras', 'groq', 'mistral', 'stable', 'cohere', 'textcortex', 'samban', 'bigm', 'firew'])],
        'specialized': [s for s in valid_scripts if s.startswith('apitest/_') or s.startswith('apitest/__')],
        'naas': [s for s in valid_scripts if 'naas' in s],
        'experimental': [s for s in valid_scripts if '21_SHIT_STREET' in s],
        'utilities': [s for s in valid_scripts if any(util in s for util in ['listall', 'ai_ml', 'openIA'])]
    }
    
    for category, scripts in categories.items():
        if scripts:
            print(f"  {category}: {len(scripts)} scripts")

if __name__ == "__main__":
    main()
