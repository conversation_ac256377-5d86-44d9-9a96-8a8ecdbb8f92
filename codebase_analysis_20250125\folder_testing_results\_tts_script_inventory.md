# 🔊 _TTS FOLDER COMPLETE SCRIPT INVENTORY

## 📊 OVERVIEW
**Location**: `/home/<USER>/colestart/_tts`
**Total Python Scripts**: 100+ identified
**Major Subfolders**: 8 main categories
**Purpose**: Comprehensive TTS system implementations

---

## 📁 ROOT LEVEL SCRIPTS

### 🐍 PYTHON SCRIPTS (6 scripts)
1. `generate_speech.py` - Speech generation utility
2. `test_piper_installations.py` - Piper installation testing
3. `test_piper_tts.py` - Piper TTS testing
4. `tts_sender.py` - TTS sending utility

### 🔧 SHELL SCRIPTS (8 scripts)
1. `install_all_piper_methods.sh` - Complete Piper installation
2. `install_and_test_piper.sh` - Install and test workflow
3. `install_piper.sh` - Basic Piper installation
4. `install_piper_binary.sh` - Binary installation
5. `install_piper_pip.sh` - Pip installation
6. `install_piper_simple.sh` - Simple installation
7. `install_piper_tts.sh` - TTS-specific installation
8. `run_installation.sh` - Installation runner

### 💻 POWERSHELL SCRIPTS (4 scripts)
1. `play_audio.ps1` - Audio playback
2. `tts_server.ps1` - TTS server
3. `tts_server copy.ps1` - TTS server backup
4. `tts_server_augment.ps1` - Enhanced TTS server

---

## 📁 MAJOR SUBFOLDERS

### 1. IDIOT_TTS (50+ scripts)
**Location**: `_tts/idiot_tts/`
**Purpose**: Comprehensive TTS system collection

#### 1.1 ROOT LEVEL (15+ scripts)
- `simple_piper_server.py` - Basic Piper server
- `simple_piper_server_fast.py` - Fast implementation
- `simple_piper_server_fixed.py` - Fixed version
- `simple_piper_server_new.py` - New implementation
- `simple_piper_server_nodelay.py` - No delay version
- `simple_piper_server_organized.py` - Organized version
- `simple_piper_server_streamlined.py` - Streamlined version
- `simple_piper_tts.py` - Simple TTS
- `telegram_group_sorter.py` - Group sorting
- `telegram_tts_client.py` - Telegram TTS client
- `test_piper_server.py` - Server testing
- `tts_chat.py` - TTS chat functionality
- `unified_piper_v2.py` - Unified implementation

#### 1.2 RUBBISH_MAIN SUBFOLDER (30+ scripts)
- `piper_combined.py` - Combined implementation
- `piper_dynamic_playlist.py` - Dynamic playlist
- `piper_generate.py` - Speech generation
- `piper_http_server.py` - HTTP server
- `piper_multi_speaker.py` - Multi-speaker support
- `piper_play.py` - Audio playback
- `telegram_tts_client.py` - Telegram client
- `test_piper_client.py` - Client testing
- `test_sound_server.py` - Sound server testing
- `test_tts_server.py` - TTS server testing
- `test_unicode_handling.py` - Unicode testing
- `unified_piper.py` - Unified system
- `unified_piper_v2.py` - Version 2
- `windows_sound_server.py` - Windows sound server

#### 1.3 TELEGRAM_BOSS SUBFOLDER (10+ scripts)
- `auto_like_messages.py` - Auto-like functionality
- `external_like.py` - External liking
- `external_liker.py` - Liker system
- `group_list.py` - Group management
- `like.py` - Like functionality
- `newlife.py` - New life implementation
- `newliferepair.py` - Repair system
- `newlifewhynottest.py` - Testing script

#### 1.4 OTHER SUBFOLDERS
- **base_new/**: `base.py`, `tts_module.py`
- **finally_tts_vlc/**: `piper_combined.py`, `piper_generate.py`, `piper_play.py`
- **ghost/**: `message_collector.py`, `virtual_mouse.py`, `virtual_mouse_linux.py`, `virtual_mouse_windows.py`
- **more/**: `edge_tts.py`, `espeak_ng.py`, `pipe.py`, `piper_pro.py`, `pytts_x3.py`
- **what_ever/**: `telegram_bot.py`, `tts.py`
- **wie_hat_er_das_gemacht/**: `piper_generate.py`, `piper_play.py`

### 2. PIPER_TTS_SERVER (40+ scripts)
**Location**: `_tts/piper_tts_server/`
**Purpose**: Professional TTS server implementations

#### 2.1 ROOT LEVEL (10+ scripts)
- `run_server.py` - Main server runner
- `setup_piper.py` - Piper setup
- `simple_client.py` - Simple client
- `test_client.py` - Test client
- `windows_wsl_bridge.py` - WSL-Windows bridge
- `check_piper.py` - Piper checker
- `config.py` - Configuration
- `quick_speak.py` - Quick speech
- `wsl_client.py` - WSL client

#### 2.2 VERSIONED IMPLEMENTATIONS
- **v1_basic_server/**: `piper_server.py`, `piper_server_win.py`, `piper_server_wsl.py`
- **v2_instant_playback/**: `piper_server_instant.py`, `piper_server_instant_win.py`, `piper_server_instant_wsl.py`
- **v3_comprehensive/**: `piper_server_complete.py`, `piper_server_complete_win.py`, `piper_server_complete_wsl.py`

#### 2.3 WSL_PIPER_ONLY SUBFOLDER (15+ scripts)
- `start_server.py` - Server starter
- `quick_test.py` - Quick testing
- `test_setup.py` - Setup testing
- `wsl_test_client.py` - WSL test client
- `config.py` - Configuration

### 3. SIDE_PROJECT_ULTIMATE_TTS (10+ scripts)
**Location**: `_tts/side_project_ultimate_tts_20241228/`
**Purpose**: Ultimate TTS system combining all features

#### 3.1 MAIN SCRIPTS
- `scripts/ultimate_tts_v1.py` - Version 1
- `scripts/ultimate_tts_v1_fixed.py` - Fixed V1
- `scripts/ultimate_tts_v2.py` - Version 2
- `scripts/ultimate_tts_v2_fixed.py` - Fixed V2
- `ultimate_tts_tester.py` - Testing script
- `unicode_language_tester.py` - Unicode testing
- `quick_test.py` - Quick testing
- `send_request.py` - Request sender
- `start_server.py` - Server starter

#### 3.2 TEST SCRIPTS
- `tests/test_v1_basic.py` - V1 basic testing
- `tests/test_v2_playback.py` - V2 playback testing

### 4. PIPER_SCRIPT (8+ scripts)
**Location**: `_tts/piper_script/`
**Purpose**: Direct Piper TTS scripts

#### 4.1 PYTHON SCRIPTS
- `direct_windows_call.py` - Direct Windows calls
- `final_solution.py` - Final solution
- `generate_wav.py` - WAV generation
- `generate_wav_batch.py` - Batch WAV generation
- `simple_tts.py` - Simple TTS

#### 4.2 BATCH/SHELL SCRIPTS
- `generate_wav.bat` - Windows batch
- `generate_wav.ps1` - PowerShell script
- `generate_wav.sh` - Shell script
- `simple_tts.ps1` - PowerShell TTS

### 5. PIPER SOURCE CODE (20+ scripts)
**Location**: `_tts/piper/src/`
**Purpose**: Piper TTS source implementation

#### 5.1 PYTHON TRAINING
- `python/piper_train/__main__.py` - Main training
- `python/piper_train/check_phonemes.py` - Phoneme checking
- `python/piper_train/export_onnx.py` - ONNX export
- `python/piper_train/infer.py` - Inference
- `python/piper_train/preprocess.py` - Preprocessing

#### 5.2 PYTHON RUNTIME
- `python_run/piper/__main__.py` - Main runtime
- `python_run/piper/config.py` - Configuration
- `python_run/piper/download.py` - Download utility
- `python_run/piper/http_server.py` - HTTP server
- `python_run/piper/voice.py` - Voice handling

#### 5.3 BENCHMARKING
- `benchmark/benchmark_generator.py` - Generator benchmark
- `benchmark/benchmark_onnx.py` - ONNX benchmark
- `benchmark/benchmark_torchscript.py` - TorchScript benchmark

### 6. NOTEBOOKS & SCRIPTS (5+ scripts)
**Location**: `_tts/piper/notebooks/`, `_tts/piper/script/`

#### 6.1 NOTEBOOKS
- `translator.py` - Translation utility

#### 6.2 SCRIPTS
- `generate_supported_languages.py` - Language generation
- `generate_voices_md.py` - Voice documentation

---

## 📊 TESTING PRIORITY

### 🔥 HIGH PRIORITY (Core functionality)
1. **Ultimate TTS Scripts** - Most comprehensive implementations
2. **Piper TTS Server** - Professional server implementations
3. **Simple Piper Servers** - Basic functionality testing

### 🟡 MEDIUM PRIORITY (Specialized features)
1. **Telegram Integration** - TTS with Telegram bots
2. **Audio Processing** - Playback and generation scripts
3. **Testing Scripts** - Validation and testing utilities

### 🟢 LOW PRIORITY (Development tools)
1. **Installation Scripts** - Setup and configuration
2. **Benchmark Scripts** - Performance testing
3. **Utility Scripts** - Helper and support tools

---

**Total Scripts Identified**: 100+ Python scripts
**Testing Status**: ⏳ READY TO BEGIN SYSTEMATIC TESTING
**Next Action**: Start testing each script category systematically
