WORKING TTS SERVERS WITH TELEGRAM BOT INTEGRATION
============================================================

✅ ultimate_tts_v2_fixed
   Port: 5000
   Category: ultimate_tts
   Path: _tts/side_project_ultimate_tts_20241228/scripts/ultimate_tts_v2_fixed.py
   Status: FULLY FUNCTIONAL

✅ wsl_tts_vlc_server
   Port: 8001
   Category: wsl_servers
   Path: side_project_wsl_tts_servers_20250125/scripts/wsl_tts_vlc_server.py
   Status: FULLY FUNCTIONAL

✅ wsl_tts_powershell_server
   Port: 8002
   Category: wsl_servers
   Path: side_project_wsl_tts_servers_20250125/scripts/wsl_tts_powershell_server.py
   Status: FULLY FUNCTIONAL

✅ wsl_tts_windows_server
   Port: 8003
   Category: wsl_servers
   Path: side_project_wsl_tts_servers_20250125/scripts/wsl_tts_windows_server.py
   Status: FULLY FUNCTIONAL

✅ advanced_streaming_tts_server
   Port: 8004
   Category: wsl_servers
   Path: side_project_wsl_tts_servers_20250125/scripts/advanced_streaming_tts_server.py
   Status: FULLY FUNCTIONAL

✅ clean_lightweight_tts_server
   Port: 8006
   Category: wsl_servers
   Path: side_project_wsl_tts_servers_20250125/scripts/clean_lightweight_tts_server.py
   Status: FULLY FUNCTIONAL

✅ controlled_tts_server
   Port: 8007
   Category: wsl_servers
   Path: side_project_wsl_tts_servers_20250125/scripts/controlled_tts_server.py
   Status: FULLY FUNCTIONAL

