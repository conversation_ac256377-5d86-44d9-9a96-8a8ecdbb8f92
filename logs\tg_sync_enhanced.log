2025-07-29 16:23:17,502 - INFO - _load_target_groups:283 - ✅ Loaded target groups from pubg3/target_groups.txt
2025-07-29 16:23:17,573 - INFO - __init__:256 - 🚀 Enhanced TG_SyncMirrorBot initialized with all features
2025-07-29 16:23:17,574 - INFO - run_enhanced:2063 - 🚀 Starting Enhanced TG_SyncMirrorBot...
2025-07-29 16:23:17,608 - INFO - load_credentials:349 - ✅ Credentials loaded from: Base_new/config.txt
2025-07-29 16:23:17,630 - INFO - find_session_file:379 - 📱 Using existing session: Base_new/session_name.session
2025-07-29 16:23:18,070 - INFO - initialize_client:2052 - 📱 Telegram client initialized successfully
2025-07-29 16:23:19,596 - INFO - run_enhanced:2078 - ✅ Telegram client started successfully
2025-07-29 16:23:19,597 - INFO - extract_all_groups:690 - 👥 Extracting all user groups...
2025-07-29 16:23:24,599 - INFO - extract_all_groups:718 - ✅ Extracted 448 groups to data/groups_output.txt
2025-07-29 16:23:24,606 - INFO - run_enhanced:2091 - 👥 Monitoring 448 groups for messages
2025-07-29 16:23:24,607 - INFO - setup_enhanced_event_handlers:2028 - 🎯 Enhanced event handlers configured successfully
2025-07-29 16:23:24,619 - INFO - log_enhanced_statistics:1984 - 📊 Stats: 0 processed, 0 forwarded, 0 errors
2025-07-29 16:23:24,619 - INFO - run_enhanced:2115 - 🎯 Enhanced TG_SyncMirrorBot is now running with all features active
2025-07-29 16:23:24,620 - INFO - run_enhanced:2116 - 📊 Commands: /stats, /bl, /bl1-999, /blall, /blacklist (target groups only)
2025-07-29 16:23:24,620 - INFO - run_enhanced:2117 - 🔄 Press Ctrl+C to stop
2025-07-29 16:23:24,621 - INFO - start_queue_processor:523 - ⚡ Message queue processor started
2025-07-29 16:23:30,853 - DEBUG - extract_enhanced_links_and_mentions:1037 - 🔗 Extracted link (telegram_full): https://t.me/ManusWelt
2025-07-29 16:23:30,853 - DEBUG - extract_enhanced_links_and_mentions:1037 - 🔗 Extracted link (telegram_channel): https://t.me/ManusWelt
2025-07-29 16:23:30,853 - DEBUG - extract_enhanced_links_and_mentions:1037 - 🔗 Extracted link (telegram_general): https://t.me/ManusWelt
2025-07-29 16:23:30,855 - DEBUG - save_enhanced_extracted_data:1912 - 🔗 Saved 1 new links
2025-07-29 16:23:30,905 - DEBUG - process_enhanced_media_message:1891 - ⚠️ No target group defined for media type: webpage from Werbung und Promo gratis
2025-07-29 16:23:50,894 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued gif from 8ball🎱 RIP PAPA → lol3
2025-07-29 16:23:51,149 - INFO - log_forwarded:125 - [FORWARDED] gif from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol3 (-1002572672309) (msg:562098)
2025-07-29 16:24:32,432 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued gif from 8ball🎱 RIP PAPA → lol3
2025-07-29 16:24:32,727 - INFO - log_forwarded:125 - [FORWARDED] gif from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol3 (-1002572672309) (msg:562104)
2025-07-29 16:25:04,693 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued photo from 8ball🎱 RIP PAPA → lol1
2025-07-29 16:25:04,976 - INFO - log_forwarded:125 - [FORWARDED] photo from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol1 (-1002580694528) (msg:562110)
2025-07-29 16:25:09,999 - DEBUG - extract_enhanced_links_and_mentions:1037 - 🔗 Extracted link (telegram_full): https://t.me/+njp64Td8HkE4YTYy
2025-07-29 16:25:10,000 - DEBUG - extract_enhanced_links_and_mentions:1037 - 🔗 Extracted link (telegram_private): https://t.me/+njp64Td8HkE4YTYy
2025-07-29 16:25:10,000 - DEBUG - extract_enhanced_links_and_mentions:1037 - 🔗 Extracted link (telegram_general): https://t.me/+njp64Td8HkE4YTYy
2025-07-29 16:25:10,001 - DEBUG - save_enhanced_extracted_data:1912 - 🔗 Saved 1 new links
2025-07-29 16:25:10,001 - DEBUG - process_enhanced_media_message:1891 - ⚠️ No target group defined for media type: webpage from Werbung und Promo gratis
2025-07-29 16:25:11,500 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued gif from 8ball🎱 RIP PAPA → lol3
2025-07-29 16:25:11,776 - INFO - log_forwarded:125 - [FORWARDED] gif from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol3 (-1002572672309) (msg:562114)
2025-07-29 16:25:45,021 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued photo from 8ball🎱 RIP PAPA → lol1
2025-07-29 16:25:45,280 - INFO - log_forwarded:125 - [FORWARDED] photo from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol1 (-1002580694528) (msg:562121)
2025-07-29 16:27:01,878 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued photo from Meme Cannons → lol1
2025-07-29 16:27:02,187 - INFO - log_forwarded:125 - [FORWARDED] photo from Meme Cannons (https://t.me/MemeCannons88) → lol1 (-1002580694528) (msg:125428)
2025-07-29 16:27:04,269 - DEBUG - save_enhanced_dedup_cache:1957 - 💾 Saved 11 cache items (cleaned)
2025-07-29 16:27:04,271 - INFO - save_blacklist:441 - 💾 Saved 0 blacklisted groups
2025-07-29 16:27:04,272 - INFO - log_enhanced_statistics:1984 - 📊 Stats: 45 processed, 6 forwarded, 0 errors
2025-07-29 16:27:04,272 - INFO - run_enhanced:2167 - 💾 Final state saved successfully
2025-07-29 16:41:13,407 - INFO - _load_target_groups:283 - ✅ Loaded target groups from pubg3/target_groups.txt
2025-07-29 16:41:13,409 - INFO - load_blacklist:420 - 📋 Loaded 0 blacklisted groups
2025-07-29 16:41:13,414 - INFO - load_dedup_cache:630 - 🔄 Loaded 11 cached items
2025-07-29 16:41:13,418 - INFO - load_existing_extractions:668 - 🔗 Loaded 2 existing links
2025-07-29 16:41:13,434 - INFO - __init__:256 - 🚀 Enhanced TG_SyncMirrorBot initialized with all features
2025-07-29 16:41:13,434 - INFO - run_enhanced:2063 - 🚀 Starting Enhanced TG_SyncMirrorBot...
2025-07-29 16:41:13,438 - INFO - load_credentials:349 - ✅ Credentials loaded from: Base_new/config.txt
2025-07-29 16:41:13,441 - INFO - find_session_file:379 - 📱 Using existing session: Base_new/session_name.session
2025-07-29 16:41:13,479 - INFO - initialize_client:2052 - 📱 Telegram client initialized successfully
2025-07-29 16:41:15,212 - INFO - run_enhanced:2078 - ✅ Telegram client started successfully
2025-07-29 16:41:15,212 - INFO - extract_all_groups:690 - 👥 Extracting all user groups...
2025-07-29 16:41:19,809 - INFO - extract_all_groups:718 - ✅ Extracted 448 groups to data/groups_output.txt
2025-07-29 16:41:19,818 - INFO - run_enhanced:2091 - 👥 Monitoring 448 groups for messages
2025-07-29 16:41:19,818 - INFO - setup_enhanced_event_handlers:2028 - 🎯 Enhanced event handlers configured successfully
2025-07-29 16:41:19,820 - INFO - log_enhanced_statistics:1984 - 📊 Stats: 0 processed, 0 forwarded, 0 errors
2025-07-29 16:41:19,821 - INFO - run_enhanced:2115 - 🎯 Enhanced TG_SyncMirrorBot is now running with all features active
2025-07-29 16:41:19,822 - INFO - run_enhanced:2116 - 📊 Commands: /stats, /bl, /bl1-999, /blall, /blacklist (target groups only)
2025-07-29 16:41:19,822 - INFO - run_enhanced:2117 - 🔄 Press Ctrl+C to stop
2025-07-29 16:41:19,823 - INFO - start_queue_processor:523 - ⚡ Message queue processor started
2025-07-29 16:41:24,639 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued voice from 8ball🎱 RIP PAPA → lol3
2025-07-29 16:41:24,888 - INFO - log_forwarded:125 - [FORWARDED] voice from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol3 (-1002572672309) (msg:562255)
2025-07-29 16:41:38,349 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued photo from 8ball🎱 RIP PAPA → lol1
2025-07-29 16:41:38,349 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued photo from 8ball🎱 RIP PAPA → lol1
2025-07-29 16:41:38,655 - INFO - log_forwarded:125 - [FORWARDED] photo from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol1 (-1002580694528) (msg:562261)
2025-07-29 16:41:39,918 - INFO - log_forwarded:125 - [FORWARDED] photo from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol1 (-1002580694528) (msg:562262)
2025-07-29 16:42:24,446 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued video from 8ball🎱 RIP PAPA → lol2
2025-07-29 16:42:24,874 - INFO - log_forwarded:125 - [FORWARDED] video from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol2 (-1002556585806) (msg:562268)
2025-07-29 16:42:51,364 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued sticker from 8ball🎱 RIP PAPA → lol3
2025-07-29 16:42:51,615 - INFO - log_forwarded:125 - [FORWARDED] sticker from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol3 (-1002572672309) (msg:562269)
2025-07-29 16:43:07,378 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued photo from 8ball🎱 RIP PAPA → lol1
2025-07-29 16:43:07,739 - INFO - log_forwarded:125 - [FORWARDED] photo from 8ball🎱 RIP PAPA (Group ID: 1712377485) → lol1 (-1002580694528) (msg:562272)
2025-07-29 16:43:11,408 - DEBUG - extract_enhanced_links_and_mentions:997 - 🔗 Extracted forwarded chat link: https://t.me/Retards_TikTok
2025-07-29 16:43:11,409 - DEBUG - extract_enhanced_links_and_mentions:1048 - 📢 Extracted mention: @Retards_TikTok
2025-07-29 16:43:11,409 - DEBUG - extract_enhanced_links_and_mentions:1048 - 📢 Extracted mention: @Retardsoftiktok
2025-07-29 16:43:11,409 - DEBUG - save_enhanced_extracted_data:1912 - 🔗 Saved 1 new links
2025-07-29 16:43:11,412 - DEBUG - save_enhanced_extracted_data:1926 - 📢 Saved 2 new mentions
2025-07-29 16:43:11,492 - DEBUG - process_enhanced_media_message:1887 - 📤 Queued video from 𝐑𝐞𝐭𝐚𝐫𝐝𝐬 𝐨𝐟 𝐓𝐢𝐤𝐓𝐨𝐤 → lol2
2025-07-29 16:43:11,738 - INFO - log_forwarded:125 - [FORWARDED] video from 𝐑𝐞𝐭𝐚𝐫𝐝𝐬 𝐨𝐟 𝐓𝐢𝐤𝐓𝐨𝐤 (https://t.me/retardsoftiktok) → lol2 (-1002556585806) (msg:24668)
2025-07-29 16:43:32,122 - DEBUG - save_enhanced_dedup_cache:1957 - 💾 Saved 18 cache items (cleaned)
2025-07-29 16:43:32,340 - INFO - save_blacklist:441 - 💾 Saved 0 blacklisted groups
2025-07-29 16:43:32,343 - INFO - log_enhanced_statistics:1984 - 📊 Stats: 25 processed, 7 forwarded, 0 errors
2025-07-29 16:43:32,343 - INFO - run_enhanced:2167 - 💾 Final state saved successfully
