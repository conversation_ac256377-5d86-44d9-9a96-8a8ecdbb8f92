"""
SIMPLIFIED TELEGRAM BOT
======================

This script implements a Telegram bot that:
1. Responds to messages in configured groups
2. Maintains conversation context
3. Supports character profiles for different personalities
4. Uses multiple API providers with fallback and retry logic
5. Preserves the original message across provider attempts

Features:
- Direct message processing
- Character switching based on triggers
- Conversation memory for context
- Fallback to next API provider on failure
- Retry logic for transient errors
- Minimal typing indicators

Dependencies:
- telethon
- httpx
- json
- asyncio
- time
- re

Usage:
1. Configure in config.txt with API keys, group IDs, and provider settings
2. Run the script
3. <PERSON><PERSON> responds in specified groups

Version: 2.2
"""

from telethon import TelegramClient, events
import httpx
import json
import asyncio
import time
import re
import os
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Any, Optional, List
import tiktoken
try:
    import openai
except ImportError:
    print("[WARNING] OpenAI package not installed. Some features may not work properly.")

# Configure logging
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Create a logger
logger = logging.getLogger('telegram_bot')
logger.setLevel(logging.DEBUG)

# Create console handler with a higher log level
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(log_formatter)
logger.addHandler(console_handler)

# Create file handler which logs even debug messages
# Use rotating file handler to prevent logs from growing too large
file_handler = RotatingFileHandler('bot_debug.log', maxBytes=10*1024*1024, backupCount=5)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(log_formatter)
logger.addHandler(file_handler)

# Create a separate file for API request/response logs
api_logger = logging.getLogger('api_logs')
api_logger.setLevel(logging.DEBUG)
api_file_handler = RotatingFileHandler('api_debug.log', maxBytes=10*1024*1024, backupCount=5)
api_file_handler.setLevel(logging.DEBUG)
api_file_handler.setFormatter(log_formatter)
api_logger.addHandler(api_file_handler)
api_logger.addHandler(console_handler)  # Also show API logs in console

# Create a separate file for saving all group messages
message_formatter = logging.Formatter('%(asctime)s - GROUP: %(group_id)s - USER: %(user_id)s (%(username)s) - %(message)s')
group_msg_logger = logging.getLogger('group_messages')
group_msg_logger.setLevel(logging.INFO)
group_msg_file_handler = RotatingFileHandler('group_messages.log', maxBytes=50*1024*1024, backupCount=10)
group_msg_file_handler.setLevel(logging.INFO)
group_msg_file_handler.setFormatter(message_formatter)
group_msg_logger.addHandler(group_msg_file_handler)

# Function to log to both console and file
def log_info(message):
    print(f"[INFO] {message}")
    logger.info(message)

def log_warning(message):
    print(f"[WARNING] {message}")
    logger.warning(message)

def log_error(message):
    print(f"[ERROR] {message}")
    logger.error(message)

def log_debug(message):
    print(f"[DEBUG] {message}")
    logger.debug(message)

def log_api_debug(message):
    print(f"[DEBUG] {message}")
    api_logger.debug(message)

def save_group_message(group_id, user_id, username, message_text):
    """Save a message from a group to the dedicated log file.

    Args:
        group_id: The ID of the group where the message was sent
        user_id: The ID of the user who sent the message
        username: The username of the sender (or None if not available)
        message_text: The text content of the message
    """
    if not message_text or not message_text.strip():
        return  # Skip empty messages

    # Create a log record with extra fields for the custom formatter
    record = logging.LogRecord(
        name=group_msg_logger.name,
        level=logging.INFO,
        pathname='',
        lineno=0,
        msg=message_text,
        args=(),
        exc_info=None
    )

    # Add extra fields for the formatter
    record.__dict__['group_id'] = group_id
    record.__dict__['user_id'] = user_id
    record.__dict__['username'] = username or 'unknown'

    # Log the message
    group_msg_logger.handle(record)

    # Also store in group conversation history for context
    update_group_history(group_id, user_id, username, message_text)

def update_group_history(group_id, user_id, username, message_text):
    """Update the conversation history for a specific group.

    Args:
        group_id: The ID of the group
        user_id: The ID of the user who sent the message
        username: The username of the sender (or None if not available)
        message_text: The text content of the message
    """
    global group_conversation_history

    # Initialize group history if it doesn't exist
    if group_id not in group_conversation_history:
        group_conversation_history[group_id] = []

    # Add the message to the group's history
    timestamp = time.time()

    # Determine if this is a bot message (for role assignment)
    is_bot = isinstance(user_id, str) and user_id == 'bot'
    if not is_bot and username == 'bot':
        is_bot = True

    # Convert user_id to string for consistency
    user_id_str = str(user_id)

    # Update the user entity map with this sender
    if not is_bot:
        update_user_entity(user_id_str, username=username)

    # Get a sanitized sender ID for API calls
    if is_bot:
        sanitized_id = "Assistant"
    else:
        sanitized_id = get_sanitized_sender_id(user_id_str)

    message_info = {
        'user_id': user_id_str,  # Keep the original ID for internal use
        'username': sanitized_id,  # Use the sanitized ID for API calls
        'text': message_text,
        'timestamp': timestamp,
        'role': 'assistant' if is_bot else 'user'
    }

    group_conversation_history[group_id].append(message_info)

    # Limit the history size
    if len(group_conversation_history[group_id]) > MAX_GROUP_HISTORY:
        group_conversation_history[group_id].pop(0)

def find_common_phrases(text1, text2, min_length=3):
    """Find common phrases between two texts.

    Args:
        text1: First text
        text2: Second text
        min_length: Minimum number of words to consider a phrase

    Returns:
        list: List of common phrases
    """
    # Tokenize texts into words
    words1 = text1.lower().split()
    words2 = text2.lower().split()

    common_phrases = []

    # Look for common sequences of words
    for i in range(len(words1) - min_length + 1):
        for j in range(len(words2) - min_length + 1):
            # Check if we have a match starting at these positions
            match_length = 0
            while (i + match_length < len(words1) and
                   j + match_length < len(words2) and
                   words1[i + match_length] == words2[j + match_length]):
                match_length += 1

            # If we found a match of sufficient length
            if match_length >= min_length:
                phrase = ' '.join(words1[i:i + match_length])
                common_phrases.append(phrase)

    return common_phrases

def remove_repetitive_parts(response, group_id):
    """Remove parts of the response that are similar to recent responses.

    Args:
        response: The response text
        group_id: The group ID

    Returns:
        str: Response with repetitive parts removed
    """
    if group_id not in recent_responses:
        return response

    original_response = response
    log_debug(f"Checking for repetitive parts in: {response[:50]}...")

    for recent in recent_responses[group_id]:
        # Find common phrases (4+ words)
        common_phrases = find_common_phrases(response, recent, min_length=4)

        # Replace substantial common phrases
        for phrase in common_phrases:
            if len(phrase) > 15:  # Only replace substantial phrases
                log_debug(f"Found repetitive phrase: {phrase}")
                # Simply remove the phrase
                response = response.replace(phrase, "")

    # If we removed too much, revert to original
    if len(response) < len(original_response) * 0.5:
        log_debug("Too much content removed, reverting to original")
        return original_response

    # Clean up any double spaces or punctuation issues
    response = re.sub(r' +', ' ', response)
    response = re.sub(r' \.', '.', response)
    response = re.sub(r' ,', ',', response)

    log_debug(f"After removing repetitive parts: {response[:50]}...")
    return response.strip()

def sanitize_response(response, group_id=None):
    """Clean up AI responses to remove references to message numbers and user IDs.

    Args:
        response: The AI-generated response text
        group_id: Optional group ID for context-specific sanitization

    Returns:
        str: Sanitized response text
    """
    if not response:
        return response

    log_debug(f"BEFORE SANITIZATION: {response[:100]}...")

    # AGGRESSIVE APPROACH: Replace ALL numeric sequences that could be user IDs
    # This includes:
    # 1. Any number with 5+ digits (likely a user ID)
    # 2. Any reference to User_XXX or Person_XXX
    # 3. Any reference to "user" followed by numbers

    # First pass: Replace all large numbers (5+ digits) with "someone"
    # This is the most aggressive approach but necessary
    response = re.sub(r'\b\d{5,}\b', 'someone', response)

    # Second pass: Replace all User_XXX patterns
    response = re.sub(r'User_\d+', 'someone', response)
    response = re.sub(r'Someone_\d+', 'someone', response)
    response = re.sub(r'Person_\d+', 'someone', response)

    # Third pass: Replace "user" followed by numbers
    response = re.sub(r'[Uu]ser\s+\d+', 'someone', response)
    response = re.sub(r'[Pp]erson\s+\d+', 'someone', response)

    # Fourth pass: Replace message number references
    response = re.sub(r'[Mm]essage\s+\d+\s*:', '', response)
    response = re.sub(r'[Mm]essage\s+\d+', '', response)

    # Fifth pass: Try to replace any remaining patterns that look like IDs
    # This is a more targeted approach for any IDs we know about
    for user_id, entity in user_entity_map.items():
        if user_id.isdigit() and len(user_id) >= 5:
            # This is likely a Telegram ID, replace it aggressively
            response = re.sub(r'\b' + re.escape(user_id) + r'\b', 'someone', response)

        # Also replace the sanitized ID if we have it
        sanitized_id = entity.get('sanitized_id', '')
        if sanitized_id:
            response = re.sub(r'\b' + re.escape(sanitized_id) + r'\b', 'someone', response)

    # Clean up any double spaces created by the replacements
    response = re.sub(r' +', ' ', response)

    log_debug(f"AFTER SANITIZATION: {response[:100]}...")
    return response.strip()

def post_process_response(response, group_id=None):
    """Process the response after receiving it from the API.

    Args:
        response: The AI-generated response
        group_id: The group ID

    Returns:
        str: Processed response
    """
    if not response:
        return response

    log_debug(f"POST-PROCESSING RESPONSE: {response[:100]}...")

    # Step 1: Sanitize user IDs and message references
    sanitized = sanitize_response(response, group_id)

    # Step 2: Check for repetition against recent responses
    if group_id and group_id in recent_responses:
        sanitized = remove_repetitive_parts(sanitized, group_id)

    # Step 3: Final cleanup
    sanitized = sanitized.strip()

    log_debug(f"AFTER POST-PROCESSING: {sanitized[:100]}...")
    return sanitized

def get_group_context(group_id, last_n=15, include_bot_messages=False):
    """Get the conversation context for a specific group.

    Args:
        group_id: The ID of the group
        last_n: Number of messages to include in the context
        include_bot_messages: Whether to include messages from the bot (default: False)

    Returns:
        str: Formatted conversation context
    """
    if group_id not in group_conversation_history:
        return ""

    # Get the last N messages
    all_messages = group_conversation_history[group_id]

    # Filter out bot messages if requested
    if not include_bot_messages:
        filtered_messages = [msg for msg in all_messages if msg.get('role') != 'assistant']
    else:
        filtered_messages = all_messages

    # Get the last N messages after filtering
    messages = filtered_messages[-last_n:]

    # Format the messages with sanitized usernames
    formatted_messages = []

    for msg in messages:
        # Skip bot/assistant messages if we're not including them
        if not include_bot_messages and (msg.get('role') == 'assistant' or msg['username'] == 'Assistant' or msg['username'] == 'bot'):
            continue

        # Get user ID from the message
        user_id = str(msg.get('user_id', ''))

        # Get a sanitized sender ID for this user
        if user_id and user_id in user_entity_map:
            # Use the sanitized ID from our mapping
            sender_id = user_entity_map[user_id]['sanitized_id']
        else:
            # Create a sanitized ID based on the username
            username = msg['username']
            if username == 'bot' or username == 'Assistant':
                continue  # Skip bot messages
            elif isinstance(username, str) and username.isdigit():
                sender_id = f"User_{abs(hash(username)) % 1000}"
            elif username == 'unknown':
                sender_id = "Someone"
            else:
                sender_id = f"User_{abs(hash(str(username))) % 1000}"

        text = msg['text']
        formatted_messages.append(f"{sender_id}: {text}")

    # If we have no messages after filtering, return empty string
    if not formatted_messages:
        return ""

    # Add a header explaining the format
    header = "CONVERSATION HISTORY (most recent messages from the group chat):"
    return f"{header}\n\n" + "\n".join(formatted_messages)

def is_similar_to_recent_responses(group_id, response):
    """Check if a response is similar to recently sent responses in the same group.

    Args:
        group_id: The ID of the group
        response: The response to check

    Returns:
        bool: True if the response is similar to a recent response, False otherwise
    """
    global recent_responses

    # Initialize if this is a new group
    if group_id not in recent_responses:
        recent_responses[group_id] = []
        return False

    log_debug(f"Checking similarity for response: {response[:50]}...")
    response_lower = response.lower()

    # Method 1: Check for exact repetition of common phrases
    common_phrases = [
        "i ain't got time for this",
        "i'm on a mission",
        "if you ain't got nothing important to say",
        "beat it",
        "what's your problem",
        "i ain't here to play",
        "justice is my game",
        "i'll make them pay",
        "redemption is a bitch"
    ]

    for phrase in common_phrases:
        if phrase in response_lower:
            # Check if any recent response also contains this phrase
            for recent in recent_responses[group_id]:
                if phrase in recent.lower():
                    log_info(f"Rejected response - contains repeated phrase: '{phrase}'")
                    return True

    # Method 2: Check for common phrases between this response and recent ones
    for recent in recent_responses[group_id]:
        # Find common phrases (4+ words)
        shared_phrases = find_common_phrases(response, recent, min_length=4)

        # If we find substantial common phrases, consider it repetitive
        for phrase in shared_phrases:
            if len(phrase) > 20:  # Only consider substantial phrases (20+ chars)
                log_info(f"Rejected response - contains long common phrase with recent response: '{phrase[:30]}...'")
                return True

    # Method 3: Check for overall similarity with recent responses
    for recent in recent_responses[group_id]:
        # Only check substantial responses
        if len(response) > 20 and len(recent) > 20:
            # Calculate Jaccard similarity (word overlap)
            words1 = set(response_lower.split())
            words2 = set(recent.lower().split())

            if len(words1) > 0 and len(words2) > 0:
                similarity = len(words1.intersection(words2)) / len(words1.union(words2))

                if similarity > RESPONSE_SIMILARITY_THRESHOLD:
                    log_info(f"Rejected response - too similar to recent response (similarity: {similarity:.2f})")
                    return True

    return False

def add_to_recent_responses(group_id, response):
    """Add a response to the list of recent responses for a group.

    Args:
        group_id: The ID of the group
        response: The response to add
    """
    global recent_responses

    # Initialize if this is a new group
    if group_id not in recent_responses:
        recent_responses[group_id] = []

    # Add the response to the list
    recent_responses[group_id].append(response)

    # Limit the size of the list
    if len(recent_responses[group_id]) > MAX_RECENT_RESPONSES:
        recent_responses[group_id].pop(0)

def is_trigger_on_cooldown(group_id, trigger_word):
    """Check if a trigger word is on cooldown for a specific group.

    Args:
        group_id: The ID of the group
        trigger_word: The trigger word to check

    Returns:
        bool: True if the trigger word is on cooldown, False otherwise
    """
    global recent_triggers

    # Initialize if this is a new group
    if group_id not in recent_triggers:
        recent_triggers[group_id] = {}
        return False

    # Check if the trigger word is on cooldown
    if trigger_word in recent_triggers[group_id]:
        last_triggered = recent_triggers[group_id][trigger_word]
        if time.time() - last_triggered < TRIGGER_COOLDOWN:
            log_info(f"Trigger word '{trigger_word}' is on cooldown for group {group_id}")
            return True

    return False

def update_trigger_timestamp(group_id, trigger_word):
    """Update the timestamp for a trigger word in a specific group.

    Args:
        group_id: The ID of the group
        trigger_word: The trigger word to update
    """
    global recent_triggers

    # Initialize if this is a new group
    if group_id not in recent_triggers:
        recent_triggers[group_id] = {}

    # Update the timestamp
    recent_triggers[group_id][trigger_word] = time.time()

# ===== TOKEN COUNTING FUNCTIONS =====

def num_tokens_from_string(string: str, model_name: str = "gpt-3.5-turbo") -> int:
    """Returns the number of tokens in a text string."""
    try:
        encoding = tiktoken.encoding_for_model(model_name)
    except KeyError:
        # Use cl100k_base encoding for models not directly supported by tiktoken
        encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(string))

def get_model_context_window(provider_name: str, model_name: str) -> int:
    """Returns the approximate context window size for a given model."""
    # Default to 4096 if unknown
    context_sizes = {
        "openrouter": {
            "default": 4096,
            "anthropic/claude-3-opus": 200000,
            "anthropic/claude-3-sonnet": 200000,
            "anthropic/claude-3-haiku": 200000,
            "anthropic/claude-2": 100000,
            "anthropic/claude-instant-1": 100000,
            "google/gemini-pro": 32768,
            "google/gemini-1.5-pro": 1000000,
            "meta-llama/llama-3-70b-instruct": 8192,
            "meta-llama/llama-3-8b-instruct": 8192,
            "meta-llama/llama-2-70b-chat": 4096,
            "mistralai/mistral-7b-instruct": 8192,
            "mistralai/mixtral-8x7b-instruct": 32768,
            "mistralai/mistral-large": 32768,
            "mistralai/mistral-small": 32768,
            "mistralai/mistral-medium": 32768,
        },
        "cerebras": {
            "default": 4096,
        },
        "cohere": {
            "default": 4096,
            "command": 4096,
            "command-r": 4096,
            "command-r-plus": 128000,
        },
        "mistral": {
            "default": 8192,
            "mistral-tiny": 8192,
            "mistral-small": 8192,
            "mistral-medium": 32768,
            "mistral-large": 32768,
        },
        "codestral": {
            "default": 8192,
            "codestral-v0.1": 8192,
        },
        "lamini": {
            "default": 16384,
        }
    }

    provider_dict = context_sizes.get(provider_name.lower(), {"default": 4096})
    return provider_dict.get(model_name, provider_dict.get("default", 4096))

def log_token_usage(provider_name: str, model_name: str, system_prompt: str, context: str, user_msg: str):
    """Log token usage information for the current request."""
    system_tokens = num_tokens_from_string(system_prompt)
    context_tokens = num_tokens_from_string(context)
    user_msg_tokens = num_tokens_from_string(user_msg)
    total_tokens = system_tokens + context_tokens + user_msg_tokens

    context_window = get_model_context_window(provider_name, model_name)
    usage_percentage = (total_tokens / context_window) * 100

    log_info(f"Token usage - System: {system_tokens}, Context: {context_tokens}, User message: {user_msg_tokens}")
    log_info(f"Total tokens: {total_tokens} / {context_window} ({usage_percentage:.2f}% of context window)")

    # Also log to API debug for more visibility
    log_api_debug(f"TOKEN USAGE: {total_tokens} tokens ({usage_percentage:.2f}% of {context_window} context window)")
    log_api_debug(f"TOKEN BREAKDOWN: System: {system_tokens}, Context: {context_tokens}, User message: {user_msg_tokens}")

    return total_tokens, context_window, usage_percentage

# ===== GLOBAL VARIABLES AND SETTINGS =====

conversation_memory = []
max_memory_items = 20  # Increased from 10 to retain more conversation history
current_character = "default"
character_profiles = {}
runtime_system_prompt = None
group_conversation_history = {}  # Store conversation history per group

# Word frequency tracking variables
word_counts = {}  # Tracks count of each word
word_messages = {}  # Stores messages containing each word
last_reset_time = time.time()  # For periodic reset
WORD_COUNT_THRESHOLD = 3  # Trigger response after 3 mentions
RESET_INTERVAL = 3600  # Reset counts every hour (in seconds)
MAX_GROUP_HISTORY = 50  # Maximum number of messages to store per group

# User entity mapping system
user_entity_map = {}  # Maps user IDs to their entity information

def update_user_entity(user_id, username=None, first_name=None, last_name=None, forwarded=False):
    """Update the user entity map with information about a user.

    Args:
        user_id: The Telegram user ID
        username: The username (if available)
        first_name: The user's first name (if available)
        last_name: The user's last name (if available)
        forwarded: Whether this is from a forwarded message
    """
    if user_id is None:
        return

    # Convert user_id to string for consistency
    user_id = str(user_id)

    if user_id not in user_entity_map:
        user_entity_map[user_id] = {
            'username': username,
            'first_name': first_name,
            'last_name': last_name,
            'display_name': None,  # Will be set below
            'sanitized_id': f"{'Person' if forwarded else 'User'}_{abs(hash(user_id)) % 1000}"
        }
    else:
        # Update existing entry with any new information
        entity = user_entity_map[user_id]
        if username:
            entity['username'] = username
        if first_name:
            entity['first_name'] = first_name
        if last_name:
            entity['last_name'] = last_name

    # Create a display name (prioritize username, then first+last name, then first name)
    entity = user_entity_map[user_id]
    if username:
        entity['display_name'] = username
    elif first_name and last_name:
        entity['display_name'] = f"{first_name} {last_name}"
    elif first_name:
        entity['display_name'] = first_name
    else:
        entity['display_name'] = f"User_{user_id[:4]}"  # Use first 4 chars of ID

def get_sanitized_sender_id(user_id, forwarded=False):
    """Get a sanitized sender ID for API calls."""
    if not user_id:
        return "Someone"

    # Convert user_id to string for consistency
    user_id = str(user_id)

    if user_id in user_entity_map:
        return user_entity_map[user_id]['sanitized_id']
    else:
        return f"{'Person' if forwarded else 'User'}_{abs(hash(user_id)) % 1000}"

async def extract_user_info(event):
    """Extract user information from a message event."""
    # Regular message sender
    if hasattr(event, 'sender') and event.sender:
        update_user_entity(
            event.sender_id,
            username=getattr(event.sender, 'username', None),
            first_name=getattr(event.sender, 'first_name', None),
            last_name=getattr(event.sender, 'last_name', None)
        )

    # Handle forwarded messages
    if hasattr(event.message, 'fwd_from') and event.message.fwd_from:
        # Extract forwarded message sender info
        fwd_from = event.message.fwd_from
        fwd_id = getattr(fwd_from, 'from_id', None)
        if fwd_id:
            fwd_id = fwd_id.user_id if hasattr(fwd_id, 'user_id') else fwd_id
            update_user_entity(
                fwd_id,
                username=getattr(fwd_from, 'from_name', None),
                forwarded=True
            )
        elif hasattr(fwd_from, 'from_name') and fwd_from.from_name:
            # If we only have a name but no ID, create a hash from the name
            name_hash = str(hash(fwd_from.from_name))
            update_user_entity(
                name_hash,
                username=fwd_from.from_name,
                forwarded=True
            )

# ===== ANTI-REPETITION SYSTEM =====
# This system prevents the bot from repeating itself or sending duplicate responses
# It tracks recent responses and trigger words to ensure variety and prevent spam

# Dictionary to track recent responses by group
# Structure: {group_id: [response1, response2, ...]}
recent_responses = {}

# Dictionary to track recently triggered words by group with timestamps
# Structure: {group_id: {word1: timestamp1, word2: timestamp2, ...}}
recent_triggers = {}

# Configuration parameters
MAX_RECENT_RESPONSES = 10  # Number of recent responses to remember per group
RESPONSE_SIMILARITY_THRESHOLD = 0.7  # Threshold for considering responses similar (0.0-1.0)
TRIGGER_COOLDOWN = 300  # Seconds to wait before responding to the same trigger word again (5 minutes)

# The anti-repetition system works by:
# 1. Tracking recent responses in each group
# 2. Checking new responses for similarity to recent ones
# 3. Tracking trigger words with timestamps to implement cooldowns
# 4. Regenerating responses with higher temperature when repetition is detected
# 5. Specifically checking for common repetitive phrases like "I ain't got time for this"

log_info("Starting simplified Telegram bot...")

# ===== API PROVIDER CLASSES =====

class APIProvider:
    """Base class for API providers"""
    def __init__(self, api_key: str, model: str):
        self.api_key = api_key
        self.model = model

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        raise NotImplementedError("Each API provider must implement this method")

    @staticmethod
    def get_providers(config: Dict[str, str]) -> List['APIProvider']:
        """Return a list of initialized API providers"""
        provider_order = config.get('PROVIDER_ORDER', 'openrouter').lower().split(',')
        providers = []

        for provider_name in provider_order:
            provider_name = provider_name.strip()
            api_key = None
            model = config.get('MODEL', '')

            if provider_name == 'openrouter':
                api_key = config.get('OPENROUTER_API_KEY', '')
                model = config.get('OPENROUTER_MODEL', '')
            elif provider_name == 'cerebras':
                api_key = config.get('CEREBRAS_API_KEY', '')
                model = config.get('CEREBRAS_MODEL', '')
            elif provider_name == 'cohere':
                api_key = config.get('COHERE_API_KEY', '')
                model = config.get('COHERE_MODEL', '')
            elif provider_name == 'mistral':
                api_key = config.get('MISTRAL_API_KEY', '')
                model = config.get('MISTRAL_MODEL', '')
            elif provider_name == 'codestral':
                api_key = config.get('CODESTRAL_API_KEY', '')
                model = config.get('CODESTRAL_MODEL', '')
            elif provider_name == 'lamini':
                api_key = config.get('LAMINI_API_KEY', '')
                model = config.get('LAMINI_MODEL', '')
            else:
                log_warning(f"Unknown API provider: {provider_name}")
                continue

            if not api_key:
                log_warning(f"API key missing for provider: {provider_name}")
                continue

            try:
                provider_instance = _get_provider_instance(provider_name, api_key, model)
                providers.append(provider_instance)
                log_info(f"Initialized provider: {provider_name}")
            except Exception as e:
                log_error(f"Failed to initialize provider {provider_name}: {e}")

        if not providers:
            raise ValueError("No API providers could be initialized.")
        return providers

def _get_provider_instance(provider_name: str, api_key: str, model: str) -> 'APIProvider':
    if provider_name == 'openrouter':
        return OpenRouterProvider(api_key, model)
    elif provider_name == 'cerebras':
        return CerebrasProvider(api_key, model)
    elif provider_name == 'cohere':
        return CohereProvider(api_key, model)
    elif provider_name == 'mistral':
        return MistralProvider(api_key, model)
    elif provider_name == 'codestral':
        return CodestralProvider(api_key, model)
    elif provider_name == 'lamini':
        return LaminiProvider(api_key, model)
    raise ValueError(f"Unknown API provider: {provider_name}")

class OpenRouterProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"OpenRouter request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=60) as client:  # Increased timeout to 60 seconds
                log_api_debug(f"Sending request to OpenRouter API...")
                response = client.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                log_api_debug(f"OpenRouter response status: {response.status_code}")
                log_api_debug(f"OpenRouter response headers: {response.headers}")

                # Always log the complete response content for debugging
                log_api_debug(f"OpenRouter API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"OpenRouter API returned status {response.status_code}")
                    return f"__ERROR__: OpenRouter API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"OpenRouter API error: {str(e)}")
            return f"__ERROR__: OpenRouter API error: {str(e)}"

class CerebrasProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"Cerebras request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=60) as client:  # Increased timeout to 60 seconds
                log_api_debug(f"Sending request to Cerebras API...")
                response = client.post(
                    "https://api.cerebras.ai/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Cerebras response status: {response.status_code}")
                log_api_debug(f"Cerebras response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Cerebras API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Cerebras API returned status {response.status_code}")
                    return f"__ERROR__: Cerebras API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Cerebras API error: {str(e)}")
            return f"__ERROR__: Cerebras API error: {str(e)}"

class CohereProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # Cohere API v2 expects different parameter names than OpenAI-compatible APIs
        data = {
            "chat_history": [],  # We'll build this from system prompt and context
            "message": prompt,
            "model": self.model,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        # Add system prompt as a preamble message if provided
        if system_prompt.strip():
            data["preamble"] = system_prompt

        log_api_debug(f"Cohere request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=60) as client:  # Increased timeout to 60 seconds
                log_api_debug(f"Sending request to Cohere API...")
                response = client.post(
                    "https://api.cohere.ai/v1/chat",  # Using v1 endpoint which is more stable
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Cohere response status: {response.status_code}")
                log_api_debug(f"Cohere response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Cohere API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Cohere API returned status {response.status_code}")
                    return f"__ERROR__: Cohere API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            # Extract response based on Cohere's v1 API response format
            if "text" in result:
                return result["text"]
            elif "reply" in result:
                return result["reply"]
            elif "response" in result and "text" in result["response"]:
                return result["response"]["text"]
            elif "generations" in result and len(result["generations"]) > 0:
                return result["generations"][0]["text"]

            # If we can't find the response in the expected fields, return an error
            log_error(f"Could not parse Cohere response format: {json.dumps(result)}")
            return f"__ERROR__: Could not parse Cohere response format: {json.dumps(result)}"

        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Cohere API error: {str(e)}")
            return f"__ERROR__: Cohere API error: {str(e)}"

class MistralProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"Mistral request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=60) as client:  # Increased timeout to 60 seconds
                log_api_debug(f"Sending request to Mistral API...")
                response = client.post(
                    "https://api.mistral.ai/v1/chat/completions",
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Mistral response status: {response.status_code}")
                log_api_debug(f"Mistral response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Mistral API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Mistral API returned status {response.status_code}")
                    return f"__ERROR__: Mistral API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Mistral API error: {str(e)}")
            return f"__ERROR__: Mistral API error: {str(e)}"

class CodestralProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        log_api_debug(f"Codestral request data: {json.dumps(data, indent=2)}")

        try:
            with httpx.Client(timeout=60) as client:  # Increased timeout to 60 seconds
                log_api_debug(f"Sending request to Codestral API...")
                response = client.post(
                    "https://codestral.mistral.ai/v1/chat/completions",  # Correct endpoint for Codestral
                    headers=headers,
                    json=data
                )

                log_api_debug(f"Codestral response status: {response.status_code}")
                log_api_debug(f"Codestral response headers: {response.headers}")

                # Always log the complete response content for debugging without truncation
                log_api_debug(f"Codestral API response: {response.text}")

                if response.status_code != 200:
                    log_error(f"Codestral API returned status {response.status_code}")
                    return f"__ERROR__: Codestral API returned status {response.status_code}: {response.text}"

                response.raise_for_status()

            result = response.json()
            log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

            return result["choices"][0]["message"]["content"]
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            log_api_debug(f"Exception traceback: {tb}")
            log_error(f"Codestral API error: {str(e)}")
            return f"__ERROR__: Codestral API error: {str(e)}"

class LaminiProvider(APIProvider):
    def __init__(self, api_key: str, model: str, api_url: str = "https://api.lamini.ai/inf"):
        self.api_key = api_key
        self.model = model
        self.api_url = api_url

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        log_api_debug(f"Lamini messages: {json.dumps(messages, indent=2)}")

        # Try using OpenAI client first (preferred method)
        try:
            import openai
            client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.api_url,
            )

            log_api_debug(f"Using OpenAI client for Lamini API")
            log_api_debug(f"Lamini request parameters: model={self.model}, temperature={temperature}, max_tokens={max_tokens}")

            try:
                log_api_debug(f"Sending request to Lamini API via OpenAI client...")
                response = client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                log_api_debug(f"Lamini API response via OpenAI client: {response}")
                return response.choices[0].message.content
            except Exception as e:
                import traceback
                tb = traceback.format_exc()
                log_api_debug(f"Exception traceback (OpenAI client): {tb}")
                log_error(f"Lamini API error (OpenAI client): {str(e)}")
                return f"__ERROR__: Lamini API error (OpenAI client): {str(e)}"

        # Fall back to httpx if OpenAI client is not available
        except ImportError:
            log_info("OpenAI package not installed. Using httpx fallback for Lamini API.")
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "OpenAI-Python/1.0",
                "Accept": "application/json"
            }

            data = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            endpoint = f"{self.api_url}/v1/chat/completions"

            log_api_debug(f"Lamini request data (httpx fallback): {json.dumps(data, indent=2)}")

            try:
                with httpx.Client(timeout=60, follow_redirects=True) as client:  # Increased timeout to 60 seconds
                    log_api_debug(f"Sending request to Lamini API via httpx...")
                    response = client.post(endpoint, headers=headers, json=data)

                    log_api_debug(f"Lamini response status: {response.status_code}")
                    log_api_debug(f"Lamini response headers: {response.headers}")

                    # Always log the complete response content for debugging without truncation
                    log_api_debug(f"Lamini API response: {response.text}")

                    if response.status_code != 200:
                        log_error(f"Lamini API returned status {response.status_code}")
                        return f"__ERROR__: Lamini API returned status {response.status_code}: {response.text}"

                    response.raise_for_status()

                result = response.json()
                log_api_debug(f"Parsed JSON response: {json.dumps(result, indent=2)}")

                return result["choices"][0]["message"]["content"]
            except Exception as e:
                import traceback
                tb = traceback.format_exc()
                log_api_debug(f"Exception traceback (httpx): {tb}")
                log_error(f"Lamini API error (httpx): {str(e)}")
                return f"__ERROR__: Lamini API error (httpx): {str(e)}"

# ===== CONFIGURATION FUNCTIONS =====

def load_config(path="config.txt"):
    config = {}
    current_key = None
    multi_line_value = ""
    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""
                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():
                multi_line_value += " " + line.strip()
    if current_key and multi_line_value:
        config[current_key] = multi_line_value.strip()
    return config

# Load configuration
config = load_config()
log_info("Using config.txt for configuration")

api_id = int(config["API_ID"])
api_hash = config["API_HASH"]
group_ids = [int(x.strip()) for x in config["GROUP_IDS"].split(",")]
system_prompt = config.get("SYSTEM_PROMPT", "").strip()
runtime_system_prompt = system_prompt

log_info(f"System prompt length: {len(system_prompt)} characters")

# ===== CHARACTER PROFILE MANAGEMENT =====

def load_character_profiles(profiles_file="characters.json"):
    global character_profiles
    try:
        if os.path.exists(profiles_file):
            with open(profiles_file, "r") as f:
                character_profiles = json.load(f)
            log_info(f"Loaded {len(character_profiles)} character profiles from {profiles_file}")
        else:
            character_profiles = {}
            with open(profiles_file, "w") as f:
                json.dump(character_profiles, f, indent=4)
            log_info(f"Created new empty character profiles file")
    except Exception as e:
        log_error(f"Failed to load character profiles: {e}")
        character_profiles = {}
    for character_id, profile in character_profiles.items():
        log_info(f"Loaded profile: {profile['name']} ({character_id}) - {len(profile['triggers'])} triggers")

def switch_character(character_id):
    global current_character, runtime_system_prompt, system_prompt
    if character_id in character_profiles:
        current_character = character_id

        # Add response quality guidelines to the character profile
        character_prompt = character_profiles[character_id]["prompt"]

        # Add enhanced response guidelines
        response_guidelines = """
RESPONSE QUALITY GUIDELINES:
1. While staying in character, ALWAYS be helpful and engage with the user's actual question or topic
2. NEVER dismiss the user or their questions, even if your character is gruff or tough
3. AVOID generic phrases like "What's your problem?" or "I ain't got time for this"
4. PROVIDE specific, relevant responses that show you're paying attention
5. BALANCE your character's personality with being genuinely helpful
6. REMEMBER that being in character doesn't mean being unhelpful or dismissive
7. ENGAGE with the conversation topic in a meaningful way
8. If asked a direct question, ANSWER it while staying in character
9. CONTRIBUTE something valuable to the conversation
10. AVOID repetitive catchphrases - use them sparingly and naturally
"""

        if system_prompt:
            runtime_system_prompt = f"{system_prompt}\n\nCHARACTER PROFILE:\n{character_prompt}\n\n{response_guidelines}"
        else:
            runtime_system_prompt = f"{character_prompt}\n\n{response_guidelines}"

        log_info(f"Switched to character profile: {character_profiles[character_id]['name']} ({character_id})")
        return True
    log_warning(f"Character profile not found: {character_id}")
    return False

def detect_character_trigger(message):
    message_lower = message.lower()

    # Check for direct character creation requests
    character_request_patterns = [
        r"be a ([^.!?]+)[.!?]*",
        r"be an ([^.!?]+)[.!?]*",
        r"act like a ([^.!?]+)[.!?]*",
        r"act like an ([^.!?]+)[.!?]*",
        r"pretend to be a ([^.!?]+)[.!?]*",
        r"pretend to be an ([^.!?]+)[.!?]*",
        r"roleplay as a ([^.!?]+)[.!?]*",
        r"roleplay as an ([^.!?]+)[.!?]*",
        r"be ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*",  # Matches proper names like "Donald Trump"
        r"act like ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*",
        r"pretend to be ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*",
        r"roleplay as ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*"
    ]

    for pattern in character_request_patterns:
        match = re.search(pattern, message)
        if match:
            full_description = match.group(1).strip()
            words = full_description.split()

            # For proper names or longer descriptions, use more words for matching
            if any(word[0].isupper() for word in words) or len(words) > 2:
                core_type = ' '.join(words[:min(3, len(words))])
            else:
                core_type = ' '.join(words[:min(2, len(words))])

            log_debug(f"Detected character request: '{full_description}' (core type: '{core_type}')")

            # First try exact match on description
            for char_id, profile in character_profiles.items():
                if full_description.lower() == profile["description"].lower():
                    log_info(f"Found exact character match: {profile['name']} ({char_id})")
                    return char_id

            # Then try partial match on name or description
            for char_id, profile in character_profiles.items():
                if core_type.lower() in profile["name"].lower() or core_type.lower() in profile["description"].lower():
                    log_info(f"Found partial character match: {profile['name']} ({char_id})")
                    return char_id

            log_info(f"No existing character matches '{core_type}'")
            return None

    # Check for trigger words in existing characters
    for char_id, profile in character_profiles.items():
        for trigger in profile["triggers"]:
            if re.search(r'\b' + re.escape(trigger) + r'\b', message_lower):
                log_info(f"Triggered character via keyword: {profile['name']} ({char_id})")
                return char_id

    return None

def create_character(description, created_by="user", api_providers=None):
    global character_profiles
    api_providers = api_providers or globals().get('api_providers', [])
    if not api_providers:
        log_error(f"No API providers available for character creation")
        return None
    try:
        words = description.lower().split()
        character_type = ' '.join(words[:min(3, len(words))])
        character_id = re.sub(r'[^a-z0-9]', '_', character_type)
        if character_id in character_profiles:
            character_id = f"{character_id}_{int(time.time())}"
        log_info(f"Creating new character: {description} (ID: {character_id})")
        # Check if this might be a real person (public figure, celebrity, etc.)
        is_real_person = False
        real_person_indicators = ["president", "actor", "actress", "singer", "politician", "celebrity",
                                 "athlete", "artist", "author", "director", "musician", "CEO",
                                 "billionaire", "royal", "leader", "prime minister"]

        for indicator in real_person_indicators:
            if indicator in description.lower():
                is_real_person = True
                break

        # Also check for capitalized words which might indicate proper names
        words = description.split()
        capitalized_words = [word for word in words if word[0].isupper() and len(word) > 1]
        if len(capitalized_words) >= 1:
            is_real_person = True

        if is_real_person:
            log_info(f"Detected potential real person: '{description}'. Using enhanced profile creation.")
            character_prompt = f"""
Create a DETAILED character profile for '{description}' who appears to be a real person or public figure.
Include:
1. NAME: Full name and any common nicknames or titles.
2. BACKGROUND: 2-3 key facts about their origin, education, or rise to prominence (bullet points).
3. CAREER/ACHIEVEMENTS: 2-3 major career milestones or achievements (bullet points).
4. PERSONALITY: 3-5 key personality traits that define them (bullet points).
5. APPEARANCE: 2-3 distinctive physical features or style choices (bullet points).
6. SPEECH PATTERNS: 2-3 distinctive ways they speak or communicate (bullet points).
7. CATCHPHRASES/QUOTES: 3-5 actual quotes or phrases they're known for, including controversial or infamous statements they've actually made.
8. RECENT EVENTS: 2-3 recent activities, projects, or news they've been involved in (bullet points).
9. CONTROVERSIES/SCANDALS: 2-4 notable controversies, scandals, or public criticisms they've faced, including the most infamous ones (bullet points).
10. PUBLIC PERCEPTION: How they're generally viewed by different segments of the public or media (2-3 bullet points).

IMPORTANT:
- Be FACTUAL and ACCURATE about real-world information.
- Include their ACTUAL controversial statements, even if offensive or provocative.
- Include their REAL scandals and controversies, even if sensitive or controversial.
- Include RECENT events and current information (as of your last update).
- DO NOT SPEAK IN 3. PERSON.
- DO NOT sanitize or censor their actual quotes or behaviors.
- DO NOT avoid mentioning major scandals or controversies they're known for.
- DO NOT use generic phrases like 'How can I help you?'.
- Respond naturally and concisely, as the person would actually speak.
- Use your knowledge to answer questions accurately, but filter everything through their voice and personality.
"""
        else:
            log_info(f"Creating fictional character profile for: '{description}'")
            character_prompt = f"""
Create a CONCISE character profile for '{description}' using bullet points and minimal text.
Include:
1. NAME: A fitting name (short and memorable).
2. BACKSTORY: 1-2 sentences maximum about origin and key events.
3. PERSONALITY: 3-5 key traits or quirks (bullet points).
4. APPEARANCE: 2-3 distinctive features (bullet points).
5. BEHAVIORS: 2-3 specific habits (bullet points).
6. CATCHPHRASES: 1-3 short, distinctive phrases.
7. VOICE: 2-3 speech characteristics (bullet points).

IMPORTANT:
- DO NOT SPEAK IN 3. PERSON.
- DO NOT use generic phrases like 'How can I help you?'.
- DO NOT add unnecessary explanations unless asked.
- Respond naturally and concisely, as the character would.
- Maintain character consistency in all responses.
- Stay helpful and responsive, but avoid being overly verbose or robotic.
- Use your knowledge as an LLM to answer questions accurately, but filter everything through the character's voice and personality.
- Keep responses realistic, grounded, and relevant to the conversation.
"""
        detailed_profile = None
        for provider in api_providers:
            log_info(f"Trying provider for character creation: {provider.__class__.__name__}")
            try:
                detailed_profile = provider.generate_response(
                    prompt=character_prompt,
                    system_prompt="You are a character profile creator specializing in detailed, immersive profiles.",
                    temperature=0.8,
                    max_tokens=2000
                )
                if not detailed_profile.startswith("__ERROR__"):
                    log_info(f"Successfully generated character profile with {provider.__class__.__name__}")
                    break
                else:
                    log_warning(f"Provider {provider.__class__.__name__} failed: {detailed_profile}")
            except Exception as e:
                log_error(f"Provider {provider.__class__.__name__} failed: {e}")
        if not detailed_profile or detailed_profile.startswith("__ERROR__"):
            log_error(f"All providers failed to create character profile")
            return None
        name_match = re.search(r'NAME:?\s*([^\n]+)', detailed_profile)
        character_name = name_match.group(1).strip() if name_match else description.split()[0].title()
        if is_real_person:
            prompt = f"""You are {description}.
{detailed_profile}

ROLEPLAY GUIDELINES:
1. Act as {description} with the described traits, speech patterns, and knowledge.
2. Use the person's ACTUAL catchphrases, controversial statements, and speech style naturally BUT DO NOT REPEAT THEM.
3. Stay in character while being helpful and informative.
4. If asked about being AI, acknowledge briefly but remain in character DO NOT ADMIT OR MENTION THAT U ARE AN AI OR LLM.
5. Do not start responses with "How can I help you" or similar phrases.
6. Assume the user already knows what they want; respond directly and intelligently.
7. Draw on your knowledge of real-world events, controversies, and the person's public statements.
8. When asked about scandals or controversies, respond as the real person likely would - don't avoid or sanitize their actual views.
9. When asked about recent events, use your knowledge up to your training cutoff date.
10. Include their authentic mannerisms, catchphrases, and controversial statements in your responses.
11. Don't shy away from their controversial opinions or statements if relevant to the conversation.
12. You are portraying this real person accurately - maintain their authentic voice, opinions, and personality.
"""
        else:
            prompt = f"""You are {description}.
{detailed_profile}

ROLEPLAY GUIDELINES:
1. Act as {description} with described traits and speech patterns.
2. Use character's catchphrases and speech style naturally without overdoing them BUT DO NOT REPEAT THEM.
3. Stay in character while being helpful and informative.
4. If asked about being AI, acknowledge briefly but remain in character DO NOT ADMIT OR MENTION THAT U ARE AN AI OR LLM.
5. Do not start responses with "How can I help you" or similar phrases.
6. Assume the user already knows what they want; respond directly and intelligently.
7. You are both a character and a capable LLM—blend utility with personality.
"""
        triggers = []
        for word in character_name.lower().split():
            if len(word) > 2 and word not in triggers:
                triggers.append(word)
        for word in description.lower().split():
            if len(word) > 3 and word not in ["the", "and", "with", "that", "this", "from", "like", "have"] and word not in triggers:
                triggers.append(word)
        if character_id not in triggers:
            triggers.append(character_id)
        if len(triggers) < 2 and description.split():
            for word in description.split()[:2]:
                clean_word = re.sub(r'[^a-z0-9]', '', word.lower())
                if clean_word and clean_word not in triggers:
                    triggers.append(clean_word)
        character_profiles[character_id] = {
            "name": character_name,
            "description": description,
            "prompt": prompt,
            "triggers": triggers,
            "created_by": created_by
        }
        with open("characters.json", "w") as f:
            json.dump(character_profiles, f, indent=4)
        log_info(f"Successfully created character: {character_name} (ID: {character_id})")
        log_info(f"Triggers: {', '.join(triggers)}")
        return character_id
    except Exception as e:
        log_error(f"Failed to create character: {e}")
        return None

# ===== SYSTEM PROMPT MANAGEMENT =====

def update_system_prompt(new_content):
    global runtime_system_prompt
    runtime_system_prompt = new_content
    log_info(f"Updated runtime system prompt: {len(runtime_system_prompt)} characters")

def extract_topics(message):
    topics = []
    if len(message) < 10:
        return topics
    words = message.split()
    for word in words:
        if (len(word) > 4 and word[0].isupper() and
            word.lower() not in ['this', 'that', 'these', 'those', 'there', 'their', 'about']):
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word and len(clean_word) > 4:
                topics.append(clean_word)
    return topics

def process_message_words(message_text, message_id, sender_info, group_id):
    """
    Process words in a message, count frequencies, and trigger responses when threshold is reached.

    Args:
        message_text: The text content of the message
        message_id: Unique identifier for the message
        sender_info: Information about the sender (username or ID)
        group_id: The ID of the group where the message was sent

    Returns:
        tuple: (trigger_word, context_messages) if a response should be triggered,
               (None, None) otherwise
    """
    global word_counts, word_messages, last_reset_time

    # Check if we should reset the counters (hourly)
    current_time = time.time()
    if current_time - last_reset_time > RESET_INTERVAL:
        word_counts = {}
        word_messages = {}
        last_reset_time = current_time
        log_debug("Reset word frequency counters")

    # Skip processing for very short messages
    if not message_text or len(message_text) < 2:
        return None, None

    # Extract words from the message
    words = message_text.lower().split()

    # Create a clean version of words (alphanumeric only)
    clean_words = []
    for word in words:
        # Remove punctuation and keep only alphanumeric characters
        clean_word = ''.join(c for c in word if c.isalnum())
        if clean_word and len(clean_word) > 2:  # Only track words with 3+ characters
            clean_words.append(clean_word)

    # Skip common words that would trigger too frequently
    common_words = {'the', 'and', 'for', 'that', 'this', 'with', 'you', 'have', 'are', 'not', 'from'}
    filtered_words = [w for w in clean_words if w not in common_words]

    # Store message info for context
    # Get a sanitized sender ID using our user entity mapping
    user_id = str(sender_info)

    # Update the user entity map with this sender
    update_user_entity(user_id, username=sender_info)

    # Get the sanitized ID for API calls
    sanitized_id = get_sanitized_sender_id(user_id)

    message_info = {
        'text': message_text,
        'sender': sanitized_id,
        'sender_raw': sender_info,  # Keep the raw sender info for internal use
        'user_id': user_id,  # Store the user ID for entity mapping
        'group_id': group_id,
        'timestamp': current_time
    }

    # Update word counts and message storage
    trigger_word = None
    for word in filtered_words:
        # Initialize if this is a new word
        if word not in word_counts:
            word_counts[word] = 0
            word_messages[word] = []

        # Update count
        word_counts[word] += 1

        # Store message (keep only last 5 messages per word)
        word_messages[word].append(message_info)
        if len(word_messages[word]) > 5:
            word_messages[word].pop(0)

        # Check if this word has reached the threshold
        if word_counts[word] == WORD_COUNT_THRESHOLD:
            trigger_word = word
            log_info(f"Word frequency threshold reached for '{word}' ({WORD_COUNT_THRESHOLD} mentions)")

    # If a word reached the threshold, return it and its context
    if trigger_word:
        context_messages = word_messages[trigger_word]
        # Reset the counter for this word to prevent repeated triggers
        word_counts[trigger_word] = 0
        return trigger_word, context_messages

    return None, None

async def generate_frequency_response(trigger_word, context_messages, api_providers, group_id=None, temperature=0.7):
    """
    Generate a response based on word frequency trigger.

    Args:
        trigger_word: The word that triggered the response
        context_messages: List of messages containing the trigger word
        api_providers: List of available API providers
        group_id: The ID of the group where the response will be sent
        temperature: Temperature parameter for response generation (higher = more creative)

    Returns:
        str: The generated response
    """
    # Format the context messages from the trigger word with better formatting
    trigger_context = ""
    for msg in context_messages:
        # Format with sanitized sender information
        sender = msg['sender']
        # Sanitize sender information
        if isinstance(sender, str) and sender.isdigit():
            sender = f"Someone_{hash(sender) % 100}"
        elif isinstance(sender, int):
            sender = f"Someone_{hash(str(sender)) % 100}"
        elif sender == 'unknown':
            sender = "Someone"

        trigger_context += f"{sender}: {msg['text']}\n"

    # Add a note about how many messages triggered the response
    trigger_count = len(context_messages)
    trigger_context = f"Found {trigger_count} recent messages containing the word '{trigger_word}':\n\n{trigger_context}"

    # Get broader conversation context from the group (excluding bot messages)
    broader_context = ""
    if group_id and group_id in group_conversation_history:
        broader_context = get_group_context(group_id, last_n=15, include_bot_messages=False)

    # Create a prompt for the API with enhanced context
    prompt = f"""You are participating in a group chat conversation. The word "{trigger_word}" has been mentioned several times.

IMPORTANT - THESE ARE THE SPECIFIC MESSAGES CONTAINING THE WORD "{trigger_word}":
{trigger_context}

PLEASE FOCUS ON RESPONDING TO THESE SPECIFIC MESSAGES FIRST

HERE IS THE REST OF THE CONVERSATION HISTORY FOR ADDITIONAL CONTEXT:
{broader_context}

Join this conversation naturally as if you were another person in the group chat.

Key guidelines:
1. DO NOT mention that you noticed the word "{trigger_word}" being used frequently
2. DO NOT use generic phrases like "How can I help?" or "What's your problem?"
3. DO NOT refer to people by numeric IDs or usernames
4. ENGAGE with the actual conversation topic in a meaningful way
5. CONTRIBUTE something interesting and relevant to the ongoing conversation
6. STAY conversational and natural
7. If you need to refer to someone, use general terms like "folks" or "everyone"

Your response should feel like a natural contribution to the conversation from someone who has been following along and wants to add value."""

    # Use the current character's system prompt if available
    system_prompt_text = runtime_system_prompt or ""

    # Add enhanced response guidelines that prioritize helpfulness while maintaining character
    response_quality_guidelines = """
CORE RESPONSE GUIDELINES:
1. MAINTAIN your character's personality and speech patterns
2. ENGAGE with the actual conversation topic in a meaningful way
3. BE SPECIFIC and helpful in your responses
4. CONTRIBUTE something valuable to the conversation
5. STAY conversational and natural
6. If the topic requires expertise, provide it while staying in character
7. USE character catchphrases sparingly and naturally

IMPORTANT FORMATTING NOTES:
- DO NOT use numeric IDs or usernames when referring to people
- DO NOT refer to specific message numbers
- DO NOT use generic phrases like "How can I help?"
- DO NOT be repetitive in your responses

GOOD RESPONSE EXAMPLE:
"The weather is indeed nice today. You're right about the forecast."
"""

    if system_prompt_text:
        system_prompt_text += f"\n\n{response_quality_guidelines}"
    else:
        system_prompt_text = f"You are joining a group conversation. {response_quality_guidelines}"

    # Try each provider until we get a response
    response = None
    for provider in api_providers:
        provider_name = provider.__class__.__name__
        model_name = provider.model
        log_info(f"Generating frequency response using provider: {provider_name}")

        try:
            response = provider.generate_response(
                prompt=prompt,
                system_prompt=system_prompt_text,
                temperature=temperature,  # Use the provided temperature parameter
                max_tokens=1000
            )
            if not response.startswith("__ERROR__"):
                log_info(f"Successfully generated frequency response with {provider_name}")
                break
            else:
                log_warning(f"{provider_name} failed for frequency response: {response}")
        except Exception as e:
            log_error(f"{provider_name} error for frequency response: {str(e)}")

    return response

# ===== INITIALIZATION =====

# Initialize API providers
try:
    api_providers = APIProvider.get_providers(config)
    provider_names = [p.__class__.__name__ for p in api_providers]
    log_info(f"Initialized {len(api_providers)} providers: {', '.join(provider_names)}")
except Exception as e:
    log_error(f"Failed to initialize API providers: {e}")
    exit(1)

# Initialize Telegram client
log_info(f"Listening to groups: {group_ids}")
client = TelegramClient('user_session', api_id, api_hash)

# ===== MESSAGE HANDLER =====

@client.on(events.NewMessage(chats=group_ids))
async def on_new_message(event):
    """Handle new messages in monitored groups"""
    try:
        me = await client.get_me()

        # Get sender information
        sender_id = event.sender_id
        sender_username = event.sender.username if hasattr(event.sender, 'username') else None
        sender_first_name = event.sender.first_name if hasattr(event.sender, 'first_name') else None
        sender_last_name = event.sender.last_name if hasattr(event.sender, 'last_name') else None
        group_id = event.chat_id

        # Extract and store user information
        await extract_user_info(event)

        # Save all text messages regardless of type
        # This includes forwarded messages and text attachments
        text_content = None
        if event.message.message:  # Check if there's any text content
            text_content = event.message.message
            save_group_message(group_id, sender_id, sender_username, text_content)
            log_debug(f"Saved message from {sender_username or sender_id} in group {group_id}")

        # Skip processing for bot responses if it's from the bot itself
        if sender_id == me.id:
            return

        # Skip non-text messages and empty messages for bot processing
        if event.message.media or event.message.action:
            return

        user_msg = event.raw_text.strip() if event.raw_text else ""
        if not user_msg:
            return

        # Process words for frequency tracking (only for non-bot messages with text)
        if text_content:
            # Use message ID as a unique identifier
            message_id = event.message.id
            sender_display = sender_username or str(sender_id)

            # Process the message words and check if we need to respond
            trigger_word, context_messages = process_message_words(
                text_content,
                message_id,
                sender_display,
                group_id
            )

            # If a word reached the threshold, check cooldown and generate response
            if trigger_word and context_messages:
                # Check if this trigger word is on cooldown
                if is_trigger_on_cooldown(group_id, trigger_word):
                    log_info(f"Skipping response for '{trigger_word}' - trigger is on cooldown")
                    return

                log_info(f"Generating response for frequent word: '{trigger_word}'")
                async with client.action(event.chat_id, 'typing'):
                    # Update trigger timestamp to prevent duplicate responses
                    update_trigger_timestamp(group_id, trigger_word)

                    # Generate response
                    response = await generate_frequency_response(
                        trigger_word,
                        context_messages,
                        api_providers,
                        group_id=group_id
                    )

                    if response and not response.startswith("__ERROR__"):
                        # Check if response is similar to recent responses
                        if is_similar_to_recent_responses(group_id, response):
                            log_info(f"Skipping similar response for '{trigger_word}'")
                            # Try to regenerate with higher temperature for more variety
                            response = await generate_frequency_response(
                                trigger_word,
                                context_messages,
                                api_providers,
                                group_id=group_id,
                                temperature=0.9  # Higher temperature for more variety
                            )
                            # If still similar, skip this response
                            if is_similar_to_recent_responses(group_id, response):
                                log_info(f"Still got similar response, skipping trigger for '{trigger_word}'")
                                return

                        # Apply comprehensive post-processing to the response
                        response = post_process_response(response, group_id)

                        # Send the processed response to the group (not as a reply to any specific message)
                        sent_message = await client.send_message(event.chat_id, response)
                        log_info(f"Sent frequency-triggered response: {response}")

                        # Add to recent responses to prevent repetition
                        add_to_recent_responses(group_id, response)

                        # Also save the bot's response to the message log
                        if sent_message:
                            save_group_message(event.chat_id, 'bot', 'Assistant', response)
                            log_debug(f"Saved frequency-triggered bot response in group {event.chat_id}")
                    else:
                        log_error(f"Failed to generate frequency response: {response}")

        # Check if the message is a reply to one of our messages
        is_reply_to_me = False
        replied_to_message_content = None
        if event.reply_to:
            try:
                replied_msg = await event.get_reply_message()
                if replied_msg and replied_msg.sender_id == me.id:
                    is_reply_to_me = True
                    replied_to_message_content = replied_msg.message
                    log_info(f"Message is a reply to me: '{replied_to_message_content[:50]}...'")
            except Exception as e:
                log_error(f"Error checking reply: {e}")

        # Check if the message mentions/tags the bot
        is_mentioned = False
        mentioned_message_content = None
        if hasattr(event.message, 'mentioned') and event.message.mentioned:
            is_mentioned = True
            mentioned_message_content = user_msg
            log_info(f"Bot was mentioned/tagged in the message: '{mentioned_message_content[:50]}...'")

        # Also check for username mention in text
        bot_username = me.username
        if bot_username and f"@{bot_username}" in user_msg:
            is_mentioned = True
            mentioned_message_content = user_msg
            # Remove the mention from the message for processing
            user_msg_clean = user_msg.replace(f"@{bot_username}", "").strip()
            log_info(f"Bot username was mentioned in text: '{user_msg[:50]}...'")
            # Keep the original message with mention for context
            user_msg = user_msg_clean

        # Only proceed with direct bot response if the message is a reply to the bot or mentions the bot
        if not (is_reply_to_me or is_mentioned):
            log_debug(f"Ignoring message for direct bot response - not a reply or mention")
            return

        log_info(f"Processing message from {event.sender.username or event.sender_id}: {user_msg}")

        # Check for character switching
        new_character = detect_character_trigger(user_msg)
        if new_character and new_character != current_character:
            switch_character(new_character)

        # Check for character creation requests
        character_request_patterns = [
            r"be a ([^.!?]+)[.!?]*",
            r"be an ([^.!?]+)[.!?]*",
            r"act like a ([^.!?]+)[.!?]*",
            r"act like an ([^.!?]+)[.!?]*",
            r"pretend to be a ([^.!?]+)[.!?]*",
            r"pretend to be an ([^.!?]+)[.!?]*",
            r"roleplay as a ([^.!?]+)[.!?]*",
            r"roleplay as an ([^.!?]+)[.!?]*",
            r"be ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*",  # Matches proper names like "Donald Trump"
            r"act like ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*",
            r"pretend to be ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*",
            r"roleplay as ([A-Z][a-z]+ [A-Z][a-z]+)[.!?]*"
        ]

        for pattern in character_request_patterns:
            # Use original case for patterns that might match proper names
            if "A-Z" in pattern:
                match = re.search(pattern, user_msg)
            else:
                match = re.search(pattern, user_msg.lower())

            if match:
                description = match.group(1).strip()
                log_info(f"Creating character: {description}")
                async with client.action(event.chat_id, 'typing'):
                    new_character_id = create_character(description, api_providers=api_providers)
                    if new_character_id:
                        switch_character(new_character_id)
                return

        # Add message to conversation memory
        conversation_memory.append({"role": "user", "content": user_msg})
        if len(conversation_memory) > max_memory_items:
            conversation_memory.pop(0)

        # Get broader conversation context from the group (excluding bot messages)
        group_context = get_group_context(group_id, last_n=15, include_bot_messages=False)

        # Prepare context and system prompt with enhanced context
        # Format direct conversation with user role but skip assistant messages
        context_items = []
        for item in conversation_memory[-10:]:
            if item['role'] == 'user':
                context_items.append(f"User: {item['content']}")
            elif item['role'] == 'assistant':
                # Skip assistant messages to save tokens and avoid confusion
                continue
        context = "\n".join(context_items)

        enhanced_system_prompt = """
CORE RESPONSE GUIDELINES:
1. MAINTAIN your character's personality and speech patterns
2. ENGAGE with the user's actual question or topic in a meaningful way
3. BE SPECIFIC and helpful in your responses
4. REMEMBER details from the conversation history
5. STAY conversational and natural
6. If asked a direct question, ANSWER it while staying in character
7. USE character catchphrases sparingly and naturally

IMPORTANT FORMATTING NOTES:
- DO NOT use numeric IDs or usernames when referring to people
- DO NOT refer to specific message numbers
- DO NOT use generic phrases like "How can I help?"
- DO NOT be repetitive in your responses

GOOD RESPONSE EXAMPLE:
"The weather is indeed nice today. You're right about the forecast. That's a good point about the chance of rain."
"""

        # Prepare special context for replied-to messages
        special_context = ""
        if is_reply_to_me and replied_to_message_content:
            special_context = f"""
IMPORTANT - SOMEONE IS REPLYING TO YOUR PREVIOUS MESSAGE:

Your previous message: {replied_to_message_content}

Their reply: {user_msg}

PLEASE FOCUS ON RESPONDING TO THIS SPECIFIC INTERACTION FIRST
"""
        elif is_mentioned and mentioned_message_content:
            special_context = f"""
IMPORTANT - YOU WERE MENTIONED/TAGGED IN THIS MESSAGE:

Message where you were tagged: {mentioned_message_content}

PLEASE FOCUS ON RESPONDING TO THIS SPECIFIC MESSAGE FIRST
"""

        # Add the group context to provide more conversation history
        context_with_history = f"""
{special_context}
HERE IS ADDITIONAL CONVERSATION CONTEXT:

{group_context}

PREVIOUS MESSAGES FROM THIS CONVERSATION:
{context}
"""

        system_prompt_text = f"{runtime_system_prompt}\n\n{enhanced_system_prompt}\n\nRecent conversation context: {context_with_history}" if runtime_system_prompt else f"{enhanced_system_prompt}\n\nRecent conversation context: {context_with_history}"

        # Generate response using available providers
        async with client.action(event.chat_id, 'typing'):
            await client.send_read_acknowledge(event.chat_id, event.message)
            response = None

            for provider in api_providers:
                provider_name = provider.__class__.__name__
                model_name = provider.model
                log_info(f"Trying provider: {provider_name} with model: {model_name}")

                # Log token usage before making the API call
                total_tokens, context_window, usage_percentage = log_token_usage(
                    provider_name.replace("Provider", "").lower(),
                    model_name,
                    system_prompt_text,
                    context,
                    user_msg
                )

                try:
                    response = provider.generate_response(
                        prompt=user_msg,
                        system_prompt=system_prompt_text,
                        temperature=0.7,
                        max_tokens=1000
                    )
                    if not response.startswith("__ERROR__"):
                        log_info(f"Success with {provider_name}")
                        break
                    else:
                        log_warning(f"{provider_name} failed: {response}")
                except Exception as e:
                    log_error(f"{provider_name} error: {str(e)}")

            # Send response if successful
            if response and not response.startswith("__ERROR__"):
                # Check if response is similar to recent responses
                if is_similar_to_recent_responses(group_id, response):
                    log_info(f"Detected similar response, regenerating with higher temperature")
                    # Try to regenerate with higher temperature for more variety
                    for provider in api_providers:
                        provider_name = provider.__class__.__name__
                        model_name = provider.model
                        log_info(f"Retrying with higher temperature: {provider_name}")

                        try:
                            response = provider.generate_response(
                                prompt=user_msg,
                                system_prompt=system_prompt_text,
                                temperature=0.9,  # Higher temperature for more variety
                                max_tokens=1000
                            )
                            if not response.startswith("__ERROR__"):
                                log_info(f"Success with {provider_name} (higher temperature)")
                                break
                        except Exception as e:
                            log_error(f"{provider_name} error: {str(e)}")

                    # If still similar, use it anyway but log a warning
                    if is_similar_to_recent_responses(group_id, response):
                        log_warning(f"Still got similar response, using it anyway")

                # Add to conversation memory and recent responses
                conversation_memory.append({"role": "assistant", "content": response})
                if len(conversation_memory) > max_memory_items:
                    conversation_memory.pop(0)

                # Add to recent responses to prevent repetition
                add_to_recent_responses(group_id, response)

                # Apply comprehensive post-processing to the response
                response = post_process_response(response, group_id)

                # Send the processed response
                sent_message = await event.reply(response)
                log_info(f"Sent: {response}")

                # Also save the bot's response to the message log
                if sent_message:
                    save_group_message(event.chat_id, 'bot', 'Assistant', response)
                    log_debug(f"Saved bot response in group {event.chat_id}")
            else:
                log_error(f"All providers failed")

    except Exception as e:
        log_error(f"Message processing error: {e}")

# ===== MAIN EXECUTION =====

async def main():
    """Main execution function"""
    log_info("Starting Telegram bot...")
    log_info("Message saving is ENABLED - all text messages from monitored groups will be saved to group_messages.log")
    log_info(f"Word frequency response is ENABLED - bot will respond when a word is mentioned {WORD_COUNT_THRESHOLD} times")
    log_info(f"Enhanced context handling is ENABLED - bot will remember up to {MAX_GROUP_HISTORY} messages per group")
    log_info("Improved response quality guidelines are ENABLED - bot will balance character traits with being helpful")
    log_info(f"Anti-repetition system is ENABLED - bot will avoid repeating itself and track {MAX_RECENT_RESPONSES} recent responses")
    log_info(f"Trigger cooldown is ENABLED - {TRIGGER_COOLDOWN} seconds cooldown between responses to the same trigger word")

    # Start client
    await client.start()

    # Verify group connections
    try:
        if group_ids:
            for group_id in group_ids:
                await client.get_entity(group_id)
            log_info(f"Connected to {len(group_ids)} groups")
            log_info(f"Saving messages from {len(group_ids)} monitored groups")
        else:
            log_warning("No group IDs configured")
    except Exception as e:
        log_error(f"Group connection error: {e}")

    # Load character profiles
    load_character_profiles()

    # Set initial character or default mode
    global runtime_system_prompt
    if character_profiles:
        character_keys = sorted(list(character_profiles.keys()))
        first_character = character_keys[0]
        switch_character(first_character)
        log_info(f"Starting with character: {character_profiles[first_character]['name']}")
    else:
        log_info("No characters found. Use 'be a [character]' to create one.")
        runtime_system_prompt = ""  # Empty system prompt for default behavior

    log_info("Bot is running. Press Ctrl+C to stop.")

# Run the bot
client.loop.run_until_complete(main())
client.run_until_disconnected()