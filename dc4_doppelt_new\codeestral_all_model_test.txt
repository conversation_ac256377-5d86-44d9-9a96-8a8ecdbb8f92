litellm-env) oo7@LAPTOP-07N9FDLB:~/colestart/dc4_doppelt_new$ chmod +x check_models.py && python check_models.py
2025-05-19 13:18:36,436 - DEBUG - Request headers: {'Authorization': 'Bearer AMpMVqFQPpsTTzsq9HVqUWEs2jVEXcPw', 'Content-Type': 'application/json'}
2025-05-19 13:18:36,442 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:36,939 - DEBUG - https://api.mistral.ai:443 "GET /v1/models HTTP/1.1" 200 None
2025-05-19 13:18:36,942 - DEBUG - Response status code: 200
2025-05-19 13:18:36,942 - DEBUG - Response text: {"object":"list","data":[{"id":"ministral-3b-2410","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"ministral-3b-2410","description":"Official ministral-3b-2410 Mistral AI model","max_context_length":131072,"aliases":["ministral-3b-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"ministral-3b-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"ministral-3b-2410","description":"Official ministral-3b-2410 Mistral AI model","max_context_length":131072,"aliases":["ministral-3b-2410"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"ministral-8b-2410","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"ministral-8b-2410","description":"Official ministral-8b-2410 Mistral AI model","max_context_length":131072,"aliases":["ministral-8b-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"ministral-8b-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"ministral-8b-2410","description":"Official ministral-8b-2410 Mistral AI model","max_context_length":131072,"aliases":["ministral-8b-2410"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"open-mistral-7b","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mistral-7b","description":"Official open-mistral-7b Mistral AI model","max_context_length":32768,"aliases":["mistral-tiny","mistral-tiny-2312"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-tiny","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mistral-7b","description":"Official open-mistral-7b Mistral AI model","max_context_length":32768,"aliases":["open-mistral-7b","mistral-tiny-2312"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-tiny-2312","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mistral-7b","description":"Official open-mistral-7b Mistral AI model","max_context_length":32768,"aliases":["open-mistral-7b","mistral-tiny"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"open-mistral-nemo","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"open-mistral-nemo","description":"Official open-mistral-nemo Mistral AI model","max_context_length":131072,"aliases":["open-mistral-nemo-2407","mistral-tiny-2407","mistral-tiny-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"open-mistral-nemo-2407","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"open-mistral-nemo","description":"Official open-mistral-nemo Mistral AI model","max_context_length":131072,"aliases":["open-mistral-nemo","mistral-tiny-2407","mistral-tiny-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-tiny-2407","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"open-mistral-nemo","description":"Official open-mistral-nemo Mistral AI model","max_context_length":131072,"aliases":["open-mistral-nemo","open-mistral-nemo-2407","mistral-tiny-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-tiny-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"open-mistral-nemo","description":"Official open-mistral-nemo Mistral AI model","max_context_length":131072,"aliases":["open-mistral-nemo","open-mistral-nemo-2407","mistral-tiny-2407"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"open-mixtral-8x7b","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mixtral-8x7b","description":"Official open-mixtral-8x7b Mistral AI model","max_context_length":32768,"aliases":["mistral-small","mistral-small-2312"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-small","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mixtral-8x7b","description":"Official open-mixtral-8x7b Mistral AI model","max_context_length":32768,"aliases":["open-mixtral-8x7b","mistral-small-2312"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-small-2312","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mixtral-8x7b","description":"Official open-mixtral-8x7b Mistral AI model","max_context_length":32768,"aliases":["open-mixtral-8x7b","mistral-small"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"open-mixtral-8x22b","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mixtral-8x22b","description":"Official open-mixtral-8x22b Mistral AI model","max_context_length":65536,"aliases":["open-mixtral-8x22b-2404"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"open-mixtral-8x22b-2404","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"open-mixtral-8x22b","description":"Official open-mixtral-8x22b Mistral AI model","max_context_length":65536,"aliases":["open-mixtral-8x22b"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-small-2402","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"mistral-small-2402","description":"Official mistral-small-2402 Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-small-2409","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"mistral-small-2409","description":"Official mistral-small-2409 Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-medium-2312","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-medium-2312","description":"Official mistral-medium-2312 Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-large-2402","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-large-2402","description":"Official mistral-large-2402 Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-large-2407","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"mistral-large-2407","description":"Official mistral-large-2407 Mistral AI model","max_context_length":131072,"aliases":[],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-large-2411","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"mistral-large-2411","description":"Official mistral-large-2411 Mistral AI model","max_context_length":131072,"aliases":["mistral-large-latest"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-large-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"mistral-large-2411","description":"Official mistral-large-2411 Mistral AI model","max_context_length":131072,"aliases":["mistral-large-2411"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"pixtral-large-2411","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"pixtral-large-2411","description":"Official pixtral-large-2411 Mistral AI model","max_context_length":131072,"aliases":["pixtral-large-latest","mistral-large-pixtral-2411"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"pixtral-large-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"pixtral-large-2411","description":"Official pixtral-large-2411 Mistral AI model","max_context_length":131072,"aliases":["pixtral-large-2411","mistral-large-pixtral-2411"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"mistral-large-pixtral-2411","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"pixtral-large-2411","description":"Official pixtral-large-2411 Mistral AI model","max_context_length":131072,"aliases":["pixtral-large-2411","pixtral-large-latest"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"codestral-2405","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":true,"function_calling":true,"fine_tuning":true,"vision":false,"classification":false},"name":"codestral-2405","description":"Official codestral-2405 Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"codestral-2501","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":true,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-2501","description":"Official codestral-2501 Mistral AI model","max_context_length":262144,"aliases":["codestral-latest","codestral-2412","codestral-2411-rc5"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"codestral-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":true,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-2501","description":"Official codestral-2501 Mistral AI model","max_context_length":262144,"aliases":["codestral-2501","codestral-2412","codestral-2411-rc5"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"codestral-2412","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":true,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-2501","description":"Official codestral-2501 Mistral AI model","max_context_length":262144,"aliases":["codestral-2501","codestral-latest","codestral-2411-rc5"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"codestral-2411-rc5","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":true,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-2501","description":"Official codestral-2501 Mistral AI model","max_context_length":262144,"aliases":["codestral-2501","codestral-latest","codestral-2412"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"codestral-mamba-2407","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-mamba-2407","description":"Official codestral-mamba-2407 Mistral AI model","max_context_length":262144,"aliases":["open-codestral-mamba","codestral-mamba-latest"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"open-codestral-mamba","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-mamba-2407","description":"Official codestral-mamba-2407 Mistral AI model","max_context_length":262144,"aliases":["codestral-mamba-2407","codestral-mamba-latest"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"codestral-mamba-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"codestral-mamba-2407","description":"Official codestral-mamba-2407 Mistral AI model","max_context_length":262144,"aliases":["codestral-mamba-2407","open-codestral-mamba"],"deprecation":null,"default_model_temperature":0.7,"type":"base"},{"id":"pixtral-12b-2409","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"pixtral-12b-2409","description":"Official pixtral-12b-2409 Mistral AI model","max_context_length":131072,"aliases":["pixtral-12b","pixtral-12b-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"pixtral-12b","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"pixtral-12b-2409","description":"Official pixtral-12b-2409 Mistral AI model","max_context_length":131072,"aliases":["pixtral-12b-2409","pixtral-12b-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"pixtral-12b-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"pixtral-12b-2409","description":"Official pixtral-12b-2409 Mistral AI model","max_context_length":131072,"aliases":["pixtral-12b-2409","pixtral-12b"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-small-2501","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-small-2501","description":"Official mistral-small-2501 Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-small-2503","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"mistral-small-2503","description":"Official mistral-small-2503 Mistral AI model","max_context_length":131072,"aliases":["mistral-small-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-small-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"mistral-small-2503","description":"Official mistral-small-2503 Mistral AI model","max_context_length":131072,"aliases":["mistral-small-2503"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-saba-2502","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-saba-2502","description":"Official mistral-saba-2502 Mistral AI model","max_context_length":32768,"aliases":["mistral-saba-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-saba-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-saba-2502","description":"Official mistral-saba-2502 Mistral AI model","max_context_length":32768,"aliases":["mistral-saba-2502"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-medium-2505","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"mistral-medium-2505","description":"Official mistral-medium-2505 Mistral AI model","max_context_length":131072,"aliases":["mistral-medium-latest","mistral-medium"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-medium-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"mistral-medium-2505","description":"Official mistral-medium-2505 Mistral AI model","max_context_length":131072,"aliases":["mistral-medium-2505","mistral-medium"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-medium","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":true,"completion_fim":false,"function_calling":true,"fine_tuning":false,"vision":true,"classification":false},"name":"mistral-medium-2505","description":"Official mistral-medium-2505 Mistral AI model","max_context_length":131072,"aliases":["mistral-medium-2505","mistral-medium-latest"],"deprecation":null,"default_model_temperature":0.3,"type":"base"},{"id":"mistral-embed","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":false,"completion_fim":false,"function_calling":false,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-embed","description":"Official mistral-embed Mistral AI model","max_context_length":32768,"aliases":[],"deprecation":null,"default_model_temperature":null,"type":"base"},{"id":"mistral-moderation-2411","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":false,"completion_fim":false,"function_calling":false,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-moderation-2411","description":"Official mistral-moderation-2411 Mistral AI model","max_context_length":32768,"aliases":["mistral-moderation-latest"],"deprecation":null,"default_model_temperature":null,"type":"base"},{"id":"mistral-moderation-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":false,"completion_fim":false,"function_calling":false,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-moderation-2411","description":"Official mistral-moderation-2411 Mistral AI model","max_context_length":32768,"aliases":["mistral-moderation-2411"],"deprecation":null,"default_model_temperature":null,"type":"base"},{"id":"mistral-ocr-2503","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":false,"completion_fim":false,"function_calling":false,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-ocr-2503","description":"Official mistral-ocr-2503 Mistral AI model","max_context_length":32768,"aliases":["mistral-ocr-latest"],"deprecation":null,"default_model_temperature":null,"type":"base"},{"id":"mistral-ocr-latest","object":"model","created":1747653518,"owned_by":"mistralai","capabilities":{"completion_chat":false,"completion_fim":false,"function_calling":false,"fine_tuning":false,"vision":false,"classification":false},"name":"mistral-ocr-2503","description":"Official mistral-ocr-2503 Mistral AI model","max_context_length":32768,"aliases":["mistral-ocr-2503"],"deprecation":null,"default_model_temperature":null,"type":"base"}]}
Checking model: ministral-3b-2410
2025-05-19 13:18:36,947 - DEBUG - Checking model: ministral-3b-2410
2025-05-19 13:18:36,951 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:37,204 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:37,206 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:37,207 - DEBUG - Response: 200 - {"id":"6c2f1d1c8e9743cc8ba1dd442e92f130","object":"chat.completion","created":1747653518,"model":"ministral-3b-2410","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you. How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":13,"total_tokens":32,"completion_tokens":19}}
Model ministral-3b-2410 is working
Checking model: ministral-3b-latest
2025-05-19 13:18:37,208 - DEBUG - Checking model: ministral-3b-latest
2025-05-19 13:18:37,210 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:37,677 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:37,679 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:37,679 - DEBUG - Response: 200 - {"id":"591ffc7254d2402a804ef9dd248858ad","object":"chat.completion","created":1747653519,"model":"ministral-3b-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you. How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":13,"total_tokens":32,"completion_tokens":19}}
Model ministral-3b-latest is working
Checking model: ministral-8b-2410
2025-05-19 13:18:37,680 - DEBUG - Checking model: ministral-8b-2410
2025-05-19 13:18:37,682 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:37,823 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:37,827 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:37,827 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model ministral-8b-2410 is not working. Status code: 429
Checking model: ministral-8b-latest
2025-05-19 13:18:37,830 - DEBUG - Checking model: ministral-8b-latest
2025-05-19 13:18:37,834 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:37,981 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:37,982 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:37,982 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model ministral-8b-latest is not working. Status code: 429
Checking model: open-mistral-7b
2025-05-19 13:18:37,983 - DEBUG - Checking model: open-mistral-7b
2025-05-19 13:18:37,984 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:38,122 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:38,125 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:38,126 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model open-mistral-7b is not working. Status code: 429
Checking model: mistral-tiny
2025-05-19 13:18:38,126 - DEBUG - Checking model: mistral-tiny
2025-05-19 13:18:38,128 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:38,448 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:38,450 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:38,450 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-tiny is not working. Status code: 429
Checking model: mistral-tiny-2312
2025-05-19 13:18:38,457 - DEBUG - Checking model: mistral-tiny-2312
2025-05-19 13:18:38,459 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:38,631 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:38,641 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:38,641 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-tiny-2312 is not working. Status code: 429
Checking model: open-mistral-nemo
2025-05-19 13:18:38,644 - DEBUG - Checking model: open-mistral-nemo
2025-05-19 13:18:38,647 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:38,977 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:38,987 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:38,988 - DEBUG - Response: 200 - {"id":"9c6825f3b083422c880a8166528525ac","object":"chat.completion","created":1747653520,"model":"open-mistral-nemo","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you! How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":13,"total_tokens":32,"completion_tokens":19}}
Model open-mistral-nemo is working
Checking model: open-mistral-nemo-2407
2025-05-19 13:18:38,993 - DEBUG - Checking model: open-mistral-nemo-2407
2025-05-19 13:18:38,997 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:39,356 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:39,359 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:39,360 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model open-mistral-nemo-2407 is not working. Status code: 429
Checking model: mistral-tiny-2407
2025-05-19 13:18:39,361 - DEBUG - Checking model: mistral-tiny-2407
2025-05-19 13:18:39,363 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:39,670 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:39,672 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:39,673 - DEBUG - Response: 200 - {"id":"e9e867ccedf14e49ac95b1d998c52c8e","object":"chat.completion","created":1747653520,"model":"mistral-tiny-2407","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you! How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":13,"total_tokens":32,"completion_tokens":19}}
Model mistral-tiny-2407 is working
Checking model: mistral-tiny-latest
2025-05-19 13:18:39,674 - DEBUG - Checking model: mistral-tiny-latest
2025-05-19 13:18:39,687 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:39,815 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:39,817 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:39,817 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-tiny-latest is not working. Status code: 429
Checking model: open-mixtral-8x7b
2025-05-19 13:18:39,817 - DEBUG - Checking model: open-mixtral-8x7b
2025-05-19 13:18:39,821 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:40,732 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:40,733 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:40,733 - DEBUG - Response: 200 - {"id":"d4eb228b95b2461c91d40a32202007b3","object":"chat.completion","created":1747653521,"model":"open-mixtral-8x7b","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to help! How can I assist you today?\n\nIf you have any questions or need help with something, feel free to ask. I'm designed to provide helpful, accurate, and reliable information, as well as support for a wide range of tasks. Let me know what you need, and I'll do my best to assist you."},"finish_reason":"stop"}],"usage":{"prompt_tokens":20,"total_tokens":100,"completion_tokens":80}}
Model open-mixtral-8x7b is working
Checking model: mistral-small
2025-05-19 13:18:40,734 - DEBUG - Checking model: mistral-small
2025-05-19 13:18:40,736 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:41,506 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:41,508 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:41,508 - DEBUG - Response: 200 - {"id":"d522f3a190384385b3174272190c89c3","object":"chat.completion","created":1747653522,"model":"mistral-small","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to help! How can I assist you today?\n\nIf you have any questions or need assistance with a task, feel free to ask. I'm here to help make your life easier. 😊"},"finish_reason":"stop"}],"usage":{"prompt_tokens":20,"total_tokens":71,"completion_tokens":51}}
Model mistral-small is working
Checking model: mistral-small-2312
2025-05-19 13:18:41,591 - DEBUG - Checking model: mistral-small-2312
2025-05-19 13:18:41,593 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:43,386 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:43,388 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:43,388 - DEBUG - Response: 200 - {"id":"bef3afbda8aa43a2b2f0c5630721db7f","object":"chat.completion","created":1747653523,"model":"mistral-small-2312","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to help! How can I assist you today?\n\nIf you have any questions or need help with something, feel free to ask. I'm designed to provide helpful, accurate, and reliable information across a wide range of topics. I can assist you with general knowledge questions, provide explanations on complex topics, help you manage your schedule, set reminders, and much more. Let me know what you need!"},"finish_reason":"stop"}],"usage":{"prompt_tokens":20,"total_tokens":114,"completion_tokens":94}}
Model mistral-small-2312 is working
Checking model: open-mixtral-8x22b
2025-05-19 13:18:43,389 - DEBUG - Checking model: open-mixtral-8x22b
2025-05-19 13:18:43,391 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:44,238 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:44,245 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:44,246 - DEBUG - Response: 200 - {"id":"c9ba17f22a4b457882116480e457d222","object":"chat.completion","created":1747653525,"model":"open-mixtral-8x22b","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you. While I don't have feelings, emotions, or the ability to \"work\" in the human sense, I'm designed to help answer your questions and provide information to the best of my abilities."},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":69,"completion_tokens":54}}
Model open-mixtral-8x22b is working
Checking model: open-mixtral-8x22b-2404
2025-05-19 13:18:44,247 - DEBUG - Checking model: open-mixtral-8x22b-2404
2025-05-19 13:18:44,277 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:45,232 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:45,233 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:45,233 - DEBUG - Response: 200 - {"id":"4eb65ef3629648a99afc9114315a25a6","object":"chat.completion","created":1747653525,"model":"open-mixtral-8x22b-2404","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here to assist you. While I don't have feelings or the ability to work in the human sense, I'm always operational and ready to help answer your questions to the best of my abilities."},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":62,"completion_tokens":47}}
Model open-mixtral-8x22b-2404 is working
Checking model: mistral-small-2402
2025-05-19 13:18:45,234 - DEBUG - Checking model: mistral-small-2402
2025-05-19 13:18:45,235 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:45,733 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:45,735 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:45,736 - DEBUG - Response: 200 - {"id":"aff43466c36d4f1f86fca4322898ac9f","object":"chat.completion","created":1747653526,"model":"mistral-small-2402","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I am designed to assist and answer your questions 24/7. How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":40,"completion_tokens":25}}
Model mistral-small-2402 is working
Checking model: mistral-small-2409
2025-05-19 13:18:45,737 - DEBUG - Checking model: mistral-small-2409
2025-05-19 13:18:45,742 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:47,090 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:47,092 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:47,092 - DEBUG - Response: 200 - {"id":"ebe55e44bb7f4ae5844ecdbee1c452f3","object":"chat.completion","created":1747653527,"model":"mistral-small-2409","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"I don't work in the same way that humans do. I don't have a job, earn a salary, or take breaks. I'm here to assist and provide information to the best of my ability whenever you need help. So, in a sense, I'm always ready to assist you! How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":87,"completion_tokens":72}}
Model mistral-small-2409 is not working. Response: I don't work in the same way that humans do. I don't have a job, earn a salary, or take breaks. I'm here to assist and provide information to the best of my ability whenever you need help. So, in a sense, I'm always ready to assist you! How can I help you today?
Checking model: mistral-medium-2312
2025-05-19 13:18:47,092 - DEBUG - Checking model: mistral-medium-2312
2025-05-19 13:18:47,094 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:48,617 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:48,625 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:48,625 - DEBUG - Response: 200 - {"id":"72e6c8d8af86481bbc3e7c95a8766ccb","object":"chat.completion","created":1747653528,"model":"mistral-medium-2312","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to help you! How can I assist you today? If you have any questions or need information on a particular topic, just let me know. I'm here to make your life easier and provide accurate answers promptly. Don't hesitate to ask me anything."},"finish_reason":"stop"}],"usage":{"prompt_tokens":20,"total_tokens":84,"completion_tokens":64}}
Model mistral-medium-2312 is working
Checking model: mistral-large-2402
2025-05-19 13:18:48,631 - DEBUG - Checking model: mistral-large-2402
2025-05-19 13:18:48,633 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:50,162 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:50,165 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:50,166 - DEBUG - Response: 200 - {"id":"300974abd84a41cc866b44a0964054dc","object":"chat.completion","created":1747653530,"model":"mistral-large-2402","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here to help answer your questions and assist you to the best of my ability. While I don't work in the traditional sense, I'm always ready to provide information or guidance whenever you need it."},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":63,"completion_tokens":48}}
Model mistral-large-2402 is working
Checking model: mistral-large-2407
2025-05-19 13:18:50,169 - DEBUG - Checking model: mistral-large-2407
2025-05-19 13:18:50,174 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:52,235 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:52,236 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:52,236 - DEBUG - Response: 200 - {"id":"79ae956166354463bcfd4dde022d4c17","object":"chat.completion","created":1747653532,"model":"mistral-large-2407","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you! How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":35,"completion_tokens":20}}
Model mistral-large-2407 is working
Checking model: mistral-large-2411
2025-05-19 13:18:52,237 - DEBUG - Checking model: mistral-large-2411
2025-05-19 13:18:52,240 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:53,576 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:53,578 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:53,579 - DEBUG - Response: 200 - {"id":"4a969d5b866a478c8a8ad3504f8b7fc7","object":"chat.completion","created":1747653533,"model":"mistral-large-2411","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm working! I'm here to help answer your questions, provide explanations, or just chat if you're up for it. Here are a few things I can do:\n\n1. **Answer questions** based on the data I've been trained on (up until 2021).\n2. **Explain concepts** in a simple and understandable way.\n3. **Provide suggestions** and advice within reasonable and ethical boundaries"},"finish_reason":"length"}],"usage":{"prompt_tokens":15,"total_tokens":115,"completion_tokens":100}}
Model mistral-large-2411 is working
Checking model: mistral-large-latest
2025-05-19 13:18:53,581 - DEBUG - Checking model: mistral-large-latest
2025-05-19 13:18:53,583 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:54,804 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:54,807 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:54,808 - DEBUG - Response: 200 - {"id":"38362e285b1c4c248fdc056b574e8f8b","object":"chat.completion","created":1747653535,"model":"mistral-large-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm working! I'm here to help answer your questions, provide explanations, or just chat if you're up for it. Here are a few things I can do:\n\n1. **Answer Questions**: I can provide information based on the data I've been trained on, up until 2021.\n2. **Explain Concepts**: I can help break down complex ideas into simpler parts.\n3. **Provide"},"finish_reason":"length"}],"usage":{"prompt_tokens":15,"total_tokens":115,"completion_tokens":100}}
Model mistral-large-latest is working
Checking model: pixtral-large-2411
2025-05-19 13:18:54,810 - DEBUG - Checking model: pixtral-large-2411
2025-05-19 13:18:54,814 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:54,993 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:54,996 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:54,997 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model pixtral-large-2411 is not working. Status code: 429
Checking model: pixtral-large-latest
2025-05-19 13:18:54,997 - DEBUG - Checking model: pixtral-large-latest
2025-05-19 13:18:54,999 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:55,100 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:55,102 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:55,103 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model pixtral-large-latest is not working. Status code: 429
Checking model: mistral-large-pixtral-2411
2025-05-19 13:18:55,103 - DEBUG - Checking model: mistral-large-pixtral-2411
2025-05-19 13:18:55,106 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:56,393 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:56,395 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:56,395 - DEBUG - Response: 200 - {"id":"ba19abef75bc47988ecfa6a94ca6b08c","object":"chat.completion","created":1747653536,"model":"mistral-large-pixtral-2411","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm working! I'm here to help answer your questions, provide information, or assist with a variety of tasks to the best of my ability. Here are a few things I can do:\n\n1. **Answer Questions**: I can provide information based on the data I've been trained on, up until 2023.\n2. **Explain Concepts**: I can help break down complex ideas into simpler parts.\n3. **"},"finish_reason":"length"}],"usage":{"prompt_tokens":15,"total_tokens":115,"completion_tokens":100}}
Model mistral-large-pixtral-2411 is working
Checking model: codestral-2405
2025-05-19 13:18:56,396 - DEBUG - Checking model: codestral-2405
2025-05-19 13:18:56,397 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:57,420 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:57,423 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:57,423 - DEBUG - Response: 200 - {"id":"99b9dc953b284401bcc503a90a27a1ba","object":"chat.completion","created":1747653538,"model":"codestral-2405","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"No, I'm not working in the traditional sense. I'm a language model, a software program designed to understand, generate, and respond to human language. I don't have a physical presence or a job like a human would. I'm here to assist you with information, answer questions, and help with a variety of tasks. I'm always active and ready to help, 24/7."},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":103,"completion_tokens":88}}
Model codestral-2405 is working
Checking model: codestral-2501
2025-05-19 13:18:57,424 - DEBUG - Checking model: codestral-2501
2025-05-19 13:18:57,427 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:57,536 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:57,539 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:57,539 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model codestral-2501 is not working. Status code: 429
Checking model: codestral-latest
2025-05-19 13:18:57,540 - DEBUG - Checking model: codestral-latest
2025-05-19 13:18:57,542 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:58,037 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:58,040 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:58,040 - DEBUG - Response: 200 - {"id":"4b4c68dcea5443dd9223d840090ea675","object":"chat.completion","created":1747653539,"model":"codestral-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you! What do you need help with?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":34,"completion_tokens":19}}
Model codestral-latest is working
Checking model: codestral-2412
2025-05-19 13:18:58,041 - DEBUG - Checking model: codestral-2412
2025-05-19 13:18:58,043 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:58,136 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:58,137 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:58,138 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model codestral-2412 is not working. Status code: 429
Checking model: codestral-2411-rc5
2025-05-19 13:18:58,138 - DEBUG - Checking model: codestral-2411-rc5
2025-05-19 13:18:58,140 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:58,262 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:58,264 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:58,264 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model codestral-2411-rc5 is not working. Status code: 429
Checking model: codestral-mamba-2407
2025-05-19 13:18:58,264 - DEBUG - Checking model: codestral-mamba-2407
2025-05-19 13:18:58,266 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:58,797 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:58,799 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:58,800 - DEBUG - Response: 200 - {"id":"4e57243517ee430489cf003e209e3232","object":"chat.completion","created":1747653540,"model":"codestral-mamba-2407","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you. How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":36,"completion_tokens":21}}
Model codestral-mamba-2407 is working
Checking model: open-codestral-mamba
2025-05-19 13:18:58,801 - DEBUG - Checking model: open-codestral-mamba
2025-05-19 13:18:58,803 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:58,920 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:58,934 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:58,938 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model open-codestral-mamba is not working. Status code: 429
Checking model: codestral-mamba-latest
2025-05-19 13:18:58,939 - DEBUG - Checking model: codestral-mamba-latest
2025-05-19 13:18:58,940 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:59,039 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:18:59,041 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:59,042 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model codestral-mamba-latest is not working. Status code: 429
Checking model: pixtral-12b-2409
2025-05-19 13:18:59,042 - DEBUG - Checking model: pixtral-12b-2409
2025-05-19 13:18:59,044 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:59,441 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:59,442 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:59,443 - DEBUG - Response: 200 - {"id":"7d9cb35b7d2c40d58a62035e71a6f922","object":"chat.completion","created":1747653540,"model":"pixtral-12b-2409","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here to help! What do you need assistance with?"},"finish_reason":"stop","logprobs":null}],"usage":{"prompt_tokens":13,"total_tokens":29,"completion_tokens":16}}
Model pixtral-12b-2409 is working
Checking model: pixtral-12b
2025-05-19 13:18:59,443 - DEBUG - Checking model: pixtral-12b
2025-05-19 13:18:59,445 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:18:59,891 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:18:59,894 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:18:59,895 - DEBUG - Response: 200 - {"id":"30a9f7abdb31469db140537fa3e6706d","object":"chat.completion","created":1747653541,"model":"pixtral-12b","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here to assist you. How can I help you today?"},"finish_reason":"stop","logprobs":null}],"usage":{"prompt_tokens":13,"total_tokens":30,"completion_tokens":17}}
Model pixtral-12b is working
Checking model: pixtral-12b-latest
2025-05-19 13:18:59,896 - DEBUG - Checking model: pixtral-12b-latest
2025-05-19 13:18:59,900 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:00,392 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:19:00,394 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:00,395 - DEBUG - Response: 200 - {"id":"7ede1c7e67934ec58c6a12dd23c306df","object":"chat.completion","created":1747653541,"model":"pixtral-12b-latest","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here to assist you. How can I help you today?"},"finish_reason":"stop","logprobs":null}],"usage":{"prompt_tokens":13,"total_tokens":30,"completion_tokens":17}}
Model pixtral-12b-latest is working
Checking model: mistral-small-2501
2025-05-19 13:19:00,398 - DEBUG - Checking model: mistral-small-2501
2025-05-19 13:19:00,400 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:01,260 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:19:01,261 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:01,261 - DEBUG - Response: 200 - {"id":"5b1bd292b4d246ea8bec7c5548dab06e","object":"chat.completion","created":1747653542,"model":"mistral-small-2501","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"I'm here and ready to assist you! While I don't have personal experiences or emotions, I can certainly help answer your questions, provide information, or even engage in a friendly conversation. Here are a few things I can do:\n\n1. **Answer Questions**: I can provide information based on the data I've been trained on (up until 2021).\n\n2. **Explain Concepts**: I can help break down complex ideas into simpler parts.\n\n3. **Provide S"},"finish_reason":"length"}],"usage":{"prompt_tokens":15,"total_tokens":115,"completion_tokens":100}}
Model mistral-small-2501 is not working. Response: I'm here and ready to assist you! While I don't have personal experiences or emotions, I can certainly help answer your questions, provide information, or even engage in a friendly conversation. Here are a few things I can do:

1. **Answer Questions**: I can provide information based on the data I've been trained on (up until 2021).

2. **Explain Concepts**: I can help break down complex ideas into simpler parts.

3. **Provide S
Checking model: mistral-small-2503
2025-05-19 13:19:01,262 - DEBUG - Checking model: mistral-small-2503
2025-05-19 13:19:01,263 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:01,356 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:01,357 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:01,357 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-small-2503 is not working. Status code: 429
Checking model: mistral-small-latest
2025-05-19 13:19:01,358 - DEBUG - Checking model: mistral-small-latest
2025-05-19 13:19:01,359 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:01,494 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:01,496 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:01,496 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-small-latest is not working. Status code: 429
Checking model: mistral-saba-2502
2025-05-19 13:19:01,496 - DEBUG - Checking model: mistral-saba-2502
2025-05-19 13:19:01,498 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:01,990 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:19:01,992 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:01,992 - DEBUG - Response: 200 - {"id":"4346fc99f5d9450f8246ced38232364a","object":"chat.completion","created":1747653543,"model":"mistral-saba-2502","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm here and ready to assist you. How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":34,"completion_tokens":19}}
Model mistral-saba-2502 is working
Checking model: mistral-saba-latest
2025-05-19 13:19:01,993 - DEBUG - Checking model: mistral-saba-latest
2025-05-19 13:19:01,995 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:02,142 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:02,144 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:02,144 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-saba-latest is not working. Status code: 429
Checking model: mistral-medium-2505
2025-05-19 13:19:02,144 - DEBUG - Checking model: mistral-medium-2505
2025-05-19 13:19:02,146 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:02,539 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:19:02,541 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:02,541 - DEBUG - Response: 200 - {"id":"869e79d8ec73441397392d3a2fa30bde","object":"chat.completion","created":1747653543,"model":"mistral-medium-2505","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm working and ready to assist you! How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":34,"completion_tokens":19}}
Model mistral-medium-2505 is working
Checking model: mistral-medium-latest
2025-05-19 13:19:02,542 - DEBUG - Checking model: mistral-medium-latest
2025-05-19 13:19:02,544 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:02,690 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:02,692 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:02,692 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-medium-latest is not working. Status code: 429
Checking model: mistral-medium
2025-05-19 13:19:02,692 - DEBUG - Checking model: mistral-medium
2025-05-19 13:19:02,693 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:03,713 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 200 None
2025-05-19 13:19:03,715 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:03,715 - DEBUG - Response: 200 - {"id":"eca12b71a2aa431f91ab349eeda4b12e","object":"chat.completion","created":1747653544,"model":"mistral-medium","choices":[{"index":0,"message":{"role":"assistant","tool_calls":null,"content":"Yes, I'm working and ready to assist you with any questions or tasks you have. How can I help you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":15,"total_tokens":41,"completion_tokens":26}}
Model mistral-medium is working
Checking model: mistral-embed
2025-05-19 13:19:03,716 - DEBUG - Checking model: mistral-embed
2025-05-19 13:19:03,718 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:03,872 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 400 109
2025-05-19 13:19:03,874 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:03,874 - DEBUG - Response: 400 - {"object":"error","message":"Invalid model: mistral-embed","type":"invalid_model","param":null,"code":"1500"}
Model mistral-embed is not working. Status code: 400
Checking model: mistral-moderation-2411
2025-05-19 13:19:03,874 - DEBUG - Checking model: mistral-moderation-2411
2025-05-19 13:19:03,876 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:03,987 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:03,990 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:03,990 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-moderation-2411 is not working. Status code: 429
Checking model: mistral-moderation-latest
2025-05-19 13:19:03,990 - DEBUG - Checking model: mistral-moderation-latest
2025-05-19 13:19:03,992 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:04,132 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:04,134 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:04,134 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-moderation-latest is not working. Status code: 429
Checking model: mistral-ocr-2503
2025-05-19 13:19:04,155 - DEBUG - Checking model: mistral-ocr-2503
2025-05-19 13:19:04,159 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:04,303 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:04,305 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:04,306 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-ocr-2503 is not working. Status code: 429
Checking model: mistral-ocr-latest
2025-05-19 13:19:04,306 - DEBUG - Checking model: mistral-ocr-latest
2025-05-19 13:19:04,310 - DEBUG - Starting new HTTPS connection (1): api.mistral.ai:443
2025-05-19 13:19:04,467 - DEBUG - https://api.mistral.ai:443 "POST /v1/chat/completions HTTP/1.1" 429 42
2025-05-19 13:19:04,470 - DEBUG - Request: POST https://api.mistral.ai/v1/chat/completions
2025-05-19 13:19:04,470 - DEBUG - Response: 429 - {"message":"Requests rate limit exceeded"}
Model mistral-ocr-latest is not working. Status code: 429
