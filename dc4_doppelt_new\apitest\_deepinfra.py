import os
import traceback
import time
import openai

# Configuration
BASE_URL = "https://api.deepinfra.com/v1/openai"
API_KEY = "qiDyiu7g1Jvzd7YTbly7lMnF4X6sSzWx"  # Provided DeepInfra API key
DELAY_SECONDS = 1  # Adjust as needed
TEST_MESSAGE = "only say a number between 1 and 10"
MAX_TOKENS = 100

# DeepInfra models to test
DEEPINFRA_MODELS = [
    "anthropic/claude-3-7-sonnet-latest",
    "Austism/chronos-hermes-13b-v2",
    "bigcode/starcoder2-15b-instruct-v0.1",
    "cognitivecomputations/dolphin-2.6-mixtral-8x7b",
    "cognitivecomputations/dolphin-2.9.1-llama-3-70b",
    "deepinfra/airoboros-70b",
    "deepseek-ai/DeepSeek-R1",
    "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
    "deepseek-ai/DeepSeek-R1-<PERSON><PERSON><PERSON>-Qwen-32B",
    "deepseek-ai/DeepSeek-R1-Turbo",
    "deepseek-ai/DeepSeek-V3",
    "deepseek-ai/DeepSeek-V3-0324",
    "google/codegemma-7b-it",
    "google/gemini-1.5-flash",
    "google/gemini-1.5-flash-8b",
    "google/gemini-2.0-flash-001",
    "google/gemini-2.5-flash",
    "google/gemini-2.5-pro",
    "google/gemma-1.1-7b-it",
    "google/gemma-2-27b-it",
    "google/gemma-2-9b-it",
    "google/gemma-3-12b-it",
    "google/gemma-3-27b-it",
    "google/gemma-3-4b-it",
    "Gryphe/MythoMax-L2-13b",
    "Gryphe/MythoMax-L2-13b-turbo",
    "KoboldAI/LLaMA2-13B-Tiefighter",
    "lizpreciatior/lzlv_70b_fp16_hf",
    "mattshumer/Reflection-Llama-3.1-70B",
    "meta-llama/Llama-2-13b-chat-hf",
    "meta-llama/Llama-2-70b-chat-hf",
    "meta-llama/Llama-3.2-11B-Vision-Instruct",
    "meta-llama/Llama-3.2-1B-Instruct",
    "meta-llama/Llama-3.2-3B-Instruct",
    "meta-llama/Llama-3.2-90B-Vision-Instruct",
    "meta-llama/Llama-3.3-70B-Instruct",
    "meta-llama/Llama-3.3-70B-Instruct-Turbo",
    "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
    "meta-llama/Llama-4-Scout-17B-16E-Instruct",
    "meta-llama/Llama-Guard-3-8B",
    "meta-llama/Meta-Llama-3-70B-Instruct",
    "meta-llama/Meta-Llama-3-8B-Instruct",
    "meta-llama/Meta-Llama-3.1-405B-Instruct",
    "meta-llama/Meta-Llama-3.1-70B-Instruct",
    "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
    "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
    "microsoft/Phi-3-medium-4k-instruct",
    "microsoft/phi-4",
    "microsoft/Phi-4-multimodal-instruct",
    "microsoft/WizardLM-2-7B",
    "microsoft/WizardLM-2-8x22B",
    "mistralai/Mistral-7B-Instruct-v0.1",
    "mistralai/Mistral-7B-Instruct-v0.2",
    "mistralai/Mistral-7B-Instruct-v0.3",
    "mistralai/Mistral-Nemo-Instruct-2407",
    "mistralai/Mistral-Small-24B-Instruct-2501",
    "mistralai/Mixtral-8x22B-Instruct-v0.1",
    "mistralai/Mixtral-8x7B-Instruct-v0.1",
    "NousResearch/Hermes-3-Llama-3.1-405B",
    "NovaSky-AI/Sky-T1-32B-Preview",
    "nvidia/Llama-3.1-Nemotron-70B-Instruct",
    "nvidia/Nemotron-4-340B-Instruct",
    "openbmb/MiniCPM-Llama3-V-2_5",
    "openchat/openchat-3.6-8b",
    "openchat/openchat_3.5",
    "Phind/Phind-CodeLlama-34B-v2",
    "Qwen/QVQ-72B-Preview",
    "Qwen/Qwen2-72B-Instruct",
    "Qwen/Qwen2-7B-Instruct",
    "Qwen/Qwen2.5-72B-Instruct",
    "Qwen/Qwen2.5-7B-Instruct",
    "Qwen/Qwen2.5-Coder-32B-Instruct",
    "Qwen/Qwen2.5-Coder-7B",
    "Qwen/QwQ-32B",
    "Qwen/QwQ-32B-Preview",
    "Sao10K/L3-70B-Euryale-v2.1",
    "Sao10K/L3-8B-Lunaris-v1",
    "Sao10K/L3-8B-Lunaris-v1-Turbo",
    "Sao10K/L3.1-70B-Euryale-v2.2",
    "Sao10K/L3.3-70B-Euryale-v2.3"
]

def test_model(model_name):
    try:
        client = openai.OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL,
        )
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": TEST_MESSAGE}],
            max_tokens=MAX_TOKENS,
        )
        output = response.choices[0].message.content
        print(f"  Response from {model_name}: {output}")
        return True
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

def main():
    print("Testing DeepInfra models...")
    for model_name in DEEPINFRA_MODELS:
        print(f"\nTesting model: {model_name}")
        success = test_model(model_name)
        if success:
            print(f"{model_name} is accessible")
        else:
            print(f"{model_name} is not accessible")
        time.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    main()
