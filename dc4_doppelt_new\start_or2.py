"""
Bot Launcher for or2.py with Provider Selection

This script allows the user to select which API provider to use
without modifying the config.txt file. It then launches or2.py.

Usage:
    python start_or2.py
"""

import os
import sys
import subprocess

# Provider names for display
PROVIDER_NAMES = {
    "1": "OpenRouter",
    "2": "DeepSeek",
    "3": "Google"
}

# API key mapping
API_KEY_MAPPING = {
    "1": "API_KEY1",  # OpenRouter
    "2": "API_KEY2",  # DeepSeek
    "3": "API_KEY3"   # Google
}

# Available models for each provider
PROVIDER_MODELS = {
    "1": [  # OpenRouter
        "mistralai/mistral-7b-instruct",
        "mistralai/mistral-small-2405",
        "google/gemini-2.5-pro-exp-03-25",
        "google/gemini-2.0-flash-exp",
        "deepseek/deepseek-chat-v3-0324",
        "deepseek/deepseek-r1",
        "qwen/qwen2.5-vl-32b-instruct"
    ],
    "2": [  # DeepSeek
        "deepseek-chat",
        "deepseek-coder",
        "deepseek-llm"
    ],
    "3": [  # Google
        "gemini-1.5-flash",
        "gemini-1.5-pro",
        "gemini-2.5-flash",
        "gemini-2.5-pro"
    ]
}

def load_config(path="config.txt"):
    """Load configuration from a text file with support for multi-line values."""
    config = {}
    current_key = None
    multi_line_value = ""

    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                # New key-value pair
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""

                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():  # Continuation of previous value
                multi_line_value += " " + line.strip()

        # Add the last key-value pair
        if current_key and multi_line_value:
            config[current_key] = multi_line_value.strip()

    return config

def create_temp_config(original_config, provider_choice, model):
    """Create a temporary config file with the selected provider's API key and model."""
    # Create a new config dictionary
    new_config = original_config.copy()

    # Use the existing API_KEY from config.txt
    if "API_KEY" not in original_config:
        print(f"Error: API_KEY not found in config.txt")
        return None

    # Add the selected model to the config
    new_config["MODEL"] = model

    # Add the provider type
    if provider_choice == "1":
        new_config["API_PROVIDER"] = "openrouter"
    elif provider_choice == "2":
        new_config["API_PROVIDER"] = "deepseek"
    elif provider_choice == "3":
        new_config["API_PROVIDER"] = "google"

    # Write the temporary config file
    temp_config_path = "config.txt.bot"
    with open(temp_config_path, "w") as f:
        for key, value in new_config.items():
            # Skip the original API keys to keep the file clean
            if key not in ["API_KEY1", "API_KEY2", "API_KEY3"]:
                f.write(f"{key}={value}\n")

    return temp_config_path

def select_provider():
    """Ask the user to select an API provider."""
    print("\n=== Select API Provider ===")
    print("1. OpenRouter (Free models from various providers)")
    print("2. DeepSeek (DeepSeek models)")
    print("3. Google (Gemini models)")

    while True:
        choice = input("\nEnter your choice (1-3): ").strip()
        if choice in ["1", "2", "3"]:
            return choice
        print("Invalid choice. Please enter 1, 2, or 3.")

def select_model(provider_choice):
    """Ask the user to select a model for the chosen provider."""
    models = PROVIDER_MODELS[provider_choice]
    provider_name = PROVIDER_NAMES[provider_choice]

    print(f"\n=== Select {provider_name} Model ===")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")

    while True:
        try:
            choice = int(input(f"\nEnter your choice (1-{len(models)}): ").strip())
            if 1 <= choice <= len(models):
                return models[choice-1]
            print(f"Invalid choice. Please enter a number between 1 and {len(models)}.")
        except ValueError:
            print("Please enter a valid number.")

def main():
    # Check if config.txt exists
    if not os.path.exists("config.txt"):
        print("Error: config.txt not found. Please create a config.txt file with your API keys.")
        return

    # Load the original config
    config = load_config("config.txt")

    # Check if all required API keys are present
    required_keys = ["API_ID", "API_HASH", "GROUP_IDS"]
    missing_keys = [key for key in required_keys if key not in config]
    if missing_keys:
        print(f"Error: Missing required keys in config.txt: {', '.join(missing_keys)}")
        return

    # Select provider
    provider_choice = select_provider()
    provider_name = PROVIDER_NAMES[provider_choice]

    # Select model
    model = select_model(provider_choice)

    # Create temporary config file
    temp_config_path = create_temp_config(config, provider_choice, model)
    if not temp_config_path:
        return

    # This launcher is for or2.py only
    script_path = "or2.py"

    # Print summary
    print("\n=== Bot Configuration ===")
    print(f"Provider: {provider_name}")
    print(f"Model: {model}")
    print(f"Script: {script_path}")
    print("\nStarting bot...")

    # Run the bot with the temporary config
    try:
        # Rename the temp config to config.txt temporarily
        if os.path.exists("config.txt.original"):
            os.remove("config.txt.original")
        os.rename("config.txt", "config.txt.original")
        os.rename(temp_config_path, "config.txt")

        # Run the bot script
        subprocess.run([sys.executable, script_path])
    except Exception as e:
        print(f"Error running bot: {e}")
    finally:
        # Restore the original config
        if os.path.exists("config.txt"):
            os.remove("config.txt")
        if os.path.exists("config.txt.original"):
            os.rename("config.txt.original", "config.txt")

if __name__ == "__main__":
    main()
