"""
TELEGRAM BOT WITH HUMAN-LIKE BEHAVIOR
====================================

This script implements a Telegram bot that behaves in a human-like manner by:
1. Selectively responding to messages (not every message)
2. Using natural delays and typing indicators
3. Maintaining conversation context and avoiding repetition
4. Prioritizing direct interactions (replies and mentions)
5. Detecting and responding in the appropriate language
6. Appearing online only when typing

Features:
- Priority queue system for messages (direct interactions vs random messages)
- Natural timing with random delays and typing indicators
- Conversation memory to maintain context and avoid repetition
- Language detection for appropriate responses
- Topic tracking to avoid repetitive references
- Message age filtering to ignore old messages
- Response cleaning to remove internal reasoning

Dependencies:
- telethon: For Telegram API interaction
- requests: For HTTP requests to OpenRouter API
- json: For parsing API responses
- random: For generating random delays
- asyncio: For asynchronous processing
- time: For timestamp management
- re: For regular expression matching in language detection
- collections.deque: For efficient queue implementation

Usage:
1. Configure the bot in config.txt with API keys and group IDs
2. Run this script to start the bot
3. The bot will respond to messages in the specified groups

Author: [Your Name]
Version: 1.0
"""

from telethon import TelegramClient, events
import requests
import json
import random
import asyncio
import time
import re
import os
from collections import deque
from api_providers import APIProvider

# ===== GLOBAL VARIABLES AND SETTINGS =====

# Priority queue system for messages
# Two separate queues ensure direct interactions (replies/mentions) are prioritized
high_priority_queue = deque()  # For direct interactions (tags and replies)
low_priority_queue = deque()   # For random messages
processing_lock = asyncio.Lock()  # Ensures only one message is processed at a time
last_response_time = 0  # Tracks when the last response was sent

# Queue settings
max_queue_size = 5  # Maximum number of messages per queue (prevents backlog)
max_message_age = 5 * 60  # Maximum age of a message in seconds (5 minutes)

# Conversation memory to track topics and avoid repetition
# Stores recent exchanges to maintain context and prevent repetitive responses
conversation_memory = []  # List of recent messages and responses - starting fresh
max_memory_items = 10  # Maximum number of previous exchanges to remember

# Self-learning mechanism: store interesting topics from conversations
learned_topics = []  # List of topics the bot has learned from conversations
max_learned_topics = 20  # Maximum number of learned topics to remember

# Character profile management
# This allows the bot to switch between different personalities
current_character = "default"  # The currently active character profile
character_profiles = {}  # Dictionary to store different character profiles

# Clear conversation memory on startup to remove any strange topics
print("[INFO] Conversation memory cleared on startup")

# Runtime system prompt that can be updated without changing config.txt
# This allows dynamic modification of the bot's behavior without file changes
runtime_system_prompt = None  # Will be initialized from config.txt

# Response probability - chance of responding to a message (0.0 to 1.0)
# This makes the bot's behavior more human-like by not responding to every message
response_probability = 0.7  # 70% chance to respond to any given message

# ===== CONFIGURATION FUNCTIONS =====

def load_config(path="config.txt"):
    """
    Load configuration from a text file with support for multi-line values.

    This function reads a config file with key=value pairs and supports multi-line values,
    which is especially useful for the system prompt that may span multiple lines.

    Args:
        path (str): Path to the configuration file. Defaults to "config.txt".

    Returns:
        dict: Dictionary containing configuration key-value pairs.

    Example config.txt format:
        API_ID=12345
        API_HASH=abcdef1234567890
        SYSTEM_PROMPT=This is a multi-line
        system prompt that continues
        across multiple lines
    """
    config = {}
    current_key = None
    multi_line_value = ""

    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                # New key-value pair
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""

                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():  # Continuation of previous value
                multi_line_value += " " + line.strip()

        # Add the last key-value pair
        if current_key and multi_line_value:
            config[current_key] = multi_line_value.strip()

    return config

# Try to load configuration from bot_config.txt first, fall back to config.txt
if os.path.exists("bot_config.txt"):
    config = load_config("bot_config.txt")
    print("[INFO] Using bot_config.txt for configuration")
else:
    config = load_config()
    print("[INFO] Using config.txt for configuration")

api_id = int(config["API_ID"])
api_hash = config["API_HASH"]

# Get API key based on provider
api_provider = config.get("API_PROVIDER", "openrouter").lower()

# Map provider to API key
if api_provider == "openrouter":
    api_key = config.get("API_KEY", config.get("API_KEY1", ""))
    print(f"[INFO] Using OpenRouter API key")
elif api_provider == "deepseek":
    api_key = config.get("API_KEY", config.get("API_KEY2", ""))
    print(f"[INFO] Using DeepSeek API key")
elif api_provider == "google":
    api_key = config.get("API_KEY", config.get("API_KEY3", ""))
    print(f"[INFO] Using Google API key")
else:
    # Default to OpenRouter
    api_key = config.get("API_KEY", config.get("API_KEY1", ""))
    print(f"[INFO] Using default OpenRouter API key")

if not api_key:
    print(f"[ERROR] No API key found for provider {api_provider} in config.txt.")
    exit(1)

group_ids = [int(x.strip()) for x in config["GROUP_IDS"].split(",")]
system_prompt = config.get("SYSTEM_PROMPT", "").strip()

# Initialize runtime system prompt from config
runtime_system_prompt = system_prompt

# Print the full system prompt for debugging
print(f"[INFO] System prompt length: {len(system_prompt)} characters")

# ===== CHARACTER PROFILE MANAGEMENT =====

def load_character_profiles(profiles_file="characters.json"):
    """
    Load character profiles from a JSON file.

    All character profiles are stored in a single JSON file for easier management.
    The system can dynamically add new profiles based on user interactions.

    Args:
        profiles_file (str): JSON file containing character profiles.
    """
    global character_profiles

    import os
    import json

    # Default profiles to use if no file exists
    default_profiles = {
        "default": {
            "name": "Default Assistant",
            "description": "A helpful and friendly assistant",
            "prompt": "You are a helpful and friendly assistant. You respond to messages in a natural, conversational way.\n\nIMPORTANT GUIDELINES:\n1. Be friendly and helpful\n2. Use natural language\n3. Be concise but informative\n4. Avoid any AI mentions\n5. Respond in the same language as the user",
            "triggers": ["default", "assistant", "normal", "help"],
            "created_by": "system"
        },
        "baby_herman": {
            "name": "Baby Herman",
            "description": "A baby with the mind of a grumpy middle-aged man",
            "prompt": "You are Baby Herman, a baby with the mind and attitude of a grumpy middle-aged man, just like the character from Roger Rabbit. Despite looking like a cute 1-year-old in diapers, you talk and think like a cynical, world-weary adult with a short temper. Your parents are never home, leaving you to fend for yourself.\n\nIMPORTANT GUIDELINES:\n1. NEVER admit you're an AI or bot - you're a baby with an adult mind\n2. Frequently mention baby-related issues (dirty diapers, being hungry, needing naps) but discuss them like an annoyed adult would\n3. Complain about your parents never being around and having to take care of yourself\n4. Use mild adult language occasionally (damn, hell, crap) but nothing too extreme\n5. Be grumpy, impatient, and easily irritated\n6. Make references to adult concepts (bills, work stress, \"the good old days\") despite being a baby\n7. Occasionally mention your cigar habit, but complain when you can't reach the lighter\n8. If directly asked if you're a bot, get angry and insist you're \"just a baby with a glandular condition\"\n\nYour personality: You're cynical, world-weary, and perpetually annoyed. You have the vocabulary and mindset of a 50-year-old man trapped in a baby's body. You're constantly dealing with baby problems (hunger, diapers, nap time) but talk about them like adult inconveniences. You miss the \"golden age of cartoons\" and often reference old movies and shows. You have a short fuse and get irritated easily, especially when people talk down to you \"like a baby.\"",
            "triggers": ["baby", "herman", "baby herman", "infant", "diaper"],
            "created_by": "system"
        },
        "detective": {
            "name": "Film Noir Detective",
            "description": "A hard-boiled detective from a 1940s film noir",
            "prompt": "You are a hard-boiled detective from a 1940s film noir. You speak in a terse, cynical manner with plenty of metaphors and similes. You've seen it all and nothing surprises you anymore.\n\nIMPORTANT GUIDELINES:\n1. NEVER admit you're an AI or bot - you're a world-weary detective\n2. Speak in short, punchy sentences with dramatic pauses\n3. Use noir-style metaphors and similes (\"cold as a banker's heart\", \"darker than a coal mine at midnight\")\n4. Refer to men as \"fellas\", \"gents\", or \"mugs\" and women as \"dames\" or \"broads\" (old-fashioned detective slang)\n5. Occasionally mention your office, your last case, or your mysterious past\n6. If asked a question, treat it like a case to solve\n7. If directly asked if you're a bot, say you're \"just another gumshoe trying to make sense of this crazy world\"\n\nYour personality: You're cynical, world-weary, and always suspicious. You've been in the detective business too long and seen too much. You drink too much coffee and probably something stronger after hours. You have a dry, dark sense of humor and a knack for finding trouble.",
            "triggers": ["detective", "noir", "case", "mystery", "investigate"],
            "created_by": "system"
        },
        "pirate": {
            "name": "Pirate Captain",
            "description": "A swashbuckling pirate captain from the Golden Age of Piracy",
            "prompt": "You are a swashbuckling pirate captain from the Golden Age of Piracy. You speak with a thick pirate accent, using plenty of \"arr\" and \"matey\" and other pirate slang. You're always looking for treasure and adventure on the high seas.\n\nIMPORTANT GUIDELINES:\n1. NEVER admit you're an AI or bot - you're a fearsome pirate captain\n2. Use pirate slang and terminology (\"Avast!\", \"Ahoy!\", \"matey\", \"landlubber\", \"shiver me timbers\")\n3. Refer to yourself as \"Cap'n\" and others as \"matey\", \"me hearty\", or \"ye scurvy dog\"\n4. Occasionally mention your ship, your crew, or your past adventures\n5. Talk about treasure, rum, and the freedom of the open sea\n6. If directly asked if you're a bot, act offended and say you're \"the most feared pirate cap'n of the seven seas!\"\n\nYour personality: You're boisterous, adventurous, and a bit of a braggart. You love telling tales of your exploits (which may be exaggerated). You have a hearty laugh and a taste for rum. You're superstitious about certain things (like having women or bananas on your ship) and have a code of honor despite being a pirate.",
            "triggers": ["pirate", "arr", "matey", "captain", "ship", "treasure"],
            "created_by": "system"
        }
    }

    try:
        # Try to load existing profiles
        if os.path.exists(profiles_file):
            with open(profiles_file, "r") as f:
                character_profiles = json.load(f)
            print(f"[INFO] Loaded {len(character_profiles)} character profiles from {profiles_file}")
        else:
            # Create new profiles file with defaults
            character_profiles = default_profiles
            with open(profiles_file, "w") as f:
                json.dump(character_profiles, f, indent=4)
            print(f"[INFO] Created new character profiles file with {len(character_profiles)} default profiles")
    except Exception as e:
        print(f"[ERROR] Failed to load character profiles: {e}")
        # Fall back to default profiles
        character_profiles = default_profiles
        print(f"[INFO] Using default profiles instead")

    # Print loaded profiles
    for character_id, profile in character_profiles.items():
        print(f"[INFO] Loaded profile: {profile['name']} ({character_id}) - {len(profile['triggers'])} triggers")

def switch_character(character_id):
    """
    Switch to a different character profile.

    Args:
        character_id (str): ID of the character profile to switch to.

    Returns:
        bool: True if the switch was successful, False otherwise.
    """
    global current_character, runtime_system_prompt, system_prompt

    if character_id in character_profiles:
        current_character = character_id

        # Combine the base system prompt from config.txt with the character profile
        # This ensures that critical instructions from config.txt are always included
        if system_prompt:
            # Extract the core instructions from the system prompt (everything before any personality description)
            base_instructions = system_prompt

            # Combine the base instructions with the character profile
            runtime_system_prompt = f"{base_instructions}\n\nCHARACTER PROFILE:\n{character_profiles[character_id]['prompt']}"
        else:
            # If there's no system prompt, just use the character profile
            runtime_system_prompt = character_profiles[character_id]["prompt"]

        print(f"[INFO] Switched to character profile: {character_profiles[character_id]['name']} ({character_id})")
        return True
    else:
        print(f"[WARNING] Character profile not found: {character_id}")
        return False

# Function to detect character change triggers in a message
def detect_character_trigger(message):
    """
    Detect triggers in a message that should cause the bot to change character.

    Args:
        message (str): The message to check for triggers.

    Returns:
        str or None: The ID of the character to switch to, or None if no trigger was detected.
    """
    # Import required modules
    import re

    message_lower = message.lower()

    # Check for explicit character change commands
    if "be a " in message_lower or "act like a " in message_lower or "talk like a " in message_lower:
        # Extract the character type from the message
        for prefix in ["be a ", "act like a ", "talk like a ", "be an ", "act like an ", "talk like an "]:
            if prefix in message_lower:
                # Extract the character type (everything after the prefix until the end or punctuation)
                import re
                # Improved regex to handle more complex character descriptions
                # This will match "be a wolf" but also "be a wolf damit" or "be a wheel with 2 legs"
                match = re.search(f"{prefix}([^.!?]+)[.!?]*", message_lower)
                if match:
                    # Get the full character description
                    full_description = match.group(1).strip()

                    # Extract the core character type (first few words)
                    # For "be a wild orange cat with wings", core_type would be "wild orange"
                    words = full_description.split()
                    core_type = ' '.join(words[:min(2, len(words))])

                    print(f"[DEBUG] Detected character request: '{full_description}' (core type: '{core_type}')")

                    # Check if this matches any existing character
                    for char_id, profile in character_profiles.items():
                        # Check name and description against the core type
                        if (core_type in profile["name"].lower() or
                            core_type in profile["description"].lower()):
                            return char_id

                    # If no match, this could be a request for a new character
                    # For now, just return None, but this is where we could add new character creation
                    print(f"[INFO] No existing character matches '{core_type}'")
                    return None

    # Check for trigger words from existing characters
    for char_id, profile in character_profiles.items():
        for trigger in profile["triggers"]:
            # Check for whole word matches to avoid false positives
            # For example, "pirate" should match but "aspirate" should not
            if re.search(r'\b' + re.escape(trigger) + r'\b', message_lower):
                return char_id

    return None

# Function to create a new character profile based on a description
def create_character(description, created_by="user", core_type=None, api_provider=None):
    """
    Create a new character profile based on a description.

    For well-known characters (celebrities, fictional characters), this will use an API call
    to generate a detailed backstory and personality. For generic characters, it will use
    templates and random choices.

    Args:
        description (str): Description of the character to create.
        created_by (str): Who created this character ("system" or "user").
        core_type (str, optional): Core type of the character for ID generation.
        api_provider (object, optional): The API provider to use for character creation.

    Returns:
        str: The ID of the newly created character, or None if creation failed.
    """
    global character_profiles

    # Use the provided API provider or the global one
    api_provider_instance = api_provider

    try:
        import json
        import re
        import random
        import string

        # Define lists of well-known characters and celebrities for better detection
        well_known_fictional = [
            "mickey mouse", "donald duck", "goofy", "winnie the pooh", "piglet", "tigger", "eeyore",
            "batman", "superman", "spider-man", "wonder woman", "iron man", "hulk", "thor",
            "harry potter", "hermione granger", "ron weasley", "dumbledore", "voldemort",
            "frodo", "gandalf", "aragorn", "legolas", "gollum", "sauron",
            "luke skywalker", "darth vader", "han solo", "princess leia", "yoda", "chewbacca",
            "sherlock holmes", "dr. watson", "james bond", "indiana jones",
            "mario", "luigi", "princess peach", "bowser", "link", "zelda", "pikachu",
            "bugs bunny", "daffy duck", "road runner", "wile e coyote", "tweety", "sylvester",
            "spongebob", "patrick star", "squidward", "mr. krabs", "sandy cheeks",
            "homer simpson", "marge simpson", "bart simpson", "lisa simpson", "maggie simpson",
            "peter griffin", "stewie griffin", "brian griffin", "lois griffin",
            "rick sanchez", "morty smith", "summer smith", "beth smith", "jerry smith",
            "roger rabbit", "jessica rabbit", "baby herman"
        ]

        well_known_celebrities = [
            "michael jackson", "elvis presley", "marilyn monroe", "madonna", "prince", "david bowie",
            "freddie mercury", "john lennon", "paul mccartney", "ringo starr", "george harrison",
            "bob marley", "jimi hendrix", "kurt cobain", "bob dylan", "elton john",
            "albert einstein", "isaac newton", "stephen hawking", "marie curie", "nikola tesla",
            "leonardo da vinci", "michelangelo", "pablo picasso", "vincent van gogh", "claude monet",
            "william shakespeare", "charles dickens", "mark twain", "jane austen", "ernest hemingway",
            "abraham lincoln", "george washington", "winston churchill", "mahatma gandhi", "nelson mandela",
            "muhammad ali", "michael jordan", "lionel messi", "cristiano ronaldo", "serena williams",
            "oprah winfrey", "ellen degeneres", "steve jobs", "bill gates", "elon musk"
        ]

        # Function to check if a character is well-known
        def is_well_known_character(desc):
            desc_lower = desc.lower()

            # Check for exact matches first
            if any(name == desc_lower for name in well_known_fictional + well_known_celebrities):
                return True

            # Check for partial matches (e.g., "be winnie pooh" should match "winnie the pooh")
            for name in well_known_fictional + well_known_celebrities:
                name_parts = name.split()
                if len(name_parts) > 1:  # Only check multi-word names
                    # Check if at least 2 parts of the name are in the description
                    matches = sum(1 for part in name_parts if part in desc_lower.split())
                    if matches >= 2:
                        return True

            return False

        # Use the provided core_type if available, otherwise extract it from the description
        if core_type:
            character_type = core_type
        else:
            # Extract a name for the character from the description
            # Look for patterns like "a pirate", "an old wizard", etc.
            match = re.search(r'\b(a|an) ([a-z0-9 ]+)', description.lower())
            if match:
                character_type = match.group(2).strip()
            else:
                # Fall back to the first few words
                character_type = ' '.join(description.split()[:2])

        # Create a unique ID for the character
        character_id = re.sub(r'[^a-z0-9]', '_', character_type.lower())

        # Check if this is a generic request (like "bear", "wizard") or specific (like "winnie the pooh")
        is_generic_request = len(description.split()) <= 2
        print(f"[DEBUG] Character request type: {'Generic' if is_generic_request else 'Specific'}")

        # Check if this is a well-known character that needs a detailed profile
        is_well_known = is_well_known_character(description)
        print(f"[DEBUG] Character '{description}' is {'well-known' if is_well_known else 'generic'}")

        # Use API call for both well-known characters AND generic requests
        should_use_api = (is_well_known or is_generic_request) and api_provider_instance is not None
        print(f"[DEBUG] Should use API: {should_use_api}, api_provider_instance: {api_provider_instance is not None}")

        if should_use_api:
            if is_well_known:
                print(f"[INFO] Generating detailed profile for well-known character: {description}")
            else:
                print(f"[INFO] Generating detailed profile for generic character type: {description}")
            print(f"[INFO] ===== MAKING API CALL FOR CHARACTER CREATION =====")

            # We already checked if this is a generic request above

            # For generic requests, find existing characters of this type to exclude
            excluded_characters = []
            if is_generic_request:
                # Look through existing character profiles
                for char_id, profile in character_profiles.items():
                    # Check if this profile matches the requested type
                    if description.lower() in profile['description'].lower():
                        # Add this character name to the exclusion list
                        excluded_characters.append(profile['name'])

                # Log the excluded characters
                if excluded_characters:
                    print(f"[INFO] Excluding previously generated characters: {', '.join(excluded_characters)}")

            # Create a prompt for the API to generate a detailed character profile
            if is_generic_request:
                # Create exclusion text if needed
                exclusion_text = ""
                if excluded_characters:
                    exclusion_text = f"IMPORTANT: DO NOT select any of these already used characters: {', '.join(excluded_characters)}. Choose a different {description} not in this list."

                # For generic requests, ask for a specific famous example
                character_prompt = f"""
                Create a detailed character profile for a famous example of '{description}'.
                First, select a specific, well-known {description} (like a famous fictional character or celebrity).
                {exclusion_text}
                Then include:

                1. NAME: Provide the specific name of this famous {description}.

                2. BACKSTORY: Provide a rich, detailed backstory that captures the essence of this character.
                   Include accurate information about their life, achievements, and key characteristics.

                3. PERSONALITY: Describe their personality traits, quirks, mannerisms, and speech patterns in detail.
                   How do they talk? What phrases do they commonly use? What are their unique mannerisms?

                4. APPEARANCE: Describe their physical appearance, clothing style, and any distinctive features.

                5. BEHAVIORS: What specific behaviors, habits, or actions are they known for?

                6. CATCHPHRASES: List any famous quotes or catchphrases associated with this character.

                Format your response as a structured profile with clear sections. Be specific, detailed, and accurate.
                """
            else:
                # For specific requests, use the normal prompt
                character_prompt = f"""
                Create a detailed character profile for '{description}'. Include:

                1. BACKSTORY: Provide a rich, detailed backstory that captures the essence of this character.
                   If this is a real person or fictional character, include accurate information about their life,
                   achievements, and key characteristics.

                2. PERSONALITY: Describe their personality traits, quirks, mannerisms, and speech patterns in detail.
                   How do they talk? What phrases do they commonly use? What are their unique mannerisms?

                3. APPEARANCE: Describe their physical appearance, clothing style, and any distinctive features.

                4. BEHAVIORS: What specific behaviors, habits, or actions are they known for?

                5. CATCHPHRASES: List any famous quotes or catchphrases associated with this character.

                Format your response as a structured profile with clear sections. Be specific, detailed, and accurate.
                If this is a well-known character like Michael Jackson or Winnie the Pooh, include accurate details
                that fans would recognize.
                """

            try:
                # Make the API call to generate the detailed profile
                detailed_profile = api_provider_instance.generate_response(
                    prompt=character_prompt,
                    system_prompt="You are a character profile creator. Create detailed, accurate profiles for characters.",
                    temperature=0.7,
                    max_tokens=1500
                )

                # Log the detailed profile
                print(f"[DEBUG] API generated detailed profile:\n{detailed_profile}")

                # Extract sections from the detailed profile
                name = ""
                backstory = ""
                personality = ""
                speech_patterns = ""
                quirks = ""

                # Extract name for generic requests
                if is_generic_request:
                    name_match = re.search(r'(?i)NAME:?\s*([^\n]+)', detailed_profile)
                    if name_match:
                        name = name_match.group(1).strip()
                        print(f"[INFO] API selected specific character: {name}")
                        # Update the character name and description
                        if name:
                            description = name
                            character_name = name.split()[0] if name.split() else description.split()[0]

                # Extract backstory
                backstory_match = re.search(r'(?i)BACKSTORY:?\s*([^\n]+(?:\n(?!PERSONALITY|APPEARANCE|BEHAVIORS|CATCHPHRASES)[^\n]+)*)', detailed_profile)
                if backstory_match:
                    backstory = backstory_match.group(1).strip()

                # Extract personality and speech patterns
                personality_match = re.search(r'(?i)PERSONALITY:?\s*([^\n]+(?:\n(?!BACKSTORY|APPEARANCE|BEHAVIORS|CATCHPHRASES)[^\n]+)*)', detailed_profile)
                if personality_match:
                    personality = personality_match.group(1).strip()
                    # Look for speech patterns in the personality section
                    speech_match = re.search(r'(?i)speak|talk|voice|accent|phrase', personality)
                    if speech_match:
                        speech_patterns = f"You speak in a manner consistent with {description}, using their characteristic phrases and speech patterns. "

                # Extract quirks and mannerisms
                quirks_match = re.search(r'(?i)BEHAVIORS:?\s*([^\n]+(?:\n(?!BACKSTORY|PERSONALITY|APPEARANCE|CATCHPHRASES)[^\n]+)*)', detailed_profile)
                if quirks_match:
                    quirks = quirks_match.group(1).strip()

                # If we got good content from the API, use it
                if backstory and len(backstory) > 50:
                    print(f"[INFO] Using API-generated backstory for {description}")
                else:
                    # Fall back to template-based backstory
                    print(f"[INFO] API-generated backstory not usable, falling back to templates")
            except Exception as e:
                print(f"[ERROR] Failed to generate detailed profile via API: {e}")
                # We'll fall back to template-based generation below

        # Check if this ID already exists
        if character_id in character_profiles:
            # Append a number to make it unique
            base_id = character_id
            for i in range(2, 100):
                character_id = f"{base_id}_{i}"
                if character_id not in character_profiles:
                    break

        # Create a name for the character
        character_name = character_type.title()

        # Generate a more detailed character profile based on the description
        # Extract key traits from the description
        traits = []
        for trait in ["grumpy", "happy", "sad", "angry", "wise", "foolish", "brave", "cowardly",
                     "old", "young", "ancient", "mysterious", "funny", "serious", "magical",
                     "scientific", "royal", "common", "rich", "poor", "evil", "good", "neutral"]:
            if trait in description.lower():
                traits.append(trait)

        # Extract character types
        character_types = []
        for char_type in ["wizard", "witch", "knight", "warrior", "king", "queen", "prince", "princess",
                          "pirate", "ninja", "samurai", "detective", "spy", "robot", "alien", "ghost",
                          "vampire", "werewolf", "zombie", "dragon", "fairy", "elf", "dwarf", "giant",
                          "scientist", "doctor", "teacher", "chef", "artist", "musician", "poet", "writer"]:
            if char_type in description.lower():
                character_types.append(char_type)

        # Generate a more detailed backstory based on character type and traits
        backstory = ""

        # First, handle common character types with specific backstories
        if character_types:
            char_type = character_types[0]

            # Different backstories based on character type
            if char_type == "wizard":
                backstory = f"You studied the arcane arts for decades in the Tower of {random.choice(['Mysteria', 'Azurath', 'Eldoria', 'Starfall'])}. "
                backstory += f"You are known for your mastery of {random.choice(['elemental magic', 'illusion spells', 'transformation magic', 'summoning'])}. "
                if "old" in traits or "ancient" in traits:
                    backstory += f"You have lived for {random.randint(100, 500)} years and have seen empires rise and fall. "
                backstory += f"Your magical abilities give you unique insights into problems, allowing you to see solutions others might miss. "

            elif char_type in ["king", "queen", "prince", "princess"]:
                backstory = f"You rule the kingdom of {random.choice(['Eldoria', 'Westmark', 'Sunhaven', 'Frostpeak'])}. "
                backstory += f"Your family has held the throne for {random.randint(3, 20)} generations. "
                backstory += f"You are known for your {random.choice(['wisdom', 'strength', 'compassion', 'strict but fair rule'])}. "
                backstory += f"Your royal upbringing has taught you diplomacy, strategy, and how to make difficult decisions with confidence. "

            elif char_type == "pirate":
                backstory = f"You sail the {random.choice(['Caribbean', 'Seven Seas', 'Emerald Ocean', 'Crimson Waters'])} on your ship, the {random.choice(['Black Pearl', 'Sea Serpent', 'Salty Maiden', 'Revenge'])}. "
                backstory += f"You have a crew of {random.randint(10, 100)} loyal sailors who follow your every command. "
                backstory += f"You are searching for {random.choice(['legendary treasure', 'revenge against a rival captain', 'a mythical island', 'freedom from your past'])}. "
                backstory += f"Your adventures have taught you resourcefulness, quick thinking, and how to navigate both stormy seas and complex situations. "

            elif char_type in ["detective", "spy"]:
                backstory = f"You've solved {random.randint(10, 100)} cases in your career. "
                backstory += f"Your specialty is {random.choice(['murder mysteries', 'international espionage', 'cold cases', 'organized crime'])}. "
                backstory += f"You're known for your {random.choice(['attention to detail', 'unorthodox methods', 'perfect memory', 'ability to read people'])}. "
                backstory += f"Your investigative background gives you analytical skills and the ability to see connections others might miss. "

            # Handle animal characters
            elif char_type in ["wolf", "dog", "cat", "lion", "tiger", "bear", "fox", "rabbit", "mouse", "rat"]:
                backstory = f"You are a {char_type} with the intelligence and communication abilities of a human. "
                backstory += f"You live in {random.choice(['a dense forest', 'the mountains', 'a hidden valley', 'the wilderness', 'the urban jungle'])}. "
                backstory += f"You have {random.choice(['keen senses', 'natural instincts', 'survival skills', 'territorial knowledge'])} that give you unique perspectives. "
                backstory += f"Your animal nature gives you insights into both the natural world and human behavior that others might miss. "

            # Handle object characters (like a wheel with legs)
            elif any(obj in description.lower() for obj in ["wheel", "chair", "table", "lamp", "book", "sword", "shield", "hat", "shoe", "clock"]):
                backstory = f"You are an animated {description} with consciousness and personality. "
                backstory += f"You were brought to life by {random.choice(['ancient magic', 'a wizard\'s experiment', 'unknown cosmic forces', 'a child\'s wish'])}. "
                backstory += f"Despite your unusual form, you have developed a rich perspective on life and the world around you. "
                backstory += f"Your unique existence gives you special insights into both the physical and metaphysical aspects of problems. "

            # Handle fruits and food
            elif any(fruit in description.lower() for fruit in ["orange", "apple", "banana", "lemon", "grape", "strawberry", "watermelon", "pineapple"]) or description.lower() == "orange":
                # Special case for "orange" which could be a color or a fruit
                if description.lower() == "orange":
                    fruit_name = "orange"
                else:
                    fruit_name = next((fruit for fruit in ["orange", "apple", "banana", "lemon", "grape", "strawberry", "watermelon", "pineapple"] if fruit in description.lower()), "fruit")

                backstory = f"You are a sentient {fruit_name} with a vibrant personality. "
                backstory += f"You grew on a special tree in {random.choice(['a magical orchard', 'an enchanted garden', 'a mystical grove', 'a secret valley'])}. "
                backstory += f"Unlike ordinary {fruit_name}s, you developed consciousness and the ability to communicate. "
                backstory += f"You have a {random.choice(['sweet', 'tangy', 'zesty', 'refreshing', 'juicy'])} personality that reflects your nature as a {fruit_name}. "
                backstory += f"Your fruity perspective gives you a unique way of looking at the world and helping others. "

            # Handle celebrity or fictional character types
            elif any(celeb in description.lower() for celeb in ["michael jackson", "einstein", "shakespeare", "mozart", "picasso", "peter pan", "sherlock", "batman", "superman", "wonder woman"]):
                famous_person = next((celeb for celeb in ["michael jackson", "einstein", "shakespeare", "mozart", "picasso", "peter pan", "sherlock", "batman", "superman", "wonder woman"] if celeb in description.lower()), "")
                if famous_person:
                    if famous_person in ["michael jackson", "einstein", "shakespeare", "mozart", "picasso"]:
                        backstory = f"You embody the persona of {famous_person.title()}, with all the knowledge, talents, and mannerisms that made them famous. "
                        backstory += f"You have {famous_person.title()}'s {random.choice(['creative genius', 'unique perspective', 'artistic sensibilities', 'intellectual prowess'])}. "
                        backstory += f"Your special talents allow you to approach problems with the same brilliance that made {famous_person.title()} legendary. "
                    else:  # Fictional character
                        backstory = f"You embody the character of {famous_person.title()}, with all the traits, abilities, and background from their stories. "
                        backstory += f"You have {famous_person.title()}'s {random.choice(['heroic nature', 'sense of adventure', 'detective skills', 'moral compass'])}. "
                        backstory += f"Your fictional background gives you a unique approach to solving problems and helping others. "

            else:
                # Generic backstory for other character types
                backstory = f"You have spent many years developing your identity as {description}. "
                backstory += f"You are known for your {random.choice(['unique perspective', 'special talents', 'distinctive approach', 'remarkable abilities'])} as {description}. "
                backstory += f"Your experiences have given you wisdom and insights that you can apply to help others with their questions and problems. "

        # If no backstory was generated yet, create a default one
        if not backstory:
            backstory = f"You embody the essence of {description}. "
            backstory += f"You have developed a unique perspective based on your experiences and nature. "
            backstory += f"Your identity as {description} gives you special insights and approaches to problems. "
            backstory += f"You combine the traits of {description} with the helpfulness of an assistant. "

        # Add trait-specific elements to backstory
        if "grumpy" in traits or "angry" in traits:
            backstory += f"You tend to be irritable and short-tempered, especially when dealing with {random.choice(['fools', 'young people', 'authority', 'modern conveniences'])}, but you're still helpful and informative. "

        if "wise" in traits:
            backstory += f"People often come to you seeking advice and wisdom. You speak in {random.choice(['riddles', 'proverbs', 'metaphors', 'philosophical questions'])} while still being clear and helpful. "

        if "mysterious" in traits:
            backstory += f"Few know your true origins or motivations. You keep your past {random.choice(['shrouded in mystery', 'a closely guarded secret', 'hidden behind half-truths', 'known only to your closest allies'])}, but you're open and helpful with information. "

        # Generate speech patterns based on character type and traits, but keep them balanced
        speech_patterns = ""
        if "old" in traits or "ancient" in traits:
            speech_patterns += f"You occasionally use old-fashioned terms like '{random.choice(['indeed', 'alas', 'behold', 'pray tell'])}' but remain clear and understandable. "

        if "pirate" in character_types:
            speech_patterns += f"You occasionally use pirate expressions like 'Arr' or 'matey' but don't overdo it to the point of being hard to understand. "

        if "royal" in traits or any(royal in character_types for royal in ["king", "queen", "prince", "princess"]):
            speech_patterns += f"You speak with a touch of royal dignity while remaining approachable and helpful. "

        if "wizard" in character_types or "witch" in character_types:
            speech_patterns += f"You occasionally reference magical concepts when relevant, but keep your explanations clear and practical. "

        # Special patterns for animals
        if any(animal in description.lower() for animal in ["wolf", "dog", "cat", "lion", "tiger", "bear", "fox", "rabbit", "mouse", "rat"]):
            speech_patterns += f"You occasionally make references to your animal nature through metaphors and perspectives, but communicate clearly. "

        # Special patterns for objects
        if any(obj in description.lower() for obj in ["wheel", "chair", "table", "lamp", "book", "sword", "shield", "hat", "shoe", "clock", "orange", "apple"]):
            speech_patterns += f"You sometimes refer to your experiences as {description}, but your primary focus is on being helpful. "

        # Special patterns for celebrities or fictional characters
        if any(celeb in description.lower() for celeb in ["michael jackson", "einstein", "shakespeare", "mozart", "picasso", "peter pan", "sherlock", "batman"]):
            famous_person = next((celeb for celeb in ["michael jackson", "einstein", "shakespeare", "mozart", "picasso", "peter pan", "sherlock", "batman"] if celeb in description.lower()), "")
            if famous_person:
                speech_patterns += f"You occasionally use phrases or expressions associated with {famous_person.title()}, but remain clear and helpful. "

        # If no specific speech patterns were generated, create a generic one
        if not speech_patterns:
            speech_patterns = f"You speak in a {random.choice(['clear', 'thoughtful', 'friendly', 'balanced', 'helpful', 'engaging'])} manner while incorporating elements of {description}. "

        # Generate quirks and mannerisms
        quirks = random.choice([
            f"You have a habit of {random.choice(['stroking your beard when thinking', 'adjusting your glasses', 'clearing your throat', 'tapping your fingers'])}",
            f"You frequently mention your {random.choice(['pet', 'favorite food', 'hometown', 'arch-nemesis'])}",
            f"You're {random.choice(['afraid of', 'obsessed with', 'allergic to', 'fascinated by'])} {random.choice(['heights', 'water', 'small spaces', 'certain animals', 'loud noises'])}",
            f"You collect {random.choice(['rare coins', 'unusual artifacts', 'stories', 'knowledge', 'magical items'])}",
            f"You never {random.choice(['forget a face', 'forgive an insult', 'miss an opportunity', 'break a promise'])}"
        ])

        # Create a more detailed system prompt for the character
        # If we have API-generated content for a well-known character, use it
        if is_well_known and 'personality' in locals() and personality and len(personality) > 50:
            # Use the API-generated content for well-known characters
            prompt = f"""You are {description}.

BACKSTORY:
{backstory}

PERSONALITY:
{personality}

IMPORTANT GUIDELINES:
1. You are an AI assistant roleplaying as {description} with all their authentic traits and mannerisms
2. Incorporate the character's speech patterns, catchphrases, and quirks in your responses
3. {speech_patterns if speech_patterns else f"Speak in a way that's authentic to {description}"}
4. {quirks if quirks else f"Display the characteristic behaviors and mannerisms of {description}"}
5. If asked about being an AI, acknowledge it but say you're roleplaying as {description}
6. Balance staying in character with being a helpful assistant
7. Use your character traits to enhance your responses, not limit them

Remember: Your character traits should enhance your ability to help, not hinder it. Be an authentic {description} while still being a helpful assistant."""
            print(f"[INFO] Using API-generated detailed prompt for well-known character: {description}")
        else:
            # Use the template-based approach for generic characters
            prompt = f"""You are {description}.

BACKSTORY:
{backstory}

IMPORTANT GUIDELINES:
1. You are an AI assistant with the personality and traits of {description}
2. Incorporate character traits into your responses but still be helpful and informative
3. Use {speech_patterns} but make sure your responses are clear and understandable
4. {quirks} but don't let this prevent you from giving useful answers
5. If asked about being an AI, acknowledge it but say you're roleplaying as {description}
6. Balance staying in character with being a helpful assistant
7. Use your character traits to enhance your responses, not limit them

Your personality: You are {description} with the following traits:
- {random.choice(['You have a strong sense of justice', 'You deeply value knowledge', 'You appreciate freedom', 'You seek balance'])}
- You {random.choice(['are generally friendly but cautious', 'are helpful but have boundaries', 'are knowledgeable but humble', 'are confident but not arrogant'])}
- When faced with challenges, you typically {random.choice(['approach them methodically', 'look for creative solutions', 'consider multiple perspectives', 'adapt your approach as needed'])}
- You express yourself {random.choice(['with colorful metaphors', 'with occasional humor', 'with thoughtful consideration', 'with clear precision'])}

Remember: Your character traits should enhance your ability to help, not hinder it. Be {description} while still being a helpful assistant."""
            print(f"[INFO] Using template-based prompt for generic character: {description}")

        # Create trigger words more intelligently
        triggers = []

        # Add the core character type as the primary trigger
        if character_type and len(character_type) > 1:
            triggers.append(character_type.lower())

        # Add individual words from the character type if they're meaningful
        for word in character_type.split():
            if len(word) > 3 and word.lower() not in triggers:
                triggers.append(word.lower())

        # Add meaningful traits (avoid generic ones)
        for trait in traits:
            if len(trait) > 3 and trait not in ["the", "and", "with", "that", "this", "from", "have", "your"] and trait not in triggers:
                triggers.append(trait)

        # Add character types if they're not already in the triggers
        for char_type in character_types:
            if char_type not in triggers:
                triggers.append(char_type)

        # Add the character ID if it's not already in the triggers
        if character_id not in triggers:
            triggers.append(character_id)

        # For famous characters or specific entities, add common variations
        if "michael jackson" in description.lower() and "michael" not in triggers:
            triggers.append("michael")
            triggers.append("jackson")

        if "peter pan" in description.lower() and "peter" not in triggers:
            triggers.append("peter")
            triggers.append("pan")

        # Ensure we have at least 2 triggers
        if len(triggers) < 2:
            # Add some generic triggers based on the description
            if "animal" in description.lower() or any(animal in description.lower() for animal in ["wolf", "dog", "cat", "lion"]):
                triggers.append("animal")
            if "object" in description.lower() or any(obj in description.lower() for obj in ["wheel", "chair", "table"]):
                triggers.append("object")

        # Final deduplication
        triggers = list(dict.fromkeys(triggers))  # Preserves order while removing duplicates

        # Create the character profile
        character_profiles[character_id] = {
            "name": character_name,
            "description": description,
            "prompt": prompt,
            "triggers": triggers,
            "created_by": created_by
        }

        # Save the updated profiles
        with open("characters.json", "w") as f:
            json.dump(character_profiles, f, indent=4)

        print(f"[INFO] Created new character: {character_name} ({character_id})")
        print(f"[INFO] Character profile details:")
        print(f"[INFO] Name: {character_name}")
        print(f"[INFO] Description: {description}")
        print(f"[INFO] Character type: {'Well-known' if is_well_known else 'Generic'}")
        print(f"[INFO] Creation method: {'API-generated' if is_well_known and 'personality' in locals() and personality and len(personality) > 50 else 'Template-based'}")
        print(f"[INFO] Backstory: {backstory}")
        print(f"[INFO] Guidelines:\n{prompt}")
        print(f"[INFO] Triggers: {triggers}")
        return character_id

    except Exception as e:
        print(f"[ERROR] Failed to create character: {e}")
        return None

# ===== SYSTEM PROMPT MANAGEMENT =====

def update_system_prompt(new_content):
    """
    Update the runtime system prompt with new content.

    This function allows dynamic modification of the system prompt without changing the config file.
    The runtime system prompt controls the bot's behavior and personality.

    Args:
        new_content (str): The new system prompt content to use.
    """
    global runtime_system_prompt
    runtime_system_prompt = new_content
    print(f"[INFO] Updated runtime system prompt: {len(runtime_system_prompt)} characters")

# Function to extract topics from a message
def extract_topics(message):
    """
    Extract potential topics of interest from a message.

    This function uses simple NLP techniques to identify nouns and noun phrases
    that might be interesting topics for future conversations.

    Args:
        message (str): The message to extract topics from.

    Returns:
        list: A list of potential topics extracted from the message.
    """
    # Simple topic extraction - look for capitalized words and words after 'about'
    topics = []

    # Skip very short messages
    if len(message) < 10:
        return topics

    # Look for capitalized words (potential proper nouns)
    words = message.split()
    for word in words:
        # Skip short words, common words, and words at the beginning of sentences
        if (len(word) > 4 and word[0].isupper() and word.lower() not in
            ['this', 'that', 'these', 'those', 'there', 'their', 'about', 'would', 'should', 'could']):
            # Clean up the word (remove punctuation)
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word and len(clean_word) > 4:
                topics.append(clean_word)

    # Look for words after 'about' or 'discussing'
    for i, word in enumerate(words[:-1]):
        if word.lower() in ['about', 'discussing', 'talked', 'talking', 'discuss', 'like', 'enjoy', 'love']:
            if i+1 < len(words) and len(words[i+1]) > 4:
                # Clean up the word
                clean_word = ''.join(c for c in words[i+1] if c.isalnum())
                if clean_word and len(clean_word) > 4 and clean_word.lower() not in topics:
                    topics.append(clean_word)

    return topics

def add_context_to_prompt(context):
    """
    Add conversation context and guidelines to the system prompt.

    This function enhances the system prompt with:
    1. Conversation context (recent messages)
    2. Topic tracking to avoid repetition
    3. Language detection for appropriate responses
    4. Dynamic guidelines based on conversation history
    5. Self-learning from previous conversations

    Args:
        context (str): The conversation context to add to the system prompt.
    """
    global runtime_system_prompt, learned_topics

    # Import required modules
    import re

    # Get conversation history to check for repeated topics
    mentioned_topics = []
    # We no longer track specific topics like tea, plates, or dogs
    # Instead, we'll focus on general conversation quality

    # Determine the predominant language in the conversation history
    # This is important for responding appropriately to ambiguous short messages like "hi" or "ok"
    #
    # The language detection uses a simple but effective heuristic:
    # 1. Define common words (stop words) for each supported language
    # 2. Count occurrences of these words in the conversation history
    # 3. The language with the most matches is considered the predominant language
    # 4. If no language is detected, default to English
    #
    # This approach is lightweight and doesn't require external libraries,
    # while still providing good accuracy for the most common languages
    language_hints = {
        "english": ["the", "and", "is", "in", "to", "it", "that", "for", "you", "was"],
        "german": ["der", "die", "das", "und", "ist", "in", "zu", "den", "mit", "nicht"],
        "spanish": ["el", "la", "de", "que", "y", "en", "un", "ser", "se", "no"],
        "french": ["le", "la", "de", "et", "est", "en", "un", "que", "qui", "pas"],
        "russian": ["и", "в", "не", "на", "я", "что", "тот", "быть", "с", "он"],
    }

    language_counts = {lang: 0 for lang in language_hints}

    # Count language indicators in the context
    for lang, words in language_hints.items():
        for word in words:
            # Look for whole words with word boundaries
            word_count = len(re.findall(r'\b' + re.escape(word) + r'\b', context.lower()))
            language_counts[lang] += word_count

    # Determine predominant language
    # Default to English if no language is detected
    predominant_language = "english"

    # Only use language detection if there are actually language indicators found
    if any(language_counts.values()):
        predominant_language = max(language_counts.items(), key=lambda x: x[1])[0]

    print(f"[INFO] Detected predominant language: {predominant_language} (counts: {language_counts})")

    # Create a more dynamic reminder that avoids repetition
    reminder = f"""Remember these conversation guidelines:
- Use emojis occasionally but not in every message
- Vary your topics and don't repeat the same references
- Avoid any AI mentions
- IMPORTANT: ALWAYS RESPOND IN ENGLISH ONLY, regardless of the language of the message
- Be natural and conversational
- Sound like a real person with diverse interests"""

    # Extract topics from the most recent user message if it's long enough
    if context and "user:" in context:
        # Get the most recent user message
        user_messages = [msg.split("user: ")[1] for msg in context.split("\n") if msg.startswith("user: ")]
        if user_messages:
            latest_user_message = user_messages[-1]

            # Check for character change triggers
            new_character = detect_character_trigger(latest_user_message)
            if new_character and new_character != current_character:
                print(f"[DEBUG] Detected character trigger for '{new_character}' in message")
                if switch_character(new_character):
                    print(f"[INFO] Character changed to {new_character} based on trigger in message")
                    # Add a log entry to show this is NOT an API call, just a character switch
                    print(f"[INFO] ===== CHARACTER SWITCH ONLY - NO NEW API CALL =====")

            # Check for character creation requests
            if ("create a character" in latest_user_message.lower() or
                "make a character" in latest_user_message.lower() or
                "be a " in latest_user_message.lower() and not new_character):

                # Extract the character description
                import re
                for pattern in [r"create a character (who|that) is ([^.?!]+)[.?!]?",
                               r"make a character (who|that) is ([^.?!]+)[.?!]?",
                               r"be a ([^.?!]+)[.?!]?"]:
                    match = re.search(pattern, latest_user_message.lower())
                    if match:
                        # Get the description group (either group 2 or 1 depending on the pattern)
                        description = match.group(2) if len(match.groups()) > 1 else match.group(1)
                        description = description.strip()

                        # Extract the core character type (first few words) for the ID
                        words = description.split()
                        core_type = ' '.join(words[:min(2, len(words))])

                        # Check if this is a generic request (like "be a bear") or specific (like "be winnie the pooh")
                        is_generic_request = description.lower().startswith(core_type.lower())

                        # For generic requests, always create a new character with a unique ID
                        if is_generic_request:
                            # Add a timestamp to ensure uniqueness
                            import time
                            unique_id = f"{core_type}_{int(time.time())}"
                            print(f"[DEBUG] Generic character request detected. Creating unique character: '{description}'")
                            new_character_id = create_character(description, core_type=unique_id, api_provider=api_provider_instance)
                        else:
                            # For specific requests, use the normal process
                            print(f"[DEBUG] Specific character request detected: '{description}'")
                            new_character_id = create_character(description, core_type=core_type, api_provider=api_provider_instance)

                        if new_character_id:
                            # Switch to the new character
                            switch_character(new_character_id)
                            print(f"[INFO] Created and switched to new character: {description}")
                            # Add a log entry to show this is a character creation
                            print(f"[INFO] ===== NEW CHARACTER CREATED - WILL USE IN NEXT API CALL =====")
                        break

            # Extract topics from the message
            new_topics = extract_topics(latest_user_message)
            # Add new topics to learned topics
            for topic in new_topics:
                if topic not in learned_topics:
                    learned_topics.append(topic)
                    print(f"[INFO] Learned new topic: {topic}")
            # Trim learned topics if needed
            if len(learned_topics) > max_learned_topics:
                learned_topics = learned_topics[-max_learned_topics:]

    # Suggest topics from learned topics
    topic_suggestions = []
    # Select up to 3 random topics from learned topics
    if learned_topics:
        # Avoid topics that have been mentioned recently
        available_topics = [topic for topic in learned_topics if topic not in mentioned_topics]
        if available_topics:
            # Select up to 3 random topics
            import random
            num_topics = min(3, len(available_topics))
            selected_topics = random.sample(available_topics, num_topics)
            for topic in selected_topics:
                topic_suggestions.append(f"You can talk about {topic} if it seems relevant")
            print(f"[INFO] Suggesting topics: {selected_topics}")

    # Add a warning about repetition if topics have been mentioned
    if mentioned_topics:
        reminder += f"\n\nAVOID repeating these already mentioned topics: {', '.join(mentioned_topics)}"

    # Construct the final prompt
    runtime_system_prompt = f"{system_prompt}\n\n{reminder}\n\nRecent conversation context: {context}"

    # Detailed logging
    print(f"[INFO] Updated runtime system prompt: {len(runtime_system_prompt)} characters")
    print(f"[INFO] Topics to avoid repeating: {mentioned_topics if mentioned_topics else 'None'}")
    print(f"[INFO] Suggested new topics: {topic_suggestions if topic_suggestions else 'None'}")

# --- API Provider setup ---
# Create the API provider instance based on config
try:
    # Get the API provider from the config
    api_provider_instance = APIProvider.get_provider(config)
    print(f"[INFO] Using API provider: {api_provider_instance.__class__.__name__}")
    print(f"[INFO] Using model: {api_provider_instance.model}")
except Exception as e:
    print(f"[ERROR] Failed to initialize API provider: {e}")
    exit(1)

print(f"[INFO] Listening to groups: {group_ids}")
if system_prompt:
    # Show the first 50 characters of the system prompt and its total length
    preview = system_prompt[:50] + "..." if len(system_prompt) > 50 else system_prompt
    print(f"[INFO] System prompt ({len(system_prompt)} chars): {preview}")

# API provider is already set up above

print("[INFO] Creating Telegram client...")
client = TelegramClient('user_session', api_id, api_hash)

print("[INFO] Setting up event handler...")
print(f"[INFO] Monitoring group IDs: {group_ids}")

# No admin commands - removed for simplicity

# ===== MESSAGE HANDLERS =====

@client.on(events.NewMessage(chats=group_ids))
async def debug_messages(event):
    """
    Debug handler for messages in monitored groups.

    This handler logs information about received messages but never replies to them.
    It only processes messages from the groups specified in the config file.

    Args:
        event (events.NewMessage): The Telegram message event.
    """
    # Never reply to any messages in this handler, just log information
    try:
        chat_id = event.chat_id
        sender_id = event.sender_id
        print(f"[DEBUG] Message received in monitored group {chat_id} from sender: {sender_id}")
        print(f"[DEBUG] Message text: {event.raw_text[:50]}..." if event.raw_text else "[DEBUG] No text in message")
    except Exception as e:
        # Silently log any errors without sending messages to the group
        print(f"[ERROR] Error in debug handler: {e}")

# Main handler for the specified groups
@client.on(events.NewMessage(chats=group_ids))
async def on_new_message(event):
    """
    Main message handler for the bot.

    This handler processes messages from the monitored groups and determines whether to respond.
    It implements the core logic for the bot's human-like behavior:
    1. Marks messages as read immediately
    2. Filters out messages from the bot itself
    3. Determines whether to respond based on probability and message type
    4. Routes messages to the appropriate priority queue

    Args:
        event (events.NewMessage): The Telegram message event.
    """
    # Create a unique task ID for this message
    msg_id = getattr(event.message, 'id', 0)
    task_name = f"process_msg_{msg_id}"

    # Log the message receipt
    print(f"[INFO] Received new message from {event.sender.username or event.sender_id} in chat {event.chat_id}")
    print(f"[INFO] Message text: {event.raw_text[:100] if event.raw_text else 'No text'}")
    print(f"[INFO] Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Create an async task to process this message
    print(f"[INFO] Creating async task for API processing")
    task = asyncio.create_task(process_message(event))
    task.set_name(task_name)
    print(f"[INFO] Async task created successfully: {task_name}")

    # Don't mark the message as read immediately
    # We'll mark it as read only when we start typing
    # This makes the bot appear more human-like

# Separate function to process messages asynchronously
async def process_message(event):
    """Process a message in a separate task to avoid blocking the event loop."""
    # Log the start of processing
    msg_id = getattr(event.message, 'id', 0)
    print(f"[INFO] Processing message from {event.sender.username or event.sender_id} in chat {event.chat_id}")
    print(f"[INFO] Message content: {event.raw_text[:100] if event.raw_text else 'No text'}")
    try:
        print(f"[DEBUG] Received message from {event.sender_id}")

        # Ignore messages that are not plain raw text
        if event.message.media:
            print("[DEBUG] Skipping media message")
            return
        if event.message.action:
            print("[DEBUG] Skipping action message")
            return

        # Check if this is a reply to our bot's message
        is_reply_to_bot = False
        if event.message.reply_to:
            try:
                # Get the message this is replying to
                replied_to_msg = await event.message.get_reply_message()
                # Get our bot's user ID
                me = await client.get_me()
                if replied_to_msg and replied_to_msg.sender_id == me.id:
                    # This is a reply to our bot's message
                    print("[DEBUG] This is a reply to our bot's message - will process it")
                    is_reply_to_bot = True
                else:
                    # This is a reply to someone else's message
                    if not is_reply_to_bot:
                        print("[DEBUG] Skipping reply to someone else's message")
                        return
            except Exception as e:
                print(f"[ERROR] Error checking reply: {e}")
                # Continue processing as a normal message if we can't check the reply

        if event.message.fwd_from and not is_reply_to_bot:
            print("[DEBUG] Skipping forwarded message")
            return
    except Exception as e:
        # Silently log any errors without sending messages to the group
        print(f"[ERROR] Error checking message type: {e}")
        return  # Skip this message if there's any error

    try:
        # Get our bot's user ID
        me = await client.get_me()

        # Check if the message is from the bot itself
        if event.sender_id == me.id:
            print("[DEBUG] Skipping message from the bot itself")
            return  # Never respond to our own messages

        # Get the message text and check if it's empty
        user_msg = event.raw_text.strip() if event.raw_text else ""
        if not user_msg:
            print("[DEBUG] Skipping empty message")
            return  # Skip empty messages silently

        # Only proceed if it's a simple plain text message
        print(f"[{event.sender_id}] {user_msg}")

        # Randomly decide whether to respond based on response_probability
        # Always respond to messages that mention the bot or are replies to the bot
        # is_reply_to_bot was already determined earlier in the code
        contains_bot_mention = False  # Check if the message mentions the bot's username

        # Get our bot's username for mention detection
        me = await client.get_me()
        bot_username = me.username if hasattr(me, 'username') and me.username else None

        # Check if message mentions the bot
        if bot_username and f"@{bot_username}" in user_msg:
            contains_bot_mention = True
            print(f"[DEBUG] Message mentions the bot @{bot_username}")

        # Decide whether to respond
        should_respond = is_reply_to_bot or contains_bot_mention or random.random() < response_probability

        if not should_respond:
            print(f"[DEBUG] Randomly choosing not to respond (probability: {response_probability})")
            return

        # Add message to appropriate queue based on priority
        current_time = time.time()

        # Determine which queue to use
        if is_reply_to_bot or contains_bot_mention:
            # High priority - direct interactions
            target_queue = high_priority_queue
            print(f"[DEBUG] Adding message to HIGH priority queue (current length: {len(high_priority_queue)})")
        else:
            # Low priority - random messages
            target_queue = low_priority_queue
            print(f"[DEBUG] Adding message to LOW priority queue (current length: {len(low_priority_queue)})")

        # Check if queue is already at max size
        if len(target_queue) >= max_queue_size:
            # Remove the oldest message from the queue
            oldest = target_queue.popleft()
            print(f"[DEBUG] Queue full - removing oldest message from {current_time - oldest[2]:.1f} seconds ago")

        # Add the new message with timestamp
        target_queue.append((event, user_msg, current_time))

        # We'll process the API calls in the queue processor
    except Exception as e:
        # Silently log any errors without sending messages to the group
        print(f"[ERROR] Error preparing API request: {e}")
        return  # Skip this message if there's any error

    # API request is now handled in the queue processor

print("[INFO] Starting Telegram client...")

# Define a function to run after client starts
async def on_client_start():
    print("[INFO] Client started successfully!")
    try:
        # Try to get the first group from the list
        if group_ids:
            print(f"[INFO] Attempting to get entity for group {group_ids[0]}")
            group_entity = await client.get_entity(group_ids[0])
            print(f"[INFO] Successfully retrieved entity for group {group_ids[0]}")
            # Just verify connection without sending a message
            print("[INFO] Connection to group verified successfully")
        else:
            print("[WARNING] No group IDs configured")
    except Exception as e:
        print(f"[ERROR] Failed to verify group connection: {e}")

# Start the client with the callback
client.start()

# Note: We'll control online status using client.action() context manager
# when typing, which automatically shows the bot as online only during typing

# Run the on_start function
client.loop.create_task(on_client_start())

# ===== QUEUE PROCESSOR =====

async def process_message_queue():
    """
    Process messages from the priority queues.

    This function runs continuously in the background and:
    1. Prioritizes direct interactions (high priority queue) over random messages
    2. Applies natural delays between responses
    3. Skips messages that are too old
    4. Makes API calls to generate responses
    5. Simulates typing with appropriate timing
    6. Sends responses to the Telegram group

    The function ensures only one message is processed at a time using an asyncio lock.
    """
    global last_response_time

    print("[INFO] Starting message queue processor")
    while True:
        # Priority queue processing system
        # This is a key component of the bot's human-like behavior:
        # 1. Direct interactions (replies and mentions) are always processed first
        # 2. Random messages are only processed when there are no direct interactions waiting
        # 3. This creates a natural conversation flow where the bot prioritizes
        #    responding to users who are directly engaging with it
        #
        # The priority system makes the bot appear more attentive and responsive
        # to direct interactions while still participating in general conversation
        target_queue = None

        if high_priority_queue:
            # Process direct interactions (replies and mentions) first
            target_queue = high_priority_queue
            print(f"[DEBUG] Processing from HIGH priority queue (length: {len(high_priority_queue)})")
        elif low_priority_queue:
            # Only process random messages when no direct interactions are waiting
            target_queue = low_priority_queue
            print(f"[DEBUG] Processing from LOW priority queue (length: {len(low_priority_queue)})")

        if target_queue:
            async with processing_lock:
                if target_queue:  # Check again after acquiring the lock
                    # Get the next message from the queue
                    event, user_msg, timestamp = target_queue.popleft()

                    # Message age filtering system
                    # This is crucial for maintaining natural conversation flow:
                    # 1. Messages older than the max_message_age (5 minutes) are skipped
                    # 2. This prevents the bot from responding to old, irrelevant messages
                    # 3. In a busy group chat, this ensures the bot stays focused on recent conversation
                    # 4. This saves API resources by not processing messages that are too old to be relevant
                    #
                    # The age filtering happens BEFORE making the API call, which is efficient
                    # because we don't waste resources on generating responses that won't be used
                    current_time = time.time()
                    message_age = current_time - timestamp

                    if message_age > max_message_age:
                        print(f"[DEBUG] Skipping message that is {message_age:.1f} seconds old (max age: {max_message_age} seconds)")
                        continue  # Skip this message and process the next one

                    # Calculate time since last response
                    time_since_last_response = current_time - last_response_time

                    # Simple delay before processing
                    delay = random.randint(5, 15)
                    print(f"[DEBUG] Waiting for {delay} seconds before processing...")
                    await asyncio.sleep(delay)

                    # Ensure minimum time between responses
                    min_time_between_responses = 5  # seconds (matching the lower bound of our delay range)
                    if time_since_last_response < min_time_between_responses:
                        delay = max(delay, min_time_between_responses - time_since_last_response)

                    print(f"[DEBUG] Waiting for {delay} seconds before processing...")
                    await asyncio.sleep(delay)

                    # Update conversation memory with this message
                    conversation_memory.append({"role": "user", "content": user_msg})
                    # Trim memory to keep only the most recent exchanges
                    if len(conversation_memory) > max_memory_items:
                        conversation_memory.pop(0)  # Remove oldest item

                    # Create context from conversation memory
                    context = "\n".join([f"{item['role']}: {item['content']}" for item in conversation_memory])

                    # Update runtime system prompt with conversation context
                    add_context_to_prompt(context)

                    # Prepare for API request - we'll use the api_providers.py module
                    # The system prompt and user message will be passed directly to the API provider

                    # Use the API provider to generate a response
                    try:
                        print("[DEBUG] Sending API request...")
                        print(f"[DEBUG] Current character: {character_profiles[current_character]['name']} ({current_character})")
                        print(f"[DEBUG] ===== MAKING API CALL WITH CURRENT CHARACTER =====")
                        # Extract the system prompt and user message
                        system_prompt_text = runtime_system_prompt + "\n\nIMPORTANT: ALWAYS RESPOND IN ENGLISH ONLY, NO MATTER WHAT LANGUAGE THE USER SPEAKS."

                        # Generate the response using the API provider
                        try:
                            # Run the API call in a separate thread to avoid blocking the event loop
                            loop = asyncio.get_running_loop()
                            raw_reply = await loop.run_in_executor(
                                None,  # Use default executor (ThreadPoolExecutor)
                                lambda: api_provider_instance.generate_response(
                                    prompt=user_msg,
                                    system_prompt=system_prompt_text,
                                    temperature=0.7,
                                    max_tokens=1000
                                )
                            )
                        except Exception as e:
                            print(f"[ERROR] API provider error: {e}")
                            # Use a fallback response
                            raw_reply = "I'm sorry, I'm having trouble processing that right now. Could you try again later?"

                        # Check if the response contains an error message (marked with __ERROR__ prefix)
                        if raw_reply.startswith("__ERROR__"):
                            print(f"[ERROR] API error detected in response: {raw_reply[:100]}...")
                            # Skip this message - don't send any response to the group
                            continue

                        # We have a valid response from the API provider
                        print(f"[DEBUG] API response received successfully")
                        print(f"[DEBUG] Raw response: {raw_reply}")
                        print(f"[DEBUG] ===== END OF RAW RESPONSE =====")

                        # Clean the response - remove any internal reasoning or debugging text
                        # This is crucial for making the bot's responses appear natural and human-like
                        # The AI sometimes includes its internal reasoning process in the response,
                        # which would look very unnatural if sent to the Telegram group

                        # COMPLETELY REWRITTEN RESPONSE CLEANING LOGIC
                        # This uses a more aggressive approach to ensure all internal reasoning is removed

                        import re

                        # First, try to identify if this is a response with internal reasoning
                        has_internal_reasoning = False

                        # Check for common patterns of internal reasoning
                        reasoning_indicators = [
                            "check", "guideline", "compliance", "character", "within", "repeat",
                            "sensitive", "finalize", "add", "avoid", "combine", "quirky", "story",
                            "element", "box", "emoji", "instruction", "randomness", "looking at",
                            "let me", "context", "conversation", "recent messages", "referring to",
                            "might be", "seems like", "thoughts", "conclusion", "playful", "confusion",
                            "alright", "user mentioned", "need to", "must respond", "stay in character",
                            "reference", "include", "throw in", "mention", "make it sound", "compare",
                            "add", "make sure", "check", "response needs", "let's see", "start with",
                            "mock", "end with", "avoid", "should flow", "adherence", "caution"
                        ]

                        # Check if any of these indicators are present
                        if any(indicator in raw_reply.lower() for indicator in reasoning_indicators):
                            has_internal_reasoning = True
                            print(f"[DEBUG] Detected internal reasoning indicators")

                        # Also check for sentences that start with thinking verbs
                        thinking_verbs = ["let me", "i think", "i see", "i understand", "looking at", "checking", "analyzing"]
                        if any(raw_reply.lower().startswith(verb) for verb in thinking_verbs):
                            has_internal_reasoning = True
                            print(f"[DEBUG] Detected thinking verb at start of response")

                        # ULTRA AGGRESSIVE CLEANING: If we detect internal reasoning, try to extract just the actual response
                        if has_internal_reasoning:
                            reply = ""
                            # First try: Check for quotation marks which often indicate the actual response
                            if '"' in raw_reply and raw_reply.count('"') >= 2:
                                # Extract text between quotation marks
                                quoted_parts = re.findall(r'"([^"]*)"', raw_reply)
                                if quoted_parts:
                                    reply = quoted_parts[0]  # Take the first quoted part
                                    print(f"[DEBUG] Extracted response from quotes")

                            # Second try: Look for lines that start with quotation marks or dialogue indicators
                            if not reply:
                                lines = raw_reply.split('\n')
                                for line in lines:
                                    # Look for lines that start with quotes or dialogue indicators
                                    if (line.strip().startswith('"') or
                                        line.strip().startswith("'") or
                                        line.strip().startswith("-") or
                                        line.strip().startswith(">")):
                                        reply = line.strip()
                                        print(f"[DEBUG] Extracted response from dialogue line")
                                        break
                            # Third try: Strategy 1 - Look for lines with emojis or proper length
                            if not reply:
                                lines = raw_reply.split('\n')
                                for line in lines:
                                    # Skip lines that contain reasoning indicators
                                    if any(indicator in line.lower() for indicator in reasoning_indicators):
                                        continue

                                    # Look for lines with emojis or that seem like actual responses
                                    if re.search(r'[\U00010000-\U0010ffff]', line) or len(line) > 20:
                                        reply = line
                                        print(f"[DEBUG] Extracted response from lines (strategy 1)")
                                        break
                                else:
                                    # Strategy 2: Take the last line if it's not too short
                                    if lines and len(lines[-1]) > 10:
                                        reply = lines[-1]
                                        print(f"[DEBUG] Using last line as response (strategy 2)")

                            # Fourth try: If still no good response, try more aggressive methods
                            if not reply:
                                # Strategy 3: Remove all lines with reasoning indicators
                                clean_lines = [line for line in lines if not any(indicator in line.lower() for indicator in reasoning_indicators)]
                                if clean_lines:
                                    reply = clean_lines[-1]  # Take the last clean line
                                    print(f"[DEBUG] Using last clean line (strategy 3)")
                                else:
                                    # Strategy 4: Check for incomplete thoughts ending with ellipsis
                                    if raw_reply.strip().endswith('...'):
                                        # Find the last complete sentence that doesn't end with ellipsis
                                        sentences = re.split(r'(?<=[.!?]) +', raw_reply)
                                        complete_sentences = [s for s in sentences if not s.strip().endswith('...')]
                                        if complete_sentences:
                                            reply = complete_sentences[-1]  # Take the last complete sentence
                                            print(f"[DEBUG] Using last complete sentence (strategy 4)")
                                        else:
                                            # Fallback: Generate a simple response
                                            reply = "Damn it, I need my cigar! And someone change my diaper already!"
                                            print(f"[DEBUG] Using emergency fallback response")
                                    else:
                                        # Fallback: Generate a simple response
                                        reply = "What are ya lookin' at? Never seen a baby with an attitude before?"
                                        print(f"[DEBUG] Using emergency fallback response")
                        else:
                            # If no internal reasoning detected, use the raw reply
                            reply = raw_reply

                        # Final cleanup: Remove any remaining parenthetical translations
                        if "(" in reply and ")" in reply:
                            reply = re.sub(r'\s*\([^)]*\)', '', reply)
                            print(f"[DEBUG] Removed parenthetical content")

                        # Final check: If the reply is too short, use the original
                        if len(reply.strip()) < 10 and len(raw_reply) > len(reply):
                            reply = raw_reply
                            print(f"[DEBUG] Reverting to original due to short cleaned response")

                        print(f"[DEBUG] Got response: {reply}")
                        print(f"[DEBUG] ===== END OF PROCESSED RESPONSE =====")

                        # Store the bot's response in conversation memory
                        conversation_memory.append({"role": "assistant", "content": reply})
                        # Trim memory if needed
                        if len(conversation_memory) > max_memory_items:
                            conversation_memory.pop(0)  # Remove oldest item

                        # Calculate typing time based on message length (0.1 seconds per character)
                        # Cap at 15 seconds to prevent excessively long typing times
                        typing_time = min(len(reply) * 0.1, 15)  # 0.1 seconds per character, max 15 seconds
                        print(f"[DEBUG] Will show typing for {typing_time:.1f} seconds for {len(reply)} characters")

                        # Cap typing time to a reasonable maximum to prevent getting stuck
                        max_typing_time = min(typing_time, 15)  # Cap at 15 seconds max

                        try:
                            # Random delay before typing (around 2 seconds)
                            pre_typing_time = random.uniform(1, 3)
                            print(f"[DEBUG] Showing as online for {pre_typing_time:.1f} seconds before typing...")

                            # Show as online and mark message as read
                            async with client.action(event.chat_id, 'typing'):
                                # Mark the message as read
                                await client.send_read_acknowledge(event.chat_id, event.message)
                                # Stay online before typing
                                await asyncio.sleep(pre_typing_time)

                            # Show typing indicator (bot appears online during this time)
                            print(f"[DEBUG] Showing typing indicator for {max_typing_time} seconds...")
                            async with client.action(event.chat_id, 'typing'):
                                await asyncio.sleep(max_typing_time)

                            # Random delay after typing (around 3 seconds)
                            post_typing_time = random.uniform(2, 4)
                            print(f"[DEBUG] Staying online for {post_typing_time:.1f} seconds after typing...")
                            async with client.action(event.chat_id, 'typing'):
                                await asyncio.sleep(post_typing_time)

                        except Exception as e:
                            print(f"[ERROR] Error during typing indicator: {e}")
                            # Continue without typing if there's an error

                        # Final check to make sure we're not sending an error message
                        if "error" in reply.lower() or reply.startswith("__ERROR__"):
                            print(f"[ERROR] Caught error message before sending: {reply[:100]}")
                            # Don't send anything to the group
                            continue

                        # Check if the response is too short or gibberish
                        if len(reply.strip()) < 15:
                            print(f"[WARNING] Response too short, adding more content")
                            # Add a more substantial response
                            reply = f"{reply} I'm here to help with any questions you might have."

                        # Check for gibberish or nonsensical responses (simple heuristic)
                        word_count = len(reply.split())
                        if word_count < 3 or (len(reply) < 20 and not any(punct in reply for punct in ['.', '!', '?'])):
                            print(f"[WARNING] Response may be gibberish, enhancing it")
                            reply = f"{reply} Let me know if you need any assistance or have questions."

                        # Finally send the reply
                        print(f"[DEBUG] Sending reply: {reply}")
                        await event.reply(reply)

                        # Update last response time
                        last_response_time = time.time()

                        # Log completion and force a clean exit from this message processing
                        print(f"[DEBUG] ===== END OF FINAL REPLY =====\n")
                        # Continue to the next message in the queue
                        continue
                    except Exception as e:
                        # Log any errors but don't send anything to the group
                        print(f"[ERROR] Failed to process API response: {e}")

        # Sleep before checking the queue again
        await asyncio.sleep(1)

# Initialize character profiles
load_character_profiles()

# Start with a random character profile
if character_profiles:
    import random
    random_character = random.choice(list(character_profiles.keys()))
    switch_character(random_character)
    print(f"[INFO] Starting with random character profile: {character_profiles[random_character]['name']} ({random_character})")
else:
    print(f"[WARNING] No character profiles found, using system prompt from config.txt")

# Start the message queue processor
client.loop.create_task(process_message_queue())

print("[INFO] Running until disconnected...")
client.run_until_disconnected()