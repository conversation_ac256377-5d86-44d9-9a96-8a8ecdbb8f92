import asyncio
import aiohttp
import os
import traceback

# Configuration
URL = "https://api.mistral.ai/v1/chat/completions"
DELAY_SECONDS = 30  # Adjust as needed
API_KEY = "AMpMVqFQPpsTTzsq9HVqUWEs2jVEXcPw"
TEST_MESSAGE = "SAY A NUMBER BETWEEN 1 AND 10"

headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}",
}

# Mistral models to test (based on API documentation examples)
MISTRAL_MODELS = [
    "mistral-small-latest",
    "mistral-large-latest",
    "codestral-latest",
    "codestral-2405",
    "open-mistral-7b",
    "ministral-3b-latest",
    "ministral-8b-latest"
]

async def test_model(session, model_name):
    try:
        payload = {
            "model": model_name,
            "messages": [{"role": "user", "content": TEST_MESSAGE}]
        }
        async with session.post(URL, json=payload, headers=headers) as response:
            response.raise_for_status()
            data = await response.json()
            if 'choices' in data and len(data['choices']) > 0:
                print(f"  Response from {model_name}: {data['choices'][0]['message']['content']}")
                return True
            else:
                print(f"  No response from {model_name}: {data}")
                return False
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

async def main():
    async with aiohttp.ClientSession() as session:
        for model in MISTRAL_MODELS:
            print(f"\nTesting model: {model}")
            success = await test_model(session, model)
            print(f"  {model} is {'accessible' if success else 'NOT accessible'}")
            await asyncio.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    asyncio.run(main())
