{"servers": {"piper": {"enabled": true, "path": "/home/<USER>/colestart/piper_production_env/bin/piper", "models_dir": "/home/<USER>/colestart/piper_production_env/models", "output_dir": "/tmp/tts_output", "default_voice": "en_US-lessac-medium"}, "flask_server": {"enabled": false, "host": "localhost", "port": 5000, "endpoint": "/tts"}, "powershell": {"enabled": false, "script_path": "tts_scripts/powershell_tts.ps1"}, "vlc": {"enabled": false, "vlc_path": "/usr/bin/vlc"}}, "audio": {"format": "wav", "sample_rate": 22050, "channels": 1, "bit_depth": 16}, "playback": {"method": "windows_media", "volume": 0.8, "auto_cleanup": true, "max_file_age": 3600}, "queue": {"enabled": true, "max_queue_size": 50, "processing_timeout": 30}}