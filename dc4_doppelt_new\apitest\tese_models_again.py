import requests
import time

# Define valid models and their correct endpoints
models = [
    {
        "name": "Codestral FIM",
        "endpoint": "https://codestral.mistral.ai/v1/fim/completions",
        "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350",
        "payload": {
            "model": "codestral-latest",
            "prompt": "def hello_world():\n    \"\"\"Prints 'Hello, World!'\"\"\"\n",
            "suffix": "\n    print('Hello, World!')",
            "max_tokens": 50
        }
    },
    {
        "name": "Codestral Chat",
        "endpoint": "https://codestral.mistral.ai/v1/chat/completions",
        "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350",
        "payload": {
            "model": "codestral-latest",
            "messages": [{"role": "user", "content": "Hello, world!"}],
            "max_tokens": 50
        }
    },
    {
        "name": "Mistral Large",
        "endpoint": "https://api.mistral.ai/v1/chat/completions",
        "key": "YOUR_MISTRAL_API_KEY",  # Use a different key for Mistral
        "payload": {
            "model": "mistral-large-latest",
            "messages": [{"role": "user", "content": "Hello, world!"}],
            "max_tokens": 50
        }
    },
    # Add more valid models as needed
]

def test_models():
    for model in models:
        headers = {
            "Authorization": f"Bearer {model['key']}",
            "Content-Type": "application/json"
        }
        
        print(f"\nTesting {model['name']} at {model['endpoint']}")
        
        try:
            start_time = time.time()
            response = requests.post(
                model["endpoint"],
                headers=headers,
                json=model["payload"]
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ Success ({(response_time*1000):.2f}ms)")
                print(f"Response: {response.json()}")
            else:
                print(f"❌ Error {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"🚨 Request failed: {str(e)}")
        
        # Add delay to avoid rate limiting
        time.sleep(1)

if __name__ == "__main__":
    test_models()