from openai import OpenAI

# Your DeepSeek API key
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url=DEEPSEEK_BASE_URL)

def test_deepseek_model(model_name, test_message="Hello, DeepSeek!"):
    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": test_message}
            ],
            stream=False  # Non-stream example
        )
        print(f"Success with {model_name}: {response.choices[0].message.content}")
    except Exception as e:
        print(f"Error with {model_name}: {e}")

if __name__ == "__main__":
    free_deepseek_models = ["deepseek-chat", "deepseek-reasoner"]

    print("Testing free DeepSeek models...")
    for model in free_deepseek_models:
        print(f"\nTesting model: {model}")
        test_deepseek_model(model)