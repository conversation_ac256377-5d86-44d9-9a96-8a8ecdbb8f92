# System Prompts in Telegram Bot: Detailed Explanation

## Overview of System Prompts

In the Telegram bot implementation, system prompts serve two distinct purposes:

1. **Character Creation Prompts**: Used ONCE when creating a new character
2. **Runtime System Prompts**: Used with EVERY message after a character is created

## Character Creation Process

When a user requests a new character (e.g., "be <PERSON>"), the following process occurs:

1. The `create_character()` function is called
2. The function determines if it's a real person or fictional character
3. It selects the appropriate character creation prompt template
4. This prompt is sent to an API provider ONCE
5. The resulting character profile is saved for future use

### Character Creation Prompts

There are two types of character creation prompts:

#### For Real People:
```
Create a DETAILED character profile for '[description]' who appears to be a real person or public figure.
Include:
1. NAME: Full name and any common nicknames or titles.
2. BACKGROUND: 2-3 key facts about their origin, education, or rise to prominence (bullet points).
...
10. PUBLIC PERCEPTION: How they're generally viewed by different segments of the public or media (2-3 bullet points).

IMPORTANT:
- Be FACTUAL and ACCURATE about real-world information.
- Include their ACTUAL controversial statements, even if offensive or provocative.
...
```

#### For Fictional Characters:
```
Create a CONCISE character profile for '[description]' using bullet points and minimal text.
Include:
1. NAME: A fitting name (short and memorable).
2. BACKSTORY: 1-2 sentences maximum about origin and key events.
...
7. VOICE: 2-3 speech characteristics (bullet points).

IMPORTANT:
- DO NOT SPEAK IN 3. PERSON.
- DO NOT use generic phrases like 'How can I help you?'.
...
```

These prompts are ONLY sent once during character creation. They instruct the API to generate a character profile, not how to respond to every message.

## Message Handling Process

After a character is created, for each user message:

1. The bot retrieves the saved character profile (stored in `runtime_system_prompt`)
2. It adds response quality guidelines to prevent repetition and ensure helpfulness
3. It adds recent conversation context from both the group and direct interactions
4. This combined system prompt is sent to the API with each user message

### Runtime System Prompt Components

The runtime system prompt sent with each message has these components:

1. **Character Profile**: The profile generated during character creation
   ```
   You are Donald Trump.
   NAME: Donald Trump
   BACKGROUND: 
   - 45th President of the United States (2017-2021)
   - Businessman and real estate developer
   ...
   ```

2. **Response Guidelines**: Added to every message to improve quality
   ```
   IMPORTANT RESPONSE GUIDELINES:
   1. DO NOT use generic phrases like "How can I help?" or "What's your problem?"
   2. DO NOT repeat the same phrases you've used before
   ...
   14. USE character catchphrases sparingly and naturally, not in every response
   ```

3. **Conversation Context**: Recent messages from the group and direct interactions
   ```
   RECENT GROUP CONVERSATION:
   user1: What's happening with the economy?
   user2: Inflation is getting worse
   
   DIRECT CONVERSATION WITH USER:
   user: What do you think about inflation?
   ```

## API Call Flow

For each message, the API call includes:

1. **System Prompt**: The combined character profile, guidelines, and context
2. **User Message**: The actual message from the user
3. **Temperature**: Controls randomness (higher for more variety)
4. **Max Tokens**: Maximum length of the response

Example API call structure:
```python
response = provider.generate_response(
    prompt=user_msg,
    system_prompt=system_prompt_text,
    temperature=0.7,
    max_tokens=1000
)
```

## Key Points to Understand

1. Character creation prompts are used ONLY ONCE when creating a character
2. The resulting character profile becomes part of the runtime system prompt
3. The runtime system prompt is sent with EVERY message
4. Response guidelines and conversation context are added to the runtime system prompt
5. The complete system prompt guides the API to respond in character while following quality guidelines

## Code Implementation

The key functions involved in this process are:

1. `create_character()`: Creates a new character profile
2. `switch_character()`: Sets the active character and prepares the runtime system prompt
3. `on_new_message()`: Handles incoming messages and sends them to the API
4. `generate_frequency_response()`: Handles word frequency triggered responses

## Conclusion

The system prompt mechanism in the Telegram bot is a two-stage process:
1. Character creation (one-time) to generate a profile
2. Runtime system prompts (every message) that combine the profile with guidelines and context

This approach allows the bot to maintain character consistency while ensuring high-quality, non-repetitive responses that engage naturally with the conversation.
