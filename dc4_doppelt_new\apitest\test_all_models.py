import requests
import time

# Define the API endpoints and keys
models = [
    {"name": "Codestral", "endpoint": "https://codestral.mistral.ai/v1/fim/completions", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Codestral", "endpoint": "https://codestral.mistral.ai/v1/chat/completions", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Large", "endpoint": "https://api.mistral.ai/v1/mistral-large-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Pixtral Large", "endpoint": "https://api.mistral.ai/v1/pixtral-large-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Medium 3", "endpoint": "https://api.mistral.ai/v1/mistral-medium-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Saba", "endpoint": "https://api.mistral.ai/v1/mistral-saba-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Ministral 3B", "endpoint": "https://api.mistral.ai/v1/ministral-3b-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Ministral 8B", "endpoint": "https://api.mistral.ai/v1/ministral-8b-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Embed", "endpoint": "https://api.mistral.ai/v1/mistral-embed", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Moderation", "endpoint": "https://api.mistral.ai/v1/mistral-moderation-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral OCR", "endpoint": "https://api.mistral.ai/v1/mistral-ocr-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Small", "endpoint": "https://api.mistral.ai/v1/mistral-small-latest", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Pixtral", "endpoint": "https://api.mistral.ai/v1/pixtral-12b-2409", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mistral Nemo", "endpoint": "https://api.mistral.ai/v1/open-mistral-nemo", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Codestral Mamba", "endpoint": "https://api.mistral.ai/v1/open-codestral-mamba", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
    {"name": "Mathstral", "endpoint": "https://api.mistral.ai/v1/mathstral", "key": "n2H06Y7AXvV6qZK6kkY2CyoJv1Eoj350"},
]

def test_models():
    for model in models:
        headers = {
            "Authorization": f"Bearer {model['key']}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": model["name"].lower().replace(" ", "-"),
            "prompt": "Hello, world!",
            "max_tokens": 50
        }
        print(f"Request to {model['endpoint']}")
        print(f"Headers: {headers}")
        print(f"Payload: {payload}")
        try:
            response = requests.post(model["endpoint"], headers=headers, json=payload)
            response.raise_for_status()
            print(f"Success: {model['name']} - {response.json()}")
        except requests.exceptions.RequestException as e:
            print(f"Error: {model['name']} - {e}")

        # Break after the first iteration
        break

if __name__ == "__main__":
    test_models()