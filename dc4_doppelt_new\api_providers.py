"""
API Provider Manager for the Telegram Bot

This module provides a flexible way to switch between different AI API providers
(OpenRouter, Google, DeepSeek, etc.) without changing the main bot code.

Usage:
    1. Add API_PROVIDER=google in config.txt to use Google's API
    2. Add API_PROVIDER=openrouter in config.txt to use OpenRouter
    3. Add API_PROVIDER=deepseek in config.txt to use DeepSeek

Each provider has its own configuration settings in config.txt.
"""

import os
import json
import httpx
from typing import Dict, Any, Optional, List
import time

def _get_provider_instance(provider_name: str, api_key: str, model: str) -> 'APIProvider':
    """Helper function to get an instance of the API provider"""
    if provider_name == 'openrouter':
        return OpenRouterProvider(api_key, model)
    elif provider_name == 'cerebras':
        return CerebrasProvider(api_key, model)
    elif provider_name == 'cohere':
        return CohereProvider(api_key, model)
    elif provider_name == 'mistral':
        return MistralProvider(api_key, model)
    elif provider_name == 'lamini':
        return LaminiProvider(api_key, model)
    else:
        raise ValueError(f"Unknown API provider: {provider_name}")

class APIProvider:
    """Base class for all API providers"""

    def __init__(self, api_key: str, model: str):
        self.api_key = api_key
        self.model = model

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Generate a response using the API provider"""
        raise NotImplementedError("Each API provider must implement this method")

    @staticmethod
    def get_provider(config: Dict[str, str]) -> 'APIProvider':
        """Factory method to get the appropriate API provider based on config"""
        provider_order = config.get('PROVIDER_ORDER', 'openrouter').lower().split(',')
        max_retries = int(config.get('MAX_RETRIES', '3'))
        initial_delay = int(config.get('INITIAL_DELAY', '1'))

        for provider_name in provider_order:
            provider_name = provider_name.strip()
            api_key = None
            model = config.get('MODEL', '')
            
            if provider_name == 'openrouter':
                api_key = config.get('OPENROUTER_API_KEY', '')
                model = config.get('OPENROUTER_MODEL', '')
            elif provider_name == 'cerebras':
                api_key = config.get('CEREBRAS_API_KEY', '')
                model = config.get('CEREBRAS_MODEL', '')
            elif provider_name == 'cohere':
                api_key = config.get('COHERE_API_KEY', '')
                model = config.get('COHERE_MODEL', '')
            elif provider_name == 'mistral':
                api_key = config.get('MISTRAL_API_KEY', '')
                model = config.get('MISTRAL_MODEL', '')
            elif provider_name == 'lamini':
                api_key = config.get('LAMINI_API_KEY', '')
                model = config.get('LAMINI_MODEL', '')
            else:
                print(f"[WARNING] Unknown API provider: {provider_name}")
                continue

            if not api_key:
                print(f"[WARNING] API key is required for provider: {provider_name}")
                continue

            if api_key is None:
                print(f"[WARNING] API key is None for provider: {provider_name}")
                continue

            for attempt in range(max_retries + 1):
                try:
                    print(f"[INFO] Trying provider: {provider_name}, attempt: {attempt + 1}")
                    provider_instance = _get_provider_instance(provider_name, api_key, model)
                    return provider_instance
                except Exception as e:
                    print(f"[ERROR] Failed to initialize provider {provider_name}: {e}")
                    if attempt < max_retries:
                        delay = initial_delay * (2 ** attempt)
                        print(f"[INFO] Waiting {delay} seconds before retrying...")
                        time.sleep(delay)
                    else:
                        print(f"[ERROR] Max retries reached for provider {provider_name}")

        raise ValueError("No API provider could be initialized.")


class OpenRouterProvider(APIProvider):
    """Provider for OpenRouter API"""

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Generate a response using OpenRouter API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        try:
            with httpx.Client(timeout=30) as client:  # 30 second timeout
                response = client.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers=headers,
                    json=data
                )
            if response is None:
                print(f"[ERROR] OpenRouter API request failed: None response")
                return f"__ERROR__: OpenRouter API request failed: None response"
        except httpx.TimeoutException:
            print(f"[ERROR] OpenRouter API request timed out after 30 seconds")
            return f"__ERROR__: OpenRouter API request timed out. Please try again later."
        except Exception as e:
            print(f"[ERROR] OpenRouter API request failed: {e}")
            return f"__ERROR__: OpenRouter API request failed: {e}"

        if response is not None and response.status_code != 200:
            print(f"[ERROR] OpenRouter API error: {response.status_code} - {response.text}")
            # Return a special error indicator that the bot will recognize and not send to the group
            return f"__ERROR__: OpenRouter API error: {response.status_code} - {response.text}"

        try:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"[ERROR] Failed to parse OpenRouter response: {e}")
            return "__ERROR__: Failed to parse OpenRouter response"

class CerebrasProvider(APIProvider):
    """Provider for Cerebras API"""

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Generate a response using Cerebras API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": f"{system_prompt}\n{prompt}"
                }
            ]
        }

        try:
            with httpx.Client(timeout=30) as client:  # 30 second timeout
                response = client.post(
                    "https://api.cerebras.ai/v1/prediction",
                    headers=headers,
                    json=data
                )
        except httpx.TimeoutException:
            print(f"[ERROR] Cerebras API request timed out after 30 seconds")
            return f"__ERROR__: Cerebras API request timed out. Please try again later."
        except Exception as e:
            print(f"[ERROR] Cerebras API request failed: {e}")
            return f"__ERROR__: Cerebras API request failed: {e}"

        if response.status_code != 200:
            print(f"[ERROR] Cerebras API error: {response.status_code} - {response.text}")
            # Return a special error indicator that the bot will recognize and not send to the group
            return f"__ERROR__: Cerebras API error: {response.status_code} - {response.text}"

        try:
            result = response.json()
            return result["predictions"][0]["text"]
        except Exception as e:
            print(f"[ERROR] Failed to parse Cerebras response: {e}")
            return "__ERROR__: Failed to parse Cerebras response"

class CohereProvider(APIProvider):
    """Provider for Cohere API"""

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Generate a response using Cohere API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "message": f"{system_prompt}\n{prompt}"
        }

        try:
            with httpx.Client(timeout=30) as client:  # 30 second timeout
                response = client.post(
                    "https://api.cohere.ai/v1/generate",
                    headers=headers,
                    json=data
                )
        except httpx.TimeoutException:
            print(f"[ERROR] Cohere API request timed out after 30 seconds")
            return f"__ERROR__: Cohere API request timed out. Please try again later."
        except Exception as e:
            print(f"[ERROR] Cohere API request failed: {e}")
            return f"__ERROR__: Cohere API request failed: {e}"

        if response.status_code != 200:
            print(f"[ERROR] Cohere API error: {response.status_code} - {response.text}")
            # Return a special error indicator that the bot will recognize and not send to the group
            return f"__ERROR__: Cohere API error: {response.status_code} - {response.text}"

        try:
            result = response.json()
            return result["generations"][0]["text"]
        except Exception as e:
            print(f"[ERROR] Failed to parse Cohere response: {e}")
            return "__ERROR__: Failed to parse Cohere response"

class MistralProvider(APIProvider):
    """Provider for Mistral API"""

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Generate a response using Mistral API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "prompt": f"{system_prompt}\n{prompt}",
            "temperature": temperature,
            "max_tokens": max_tokens,
            "model": self.model
        }

        try:
            with httpx.Client(timeout=30) as client:  # 30 second timeout
                response = client.post(
                    "https://api.mistral.ai/v1/completions",
                    headers=headers,
                    json=data
                )
        except httpx.TimeoutException:
            print(f"[ERROR] Mistral API request timed out after 30 seconds")
            return f"__ERROR__: Mistral API request timed out. Please try again later."
        except Exception as e:
            print(f"[ERROR] Mistral API request failed: {e}")
            return f"__ERROR__: Mistral API request failed: {e}"

        if response.status_code != 200:
            print(f"[ERROR] Mistral API error: {response.status_code} - {response.text}")
            # Return a special error indicator that the bot will recognize and not send to the group
            return f"__ERROR__: Mistral API error: {response.status_code} - {response.text}"

        try:
            result = response.json()
            return result["choices"][0]["text"]
        except Exception as e:
            print(f"[ERROR] Failed to parse Mistral response: {e}")
            return "__ERROR__: Failed to parse Mistral response"

class LaminiProvider(APIProvider):
    """Provider for Lamini API"""

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """Generate a response using Lamini API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "prompt": f"{system_prompt}\n{prompt}",
            "temperature": temperature,
            "max_tokens": max_tokens,
            "model": self.model
        }

        try:
            with httpx.Client(timeout=30) as client:  # 30 second timeout
                response = client.post(
                    "https://api.lamini.ai/v1/generate",
                    headers=headers,
                    json=data
                )
        except httpx.TimeoutException:
            print(f"[ERROR] Lamini API request timed out after 30 seconds")
            return f"__ERROR__: Lamini API request timed out. Please try again later."
        except Exception as e:
            print(f"[ERROR] Lamini API request failed: {e}")
            return f"__ERROR__: Lamini API request failed: {e}"

        if response.status_code != 200:
            print(f"[ERROR] Lamini API error: {response.status_code} - {response.text}")
            # Return a special error indicator that the bot will recognize and not send to the group
            return f"__ERROR__: Lamini API error: {response.status_code} - {response.text}"

        try:
            result = response.json()
            return result["choices"][0]["text"]
        except Exception as e:
            print(f"[ERROR] Failed to parse Lamini response: {e}")
            return "__ERROR__: Failed to parse Lamini response"
