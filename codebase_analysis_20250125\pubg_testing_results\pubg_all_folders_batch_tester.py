#!/usr/bin/env python3
"""
🎮 PUBG FOLDERS COMPREHENSIVE BATCH TESTER
Systematically tests all Python scripts in pubg, pubg1, pubg2, pubg3 folders
"""

import os
import subprocess
import sys
from pathlib import Path
import time

def find_python_scripts(directory):
    """Find all Python scripts in directory and subdirectories"""
    python_scripts = []
    for root, dirs, files in os.walk(directory):
        # Skip __pycache__ directories
        dirs[:] = [d for d in dirs if d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                full_path = os.path.join(root, file)
                python_scripts.append(full_path)
    
    return sorted(python_scripts)

def test_script_syntax(script_path):
    """Test if a Python script has valid syntax"""
    try:
        result = subprocess.run(
            ['python3', '-m', 'py_compile', script_path],
            capture_output=True,
            text=True,
            timeout=15
        )
        return result.returncode == 0, result.stderr
    except subprocess.TimeoutExpired:
        return False, "Timeout during compilation"
    except Exception as e:
        return False, str(e)

def categorize_script(script_path):
    """Categorize script by PUBG folder"""
    if 'pubg3/' in script_path:
        return 'pubg3'
    elif 'pubg2/' in script_path:
        return 'pubg2'
    elif 'pubg1/' in script_path:
        return 'pubg1'
    elif 'pubg/' in script_path:
        return 'pubg'
    else:
        return 'other'

def main():
    print("🎯 PUBG FOLDERS COMPREHENSIVE TESTING STARTED")
    print("=" * 60)
    
    # Change to the correct directory
    base_dir = "/home/<USER>/colestart"
    os.chdir(base_dir)
    
    # Find all Python scripts in all PUBG folders
    pubg_folders = ["pubg", "pubg1", "pubg2", "pubg3"]
    all_scripts = []
    
    for folder in pubg_folders:
        if os.path.exists(folder):
            scripts = find_python_scripts(folder)
            all_scripts.extend(scripts)
            print(f"📁 {folder}: {len(scripts)} scripts found")
    
    total_scripts = len(all_scripts)
    print(f"📊 Total scripts across all PUBG folders: {total_scripts}")
    print("=" * 60)
    
    # Test results
    valid_scripts = []
    invalid_scripts = []
    categories = {}
    
    # Test each script
    for i, script in enumerate(all_scripts, 1):
        relative_path = os.path.relpath(script, base_dir)
        category = categorize_script(relative_path)
        
        print(f"[{i}/{total_scripts}] Testing: {relative_path}")
        print(f"  Category: {category}")
        
        is_valid, error_msg = test_script_syntax(script)
        
        if is_valid:
            print(f"  ✅ Syntax: VALID")
            valid_scripts.append(relative_path)
            if category not in categories:
                categories[category] = {'valid': 0, 'invalid': 0}
            categories[category]['valid'] += 1
        else:
            print(f"  ❌ Syntax: INVALID - {error_msg}")
            invalid_scripts.append((relative_path, error_msg))
            if category not in categories:
                categories[category] = {'valid': 0, 'invalid': 0}
            categories[category]['invalid'] += 1
    
    # Final results
    print("\n" + "=" * 60)
    print("🎯 PUBG FOLDERS TESTING COMPLETED")
    print("=" * 60)
    print(f"📊 Total Scripts: {total_scripts}")
    print(f"✅ Valid Scripts: {len(valid_scripts)} ({len(valid_scripts)/total_scripts*100:.1f}%)")
    print(f"❌ Invalid Scripts: {len(invalid_scripts)} ({len(invalid_scripts)/total_scripts*100:.1f}%)")
    
    # Save detailed results
    results_file = "codebase_analysis_20250125/pubg_testing_results/pubg_all_detailed_results.txt"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    
    with open(results_file, 'w') as f:
        f.write("🎮 PUBG FOLDERS DETAILED TESTING RESULTS\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Total Scripts: {total_scripts}\n")
        f.write(f"Valid Scripts: {len(valid_scripts)}\n")
        f.write(f"Invalid Scripts: {len(invalid_scripts)}\n\n")
        
        f.write("✅ VALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script in valid_scripts:
            f.write(f"  {script}\n")
        
        f.write("\n❌ INVALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script, error in invalid_scripts:
            f.write(f"  {script}\n")
            f.write(f"    Error: {error}\n\n")
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    # Summary by category
    print("\n📊 RESULTS BY PUBG FOLDER:")
    for category, stats in categories.items():
        total_cat = stats['valid'] + stats['invalid']
        success_rate = (stats['valid'] / total_cat * 100) if total_cat > 0 else 0
        print(f"  {category}: {stats['valid']}/{total_cat} valid ({success_rate:.1f}%)")

if __name__ == "__main__":
    main()
