import requests

# Replace with your actual AI21 Studio API key
API_KEY = "Oimh9SgRhHM3cW7bf430YQnXEN14tiBN"

# Base URL for AI21 Studio API
BASE_URL = "https://api.ai21.com/studio/v1/"

# List of available models and their endpoints
MODELS_AND_ENDPOINTS = [
    "jamba-large",
    "jamba-mini",
    # Add other models here if available (e.g., "jurassic-2", "jurassic-2-light", etc.)
]

# Example input for testing the models
TEST_PROMPT = "Explain the concept of artificial intelligence in simple terms."

def call_ai21_model(model_name, prompt):
    """
    Calls the specified AI21 Studio model with the given prompt.
    
    :param model_name: Name of the model to call (e.g., "jamba-large").
    :param prompt: The input text to send to the model.
    :return: Response from the API.
    """
    endpoint = f"{BASE_URL}{model_name}/complete"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "prompt": prompt,
        "maxTokens": 50,  # Adjust as needed
        "temperature": 0.7,  # Adjust as needed
        "stopSequences": []  # Optional: sequences to stop generation
    }
    
    try:
        response = requests.post(endpoint, headers=headers, json=payload)
        response.raise_for_status()  # Raise an error for bad status codes
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error calling model {model_name}: {e}")
        return None

def main():
    print("Testing AI21 Studio models...\n")
    
    for model in MODELS_AND_ENDPOINTS:
        print(f"Testing model: {model}")
        result = call_ai21_model(model, TEST_PROMPT)
        
        if result:
            print(f"Response from {model}:")
            print(result.get("completions", [{"data": {"text": "No completions returned."}}])[0]["data"]["text"])
        else:
            print(f"Failed to get a response from {model}.")
        
        print("-" * 80)

if __name__ == "__main__":
    main()