🚀 TRANSFERDOPPELT FOLDER DETAILED TESTING RESULTS
======================================================================

Total Scripts: 257
Valid Scripts: 254
Invalid Scripts: 3

✅ VALID SCRIPTS:
----------------------------------------
  <PERSON><PERSON>ppelt/Greg/add_hashes.py
  Transferdoppelt/Greg/bot.py
  Transferdoppelt/Greg/bot1.py
  Transferdoppelt/Greg/bot2.py
  Transferdoppelt/Greg/bot3.py
  Transferdoppelt/Greg/bot3r.py
  Transferdoppelt/Greg/botgod.py
  Transferdoppelt/Greg/botgod1.py
  Transferdoppelt/Greg/cv2.py
  Transferdoppelt/Greg/glink2.py
  Transferdoppelt/Greg/glinks.py
  Transferdoppelt/<PERSON>/godbot2.py
  <PERSON><PERSON><PERSON>t/Greg/godcj.py
  Transferdoppelt/Greg/godcj1.py
  Transferdoppelt/Greg/godcj2.py
  Transferdoppelt/Greg/godg (1).py
  <PERSON>doppelt/<PERSON>/godg (2).py
  Transferdoppelt/Greg/godg (3).py
  Transferdoppelt/Greg/godg (4).py
  Transferdoppelt/Greg/godg.py
  Transferdoppelt/Greg/godt3.py
  Transferdoppelt/Greg/godt3c.py
  Transferdoppelt/Greg/grabgroups.py
  Transferdoppelt/Greg/joingpt.py
  Transferdoppelt/Greg/joingroups.py
  Transferdoppelt/Greg/la.py
  Transferdoppelt/Greg/la2.py
  Transferdoppelt/Greg/lolcv.py
  Transferdoppelt/Greg/mex.py
  Transferdoppelt/Greg/newlife.py
  Transferdoppelt/Greg/ok.py
  Transferdoppelt/Greg/okl.py
  Transferdoppelt/Greg/okl1.py
  Transferdoppelt/Greg/okl2.py
  Transferdoppelt/Greg/okl3.py
  Transferdoppelt/Greg/okl4.py
  Transferdoppelt/Greg/okl5.py
  Transferdoppelt/Greg/okl6.py
  Transferdoppelt/Greg/okl6test.py
  Transferdoppelt/Greg/oklife.py
  Transferdoppelt/Greg/okok.py
  Transferdoppelt/Greg/t1.py
  Transferdoppelt/Greg/t2.py
  Transferdoppelt/Greg/t3.py
  Transferdoppelt/Greg/t4.py
  Transferdoppelt/Greg/t5.py
  Transferdoppelt/Greg/tb.py
  Transferdoppelt/Greg/tessa.py
  Transferdoppelt/Greg/tp.py
  Transferdoppelt/Greg/tp1.py
  Transferdoppelt/Greg/tpa.py
  Transferdoppelt/Greg/tpa1.py
  Transferdoppelt/Greg/tt.py
  Transferdoppelt/Greg/tt1.py
  Transferdoppelt/Greg/tt2.py
  Transferdoppelt/Greg/w2.py
  Transferdoppelt/Greg/w3r.py
  Transferdoppelt/Greg/was.py
  Transferdoppelt/Greg/was1.py
  Transferdoppelt/Greg/was2.py
  Transferdoppelt/Greg/was3.py.py
  Transferdoppelt/Greg/wave.py
  Transferdoppelt/Greg/wave1.py
  Transferdoppelt/Greg/wave2.py
  Transferdoppelt/Greg/wave3.py
  Transferdoppelt/Greg/wave4.py
  Transferdoppelt/Greg/wave5.py
  Transferdoppelt/Greg/webm.py
  Transferdoppelt/Kurznew/God/asf.py
  Transferdoppelt/Kurznew/asf (1).py
  Transferdoppelt/Kurznew/asf (10).py
  Transferdoppelt/Kurznew/asf (11).py
  Transferdoppelt/Kurznew/asf (2).py
  Transferdoppelt/Kurznew/asf (3).py
  Transferdoppelt/Kurznew/asf (4).py
  Transferdoppelt/Kurznew/asf (5).py
  Transferdoppelt/Kurznew/asf (6).py
  Transferdoppelt/Kurznew/asf (7).py
  Transferdoppelt/Kurznew/asf (8).py
  Transferdoppelt/Kurznew/asf (9).py
  Transferdoppelt/Kurznew/asf.py
  Transferdoppelt/Kurznew/async2.py
  Transferdoppelt/Kurznew/async3.py
  Transferdoppelt/Kurznew/asynchonkeinrestartinfiniteloop.py
  Transferdoppelt/Kurznew/asynchron2.py
  Transferdoppelt/Kurznew/asynchronkeinrestart.py
  Transferdoppelt/Kurznew/asynchronneu.py
  Transferdoppelt/Kurznew/asyncjac.py
  Transferdoppelt/Kurznew/asynclink.py
  Transferdoppelt/Kurznew/fertigbestbot.py
  Transferdoppelt/Kurznew/fertigbestbot10.py
  Transferdoppelt/Kurznew/fertigbestbot11.py
  Transferdoppelt/Kurznew/fertigbestbot12.py
  Transferdoppelt/Kurznew/fertigbestbot2.py
  Transferdoppelt/Kurznew/fertigbestbot3.py
  Transferdoppelt/Kurznew/fertigbestbot4.py
  Transferdoppelt/Kurznew/fertigbestbot5.py
  Transferdoppelt/Kurznew/fertigbestbot6.py
  Transferdoppelt/Kurznew/fertigbestbot7.py
  Transferdoppelt/Kurznew/fertigbestbot8.py
  Transferdoppelt/Kurznew/fertigbestbot9.py
  Transferdoppelt/Kurznew/gifserkannt.py
  Transferdoppelt/Kurznew/god.py
  Transferdoppelt/Kurznew/godc.py
  Transferdoppelt/Kurznew/godcj.py
  Transferdoppelt/Kurznew/godcj1.py
  Transferdoppelt/Kurznew/godg (1).py
  Transferdoppelt/Kurznew/godg (2).py
  Transferdoppelt/Kurznew/godg (3).py
  Transferdoppelt/Kurznew/godg.py
  Transferdoppelt/Kurznew/godmod.py
  Transferdoppelt/Kurznew/godt.py
  Transferdoppelt/Kurznew/godt3 (1).py
  Transferdoppelt/Kurznew/godt3.py
  Transferdoppelt/Kurznew/godt3c.py
  Transferdoppelt/Kurznew/godtime.py
  Transferdoppelt/Kurznew/godtime1.py
  Transferdoppelt/Kurznew/godtime2.py
  Transferdoppelt/Kurznew/kern.py
  Transferdoppelt/Kurznew/kern2.py
  Transferdoppelt/Kurznew/kern3.py
  Transferdoppelt/Kurznew/kurz.py
  Transferdoppelt/Kurznew/kurzforwardgerettet.py
  Transferdoppelt/Kurznew/kurzforwardgerettet2.py
  Transferdoppelt/Kurznew/kurzgut.py
  Transferdoppelt/Kurznew/kurzgut2.py
  Transferdoppelt/Kurznew/kurzgut3.py
  Transferdoppelt/Kurznew/kurzgut3compakt.py
  Transferdoppelt/Kurznew/kurzgut3compaktforward.py
  Transferdoppelt/Kurznew/kurzgut3fehler.py
  Transferdoppelt/Kurznew/kurzgut3gerettet.py
  Transferdoppelt/Kurznew/kurzgutcompaktforward3.py
  Transferdoppelt/Kurznew/main (1).py
  Transferdoppelt/Kurznew/main (2).py
  Transferdoppelt/Kurznew/main (3).py
  Transferdoppelt/Kurznew/main.py
  Transferdoppelt/Kurznew/neuermodelierungsstart.py
  Transferdoppelt/Kurznew/neuertestreparstur.py
  Transferdoppelt/Kurznew/neumodularisierungs1.py
  Transferdoppelt/Kurznew/newlife.py
  Transferdoppelt/Kurznew/periodischespeicherung.py
  Transferdoppelt/Kurznew/restartbot.py
  Transferdoppelt/Kurznew/restartbot1.py
  Transferdoppelt/Kurznew/sehrgut.py
  Transferdoppelt/Kurznew/sehrgut2.py
  Transferdoppelt/Kurznew/sehrgutchina.py
  Transferdoppelt/Kurznew/sehrgutkeinefehlerchina.py
  Transferdoppelt/Kurznew/testreparieren.py
  Transferdoppelt/Kurznew/utils (1).py
  Transferdoppelt/Kurznew/utils (2).py
  Transferdoppelt/Kurznew/utils (3).py
  Transferdoppelt/Kurznew/utils.py
  Transferdoppelt/Kurznew/wrf.py
  Transferdoppelt/Mannmannmann/config.py
  Transferdoppelt/Mannmannmann/handlers (1).py
  Transferdoppelt/Mannmannmann/handlers.py
  Transferdoppelt/Mannmannmann/logger.py
  Transferdoppelt/Mannmannmann/main.py
  Transferdoppelt/Neumodularisierung7files (1)/config.py
  Transferdoppelt/Neumodularisierung7files (1)/handlers.py
  Transferdoppelt/Neumodularisierung7files (1)/logger.py
  Transferdoppelt/Neumodularisierung7files (1)/main.py
  Transferdoppelt/Neumodularisierung7files (1)/modules/__init__.py
  Transferdoppelt/Neumodularisierung7files (1)/modules/config_loader.py
  Transferdoppelt/Neumodularisierung7files (1)/modules/group_loader.py
  Transferdoppelt/Neumodularisierung7files (1)/modules/message_handler.py
  Transferdoppelt/Neumodularisierung7files/config.py
  Transferdoppelt/Neumodularisierung7files/handlers.py
  Transferdoppelt/Neumodularisierung7files/logger.py
  Transferdoppelt/Neumodularisierung7files/main.py
  Transferdoppelt/Neumodularisierung7files/modules/__init__.py
  Transferdoppelt/Neumodularisierung7files/modules/config_loader.py
  Transferdoppelt/Neumodularisierung7files/modules/group_loader.py
  Transferdoppelt/Neumodularisierung7files/modules/message_handler.py
  Transferdoppelt/Neumoduldoppelt/config.py
  Transferdoppelt/Neumoduldoppelt/handlers (1).py
  Transferdoppelt/Neumoduldoppelt/handlers.py
  Transferdoppelt/Neumoduldoppelt/logger.py
  Transferdoppelt/Neumoduldoppelt/main.py
  Transferdoppelt/Newfiles/config.py
  Transferdoppelt/Newfiles/handlers.py
  Transferdoppelt/Newfiles/logger.py
  Transferdoppelt/Newfiles/main.py
  Transferdoppelt/Python/123.py
  Transferdoppelt/Python/Dragon/config_loader.py
  Transferdoppelt/Python/Dragon/handlers.py
  Transferdoppelt/Python/Dragon/logger.py
  Transferdoppelt/Python/Dragon/main.py
  Transferdoppelt/Python/Dragon/telegram_client.py
  Transferdoppelt/Python/Dragon2 (1)/config.py
  Transferdoppelt/Python/Dragon2 (1)/handlers (1).py
  Transferdoppelt/Python/Dragon2 (1)/handlers (2).py
  Transferdoppelt/Python/Dragon2 (1)/handlers (3).py
  Transferdoppelt/Python/Dragon2 (1)/handlers.py
  Transferdoppelt/Python/Dragon2 (1)/logger.py
  Transferdoppelt/Python/Dragon2 (1)/main.py
  Transferdoppelt/Python/Dragon2/config.py
  Transferdoppelt/Python/Dragon2/handlers (1).py
  Transferdoppelt/Python/Dragon2/handlers (2).py
  Transferdoppelt/Python/Dragon2/handlers (3).py
  Transferdoppelt/Python/Dragon2/handlers.py
  Transferdoppelt/Python/Dragon2/logger.py
  Transferdoppelt/Python/Dragon2/main.py
  Transferdoppelt/Python/alarm.py
  Transferdoppelt/Python/alarm1.py
  Transferdoppelt/Python/bot.py
  Transferdoppelt/Python/copilot.py
  Transferdoppelt/Python/copilot2.py
  Transferdoppelt/Python/copilot3.py
  Transferdoppelt/Python/copilot5.py
  Transferdoppelt/Python/dochnochgerettet.py
  Transferdoppelt/Python/dragonstart.py
  Transferdoppelt/Python/dragonstart2.py
  Transferdoppelt/Python/dragonstart3.py
  Transferdoppelt/Python/dragonstart4.py
  Transferdoppelt/Python/dragonstart5.py
  Transferdoppelt/Python/dragonstart7testlost.py
  Transferdoppelt/Python/dragonstartlost9.py
  Transferdoppelt/Python/endstation.py
  Transferdoppelt/Python/erkapiertesnicht.py
  Transferdoppelt/Python/funkt.py
  Transferdoppelt/Python/funkt2.py
  Transferdoppelt/Python/funkt3.py
  Transferdoppelt/Python/ganzschlau.py
  Transferdoppelt/Python/gutanal.py
  Transferdoppelt/Python/gutanal2.py
  Transferdoppelt/Python/kurzmodularisierungstart.py
  Transferdoppelt/Python/lasttest.py
  Transferdoppelt/Python/lasttest2.py
  Transferdoppelt/Python/lasttestanalblackwhite.py
  Transferdoppelt/Python/losgehts.py
  Transferdoppelt/Python/newfile.py
  Transferdoppelt/Python/newlife.py
  Transferdoppelt/Python/newlifewhynottest.py
  Transferdoppelt/Python/tessatest.py
  Transferdoppelt/Python/wannabe.py
  Transferdoppelt/Python/wiederneumodular.py
  Transferdoppelt/Tesseract/tesseract/nsis/find_deps.py
  Transferdoppelt/Tesseract/tesseract/src/lstm/generate_lut.py
  Transferdoppelt/Tesseract/tesseract/tessdata/godt3.py
  Transferdoppelt/Tesseract/tesseract/tessdata/newlife.py
  Transferdoppelt/Testzero/1.py
  Transferdoppelt/add_hashes.py
  Transferdoppelt/copilot4.py
  Transferdoppelt/dragonstart6.py
  Transferdoppelt/gag.py
  Transferdoppelt/gag1.py
  Transferdoppelt/godg (1).py
  Transferdoppelt/godg.py
  Transferdoppelt/godt3.py
  Transferdoppelt/grabgroups1.py
  Transferdoppelt/guttestdebuganal.py
  Transferdoppelt/newtestlogonwanalblacwhite.py
  Transferdoppelt/read_hashes.py

❌ INVALID SCRIPTS:
----------------------------------------
  Transferdoppelt/Greg/wave6.py
    Error:   File "Transferdoppelt/Greg/wave6.py", line 393
    continue
    ^^^^^^^^
SyntaxError: 'continue' not properly in loop


  Transferdoppelt/Python/dragonstartlost8.py
    Error:   File "Transferdoppelt/Python/dragonstartlost8.py", line 50
    if not API_ID oder not API_HASH:
                  ^^^^
SyntaxError: invalid syntax


  Transferdoppelt/Python/wasisthierlos.py
    Error:   File "Transferdoppelt/Python/wasisthierlos.py", line 63
    if not API_ID oder API_HASH:
                  ^^^^
SyntaxError: invalid syntax


