import asyncio
import aiohttp
import os
import traceback

# Configuration
API_KEY = "2b60e00f84923b61cbfd1323d25e699f19d09a4a250be04bed33db2804c68b63"
BASE_URL = "https://api.together.xyz/v1"
CHAT_ENDPOINT = "/chat/completions"
URL = BASE_URL + CHAT_ENDPOINT
DELAY_SECONDS = 5  # Adjust as needed
TEST_MESSAGE = "say a number between 1 and 10, say nothing more!"

headers = {
    "Authorization": f"Bearer {API_KEY}"
}

# Together AI models to test (from user's feedback)
TOGETHER_MODELS = [
    "BAAI/bge-base-en-v1.5",
    "BAAI/bge-large-en-v1.5",
    "black-forest-labs/FLUX.1-canny",
    "black-forest-labs/FLUX.1-depth",
    "black-forest-labs/FLUX.1-dev",
    "black-forest-labs/FLUX.1-dev-lora",
    "black-forest-labs/FLUX.1-pro",
    "black-forest-labs/FLUX.1-redux",
    "black-forest-labs/FLUX.1-schnell",
    "black-forest-labs/FLUX.1-schnell-Free",
    "black-forest-labs/FLUX.1.1-pro",
    "cartesia/sonic",
    "cartesia/sonic-2",
    "deepseek-ai/DeepSeek-R1",
    "deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
    "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B",
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
    "deepseek-ai/DeepSeek-V3",
    "deepseek-ai/DeepSeek-V3-p-dp",
    "google/gemma-2-27b-it",
    "google/gemma-2-9b-it",
    "google/gemma-2b-it",
    "Gryphe/MythoMax-L2-13b",
    "Gryphe/MythoMax-L2-13b-Lite",
    "meta-llama/Llama-2-13b-chat-hf",
    "meta-llama/Llama-2-70b-hf",
    "meta-llama/Llama-3-70b-chat-hf",
    "meta-llama/Llama-3-8b-chat-hf",
    "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo",
    "meta-llama/Llama-3.2-3B-Instruct-Turbo",
    "meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo",
    "meta-llama/Llama-3.3-70B-Instruct-Turbo",
    "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
    "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8",
    "meta-llama/Llama-4-Scout-17B-16E-Instruct",
    "meta-llama/Llama-Guard-3-11B-Vision-Turbo",
    "meta-llama/Llama-Vision-Free",
    "meta-llama/LlamaGuard-2-8b",
    "meta-llama/Meta-Llama-3-70B-Instruct-Turbo",
    "meta-llama/Meta-Llama-3-8B-Instruct-Lite",
    "meta-llama/Meta-Llama-3-8B-Instruct-Turbo",
    "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
    "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo",
    "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo",
    "meta-llama/Meta-Llama-Guard-3-8B",
    "microsoft/WizardLM-2-8x22B",
    "mistralai/Mistral-7B-Instruct-v0.1",
    "mistralai/Mistral-7B-Instruct-v0.2",
    "mistralai/Mistral-7B-Instruct-v0.3",
    "mistralai/Mistral-Small-24B-Instruct-2501",
    "mistralai/Mixtral-8x7B-Instruct-v0.1",
    "mistralai/Mixtral-8x7B-v0.1",
    "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO",
    "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF",
    "Qwen/Qwen2-72B-Instruct",
    "Qwen/Qwen2-VL-72B-Instruct",
    "Qwen/Qwen2.5-72B-Instruct-Turbo",
    "Qwen/Qwen2.5-7B-Instruct-Turbo",
    "Qwen/Qwen2.5-Coder-32B-Instruct",
    "Qwen/Qwen2.5-VL-72B-Instruct",
    "Qwen/QwQ-32B",
    "Salesforce/Llama-Rank-V1",
    "scb10x/scb10x-llama3-1-typhoon2-70b-instruct",
    "scb10x/scb10x-llama3-1-typhoon2-8b-instruct",
    "togethercomputer/m2-bert-80M-2k-retrieval",
    "togethercomputer/m2-bert-80M-32k-retrieval",
    "togethercomputer/m2-bert-80M-8k-retrieval",
    "togethercomputer/MoA-1",
    "togethercomputer/MoA-1-Turbo",
    "upstage/SOLAR-10.7B-Instruct-v1.0",
    "WhereIsAI/UAE-Large-V1"
]

async def test_model(session, model_name):
    try:
        payload = {
            "model": model_name,
            "prompt": TEST_MESSAGE,
        }
        async with session.post(URL, json=payload, headers=headers) as response:
            response.raise_for_status()
            data = await response.json()
            if 'output' in data and 'choices' in data['output'] and len(data['output']['choices']) > 0:
                print(f"  Response from {model_name}: {data['output']['choices'][0]['text']}")
                return True
            else:
                print(f"  No response from {model_name}: {data}")
                return False
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

async def main():
    async with aiohttp.ClientSession() as session:
        for model in TOGETHER_MODELS:
            print(f"\nTesting model: {model}")
            success = await test_model(session, model)
            print(f"  {model} is {'accessible' if success else 'NOT accessible'}")
            await asyncio.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    asyncio.run(main())
