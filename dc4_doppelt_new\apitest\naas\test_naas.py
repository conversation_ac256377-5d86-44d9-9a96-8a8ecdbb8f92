import requests
import json

def test_naas_api():
    # Configuration
    api_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzNDk4YWE5MC1kNTc4LTRkOGUtOWRkOS0wYjAxMmQxYjU1ZWIiLCJhcGlfa2V5X2lkIjoiMzE0OTcxZWEtNzU2NC00M2NjLTlhMTctMGVlMzRjNTA3MTQwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDQtMTJUMDk6MjE6NTMuODUwODA2KzAwOjAwIn0.0irR8V6GseNppTqfhDnENiJB54kpvXD0eF5sXDPxRjM"
    
    # API endpoints
    BASE_URL = "https://api.naas.ai/v1"
    
    # Headers
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # First, let's try to list models
    print("1. Testing models endpoint...")
    try:
        models_response = requests.get(f"{BASE_URL}/models", headers=headers)
        print(f"Models Status: {models_response.status_code}")
        print(f"Models Response: {models_response.text}\n")
    except Exception as e:
        print(f"Models Error: {str(e)}\n")

    # Then, let's try chat completion
    print("2. Testing chat completion...")
    chat_data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Say hi"}
        ]
    }

    try:
        chat_response = requests.post(
            f"{BASE_URL}/chat/completions",
            headers=headers,
            json=chat_data
        )
        print(f"Chat Status: {chat_response.status_code}")
        print(f"Chat Response: {chat_response.text}")
    except Exception as e:
        print(f"Chat Error: {str(e)}")

if __name__ == "__main__":
    test_naas_api()


