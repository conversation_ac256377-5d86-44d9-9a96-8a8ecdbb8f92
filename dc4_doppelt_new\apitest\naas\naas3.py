import requests
import json
import time

# Replace with your actual Naas.ai API key
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzNDk4YWE5MC1kNTc4LTRkOGUtOWRkOS0wYjAxMmQxYjU1ZWIiLCJapi_a2V5X2lkIjoiMzE0OTcxZWEtNzU2NC00M2NjLTlhMTctMGVlMzRjNTA3MTQwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDQtMTJUMDk6MjE6NTMuODUwODA2KzAwOjAwIn0.0irR8V6GseNppTqfhDnENiJB54kpvXD0eF5sXDPxRjM"  # IMPORTANT: Use the API key you provided

# Base URL for the Naas.ai API
BASE_URL = "https://api.naas.ai/v1"  # confirmed from the docs

# Headers for API requests
headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

def fetch_ai_models():
    """
    Fetches a list of AI models from the Naas.ai API.
    """
    url = f"{BASE_URL}/ai_models"  # Corrected endpoint:  Use ai_models
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        data = response.json()
        # print(json.dumps(data, indent=4)) #debug
        return data.get("ai_models", [])  # Returns a list of ai models, empty list default.  Use ai_models
    except requests.exceptions.RequestException as e:
        print(f"Error fetching AI models: {e}")
        return []  # Return an empty list on error to avoid crashing the program



def test_ai_model(model_id):
    """
    Tests a specific AI model using the chat/completions endpoint.

    Args:
        model_id (str): The ID of the AI model to test.

    Returns:
        str: The generated text, or None on error.
    """
    url = f"{BASE_URL}/chat/completions"  # Use the chat/completions endpoint
    payload = {
        "model": model_id,
        "messages": [{"role": "user", "content": "This is a test prompt."}],  #  message
        "max_tokens": 20  # keep it short
    }
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        data = response.json()
        # print(json.dumps(data, indent=4)) #debug
        if "choices" in data and len(data["choices"]) > 0:
            return data["choices"][0]["message"]["content"] #extracting the text
        else:
            print(f"Model {model_id} returned no choices.")
            return "No response"
    except requests.exceptions.RequestException as e:
        print(f"Error testing model {model_id}: {e}")
        return None

def main():
    """
    Main function to fetch AI models and test them.
    """
    print("Fetching AI models from Naas.ai...")
    ai_models = fetch_ai_models()

    if not ai_models:
        print("No AI models found or error fetching models.")
        return

    print(f"Found {len(ai_models)} AI models:")
    for model in ai_models:
        print(f"  - ID: {model['id']}, Name: {model.get('name', 'N/A')}, Provider: {model.get('provider', 'N/A')}")

    print("\nTesting AI models:")
    model_data = [] #store the results
    for model in ai_models:
        model_id = model["id"]
        print(f"\nTesting model: {model_id}")
        response_text = test_ai_model(model_id)
        if response_text:
            print(f"  Response: {response_text}")
        else:
            print(f"  No response or error.")
        time.sleep(5)  # Add a delay to avoid overwhelming the API (adjust as needed)
        model_data.append({
            "id": model_id,
            "name": model.get('name', 'N/A'),
            "provider": model.get('provider', 'N/A'),
            "endpoint": "/chat/completions",  # Hardcoded, as the general pattern,  from docs
            "response": response_text
        })
    # Save model data to a JSON file
    with open("naas_ai_models.json", "w") as f:
        json.dump(model_data, f, indent=4)
    print("\nModel data saved to naas_ai_models.json")

if __name__ == "__main__":
    main()
