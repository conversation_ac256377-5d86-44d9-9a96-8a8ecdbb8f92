# Telegram Bot API Provider Troubleshooting Plan

## 1. Check Configuration File Format

- Verify that config.txt is properly formatted
- Ensure all API keys are correctly assigned
- Check that model names are correctly specified
- Verify the provider order is correctly set

## 2. Test Individual Providers

Create a simple test script to test each provider individually:

```python
# test_provider.py
import sys
from or3_combined2 import *

def test_provider(provider_name):
    config = load_config()
    
    # Get API key and model for the specified provider
    if provider_name.lower() == 'openrouter':
        api_key = config.get('OPENROUTER_API_KEY', '')
        model = config.get('OPENROUTER_MODEL', '')
    elif provider_name.lower() == 'cerebras':
        api_key = config.get('CEREBRAS_API_KEY', '')
        model = config.get('CEREBRAS_MODEL', '')
    elif provider_name.lower() == 'cohere':
        api_key = config.get('COHERE_API_KEY', '')
        model = config.get('COHERE_MODEL', '')
    elif provider_name.lower() == 'mistral':
        api_key = config.get('MISTRAL_API_KEY', '')
        model = config.get('MISTRAL_MODEL', '')
    elif provider_name.lower() == 'codestral':
        api_key = config.get('CODESTRAL_API_KEY', '')
        model = config.get('CODESTRAL_MODEL', '')
    elif provider_name.lower() == 'lamini':
        api_key = config.get('LAMINI_API_KEY', '')
        model = config.get('LAMINI_MODEL', '')
    else:
        print(f"Unknown provider: {provider_name}")
        return
    
    # Initialize provider
    provider_instance = _get_provider_instance(provider_name.lower(), api_key, model)
    
    # Test with a simple prompt
    print(f"Testing {provider_name} with model {model}...")
    response = provider_instance.generate_response(
        prompt="Hello, how are you?",
        system_prompt="You are a helpful assistant.",
        temperature=0.7,
        max_tokens=100
    )
    
    print(f"Response: {response}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_provider.py <provider_name>")
        sys.exit(1)
    
    test_provider(sys.argv[1])
```

## 3. Check Model Availability

For each provider, verify that the models specified in config.txt are actually available:

### OpenRouter
- Check available models at: https://openrouter.ai/docs#models

### Cerebras
- Check available models in their documentation

### Cohere
- Available models: command, command-light, command-r, command-r-plus

### Mistral
- Available models: mistral-tiny, mistral-small, mistral-medium, mistral-large

### Codestral
- Check available models in their documentation

### Lamini
- Check available models in their documentation

## 4. Fix Rate Limiting Issues

- Implement exponential backoff for rate-limited providers
- Add delays between requests to avoid hitting rate limits
- Consider upgrading API tiers for frequently used providers

## 5. Update Provider Order

Modify the provider order in config.txt to prioritize the most reliable providers:

```
PROVIDER_ORDER=cohere,mistral,openrouter,cerebras,codestral,lamini
```

## 6. Check for API Changes

- Review recent documentation for each provider
- Check if any endpoints or parameters have changed
- Update code if necessary to match current API specifications

## 7. Implement Better Error Handling

- Add more specific error handling for each provider
- Log detailed error information
- Implement automatic retries with backoff for transient errors

## 8. Test with Minimal Configuration

Create a minimal config with just one or two providers to isolate issues:

```
API_ID=your_telegram_api_id
API_HASH=your_telegram_api_hash
GROUP_IDS=-1002572672309
PROVIDER_ORDER=cohere,mistral
COHERE_API_KEY=your_cohere_api_key
COHERE_MODEL=command-r
MISTRAL_API_KEY=your_mistral_api_key
MISTRAL_MODEL=mistral-small-latest
```

## 9. Check Network Connectivity

- Verify network connectivity to each provider's API endpoints
- Check if any firewalls or proxies might be blocking requests
- Test from a different network if possible

## 10. Review Recent Code Changes

- Check if any recent code changes might have affected provider functionality
- Verify that the logging changes haven't introduced any bugs
- Test with a previous working version to isolate issues
