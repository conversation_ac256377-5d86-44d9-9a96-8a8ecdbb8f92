import asyncio
import aiohttp
import os
import traceback

# Configuration
API_KEY = "51Vq48DlD8kgAOrPpMHEFTyjdCe5P8uOBVLUAZSZ"
BASE_URL = "https://api.cohere.ai/v1"
CHAT_ENDPOINT = "/chat"
URL = BASE_URL + CHAT_ENDPOINT
DELAY_SECONDS = 20  # Adjust as needed
TEST_MESSAGE = "say an number between 1 and 10, say nothing more!"

headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Authorization": f"Bearer {API_KEY}"
}

# Cohere models to test (from user's feedback)
COHERE_MODELS = [
    "c4ai-aya-expanse-32b",
    "c4ai-aya-vision-32b",
    "command-a-03-2025",
    "command-light-nightly",
    "command-nightly",
    "command-r",
    "command-r-plus",
    "command-r7b-12-2024"
]

async def test_model(session, model_name):
    try:
        payload = {
            "model": model_name,
            "message": TEST_MESSAGE,
        }
        async with session.post(URL, json=payload, headers=headers) as response:
            response.raise_for_status()
            data = await response.json()
            if 'text' in data:
                print(f"  Response from {model_name}: {data['text']}")
                return True
            else:
                print(f"  No response from {model_name}: {data}")
                return False
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False

async def main():
    async with aiohttp.ClientSession() as session:
        for model in COHERE_MODELS:
            print(f"\nTesting model: {model}")
            success = await test_model(session, model)
            print(f"  {model} is {'accessible' if success else 'NOT accessible'}")
            await asyncio.sleep(DELAY_SECONDS)

if __name__ == "__main__":
    asyncio.run(main())
