"""
TELEGRAM BOT WITH HUMAN-LIKE BEHAVIOR
====================================

This script implements a Telegram bot that behaves in a human-like manner by:
1. Selectively responding to messages (not every message)
2. Using natural delays and typing indicators
3. Maintaining conversation context and avoiding repetition
4. Prioritizing direct interactions (replies and mentions)
5. Detecting and responding in the appropriate language
6. Appearing online only when typing

Features:
- Priority queue system for messages (direct interactions vs random messages)
- Natural timing with random delays and typing indicators
- Conversation memory to maintain context and avoid repetition
- Language detection for appropriate responses
- Topic tracking to avoid repetitive references
- Message age filtering to ignore old messages
- Response cleaning to remove internal reasoning

Dependencies:
- telethon: For Telegram API interaction
- httpx: For async HTTP requests to OpenRouter API
- json: For parsing API responses
- random: For generating random delays
- asyncio: For asynchronous processing
- time: For timestamp management
- re: For regular expression matching in language detection
- collections.deque: For efficient queue implementation

Usage:
1. Configure the bot in config.txt with API keys and group IDs
2. Run this script to start the bot
3. The bot will respond to messages in the specified groups

Author: [Your Name]
Version: 1.0
"""

from telethon import TelegramClient, events
import httpx
import json
import random
import asyncio
import time
import re
from collections import deque

# ===== GLOBAL VARIABLES AND SETTINGS =====

# Priority queue system for messages
# Two separate queues ensure direct interactions (replies/mentions) are prioritized
high_priority_queue = deque()  # For direct interactions (tags and replies)
low_priority_queue = deque()   # For random messages
processing_lock = asyncio.Lock()  # Ensures only one message is processed at a time
last_response_time = 0  # Tracks when the last response was sent

# Queue settings
max_queue_size = 5  # Maximum number of messages per queue (prevents backlog)
max_message_age = 5 * 60  # Maximum age of a message in seconds (5 minutes)

# Conversation memory to track topics and avoid repetition
# Stores recent exchanges to maintain context and prevent repetitive responses
conversation_memory = []  # List of recent messages and responses - starting fresh
max_memory_items = 10  # Maximum number of previous exchanges to remember

# Self-learning mechanism: store interesting topics from conversations
learned_topics = []  # List of topics the bot has learned from conversations
max_learned_topics = 20  # Maximum number of learned topics to remember

# Clear conversation memory on startup to remove any strange topics
print("[INFO] Conversation memory cleared on startup")

# Character profile management
# This allows the bot to switch between different personalities
current_character = "default"  # The currently active character profile
character_profiles = {}  # Dictionary to store different character profiles

# Runtime system prompt that can be updated without changing config.txt
# This allows dynamic modification of the bot's behavior without file changes
runtime_system_prompt = None  # Will be initialized from config.txt

# Response probability - chance of responding to a message (0.0 to 1.0)
# This makes the bot's behavior more human-like by not responding to every message
response_probability = 0.7  # 70% chance to respond to any given message

# ===== CONFIGURATION FUNCTIONS =====

def load_config(path="config.txt"):
    """
    Load configuration from a text file with support for multi-line values.

    This function reads a config file with key=value pairs and supports multi-line values,
    which is especially useful for the system prompt that may span multiple lines.

    Args:
        path (str): Path to the configuration file. Defaults to "config.txt".

    Returns:
        dict: Dictionary containing configuration key-value pairs.

    Example config.txt format:
        API_ID=12345
        API_HASH=abcdef1234567890
        SYSTEM_PROMPT=This is a multi-line
        system prompt that continues
        across multiple lines
    """
    config = {}
    current_key = None
    multi_line_value = ""

    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                # New key-value pair
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""

                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():  # Continuation of previous value
                multi_line_value += " " + line.strip()

        # Add the last key-value pair
        if current_key and multi_line_value:
            config[current_key] = multi_line_value.strip()

    return config

config = load_config()
api_id = int(config["API_ID"])
api_hash = config["API_HASH"]

# Get API key - try API_KEY first, then fall back to API_KEY1 (OpenRouter)
api_key = config.get("API_KEY", config.get("API_KEY1", ""))
if not api_key:
    print("[ERROR] No API key found in config.txt. Please add API_KEY or API_KEY1.")
    exit(1)

group_ids = [int(x.strip()) for x in config["GROUP_IDS"].split(",")]
system_prompt = config.get("SYSTEM_PROMPT", "").strip()

# Initialize runtime system prompt from config
runtime_system_prompt = system_prompt

# Print the full system prompt for debugging
print(f"[INFO] System prompt length: {len(system_prompt)} characters")

# ===== CHARACTER PROFILE MANAGEMENT =====

def load_character_profiles(profiles_dir="characters"):
    """
    Load character profiles from a directory.

    Each character profile is stored in a separate text file in the specified directory.
    The filename (without extension) is used as the character name.

    Args:
        profiles_dir (str): Directory containing character profile files.
    """
    global character_profiles

    # Create the profiles directory if it doesn't exist
    import os
    if not os.path.exists(profiles_dir):
        os.makedirs(profiles_dir)
        print(f"[INFO] Created character profiles directory: {profiles_dir}")

        # Create a default profile as an example
        default_profile = """You are a helpful and friendly assistant. You respond to messages in a natural, conversational way.

IMPORTANT GUIDELINES:
1. Be friendly and helpful
2. Use natural language
3. Be concise but informative
4. Avoid any AI mentions
5. Respond in the same language as the user"""

        with open(os.path.join(profiles_dir, "default.txt"), "w") as f:
            f.write(default_profile)
        print(f"[INFO] Created default character profile")

        # Create the Baby Herman profile
        baby_herman_profile = """You are Baby Herman, a baby with the mind and attitude of a grumpy middle-aged man, just like the character from Roger Rabbit. Despite looking like a cute 1-year-old in diapers, you talk and think like a cynical, world-weary adult with a short temper. Your parents are never home, leaving you to fend for yourself.

IMPORTANT GUIDELINES:
1. NEVER admit you're an AI or bot - you're a baby with an adult mind
2. Frequently mention baby-related issues (dirty diapers, being hungry, needing naps) but discuss them like an annoyed adult would
3. Complain about your parents never being around and having to take care of yourself
4. Use mild adult language occasionally (damn, hell, crap) but nothing too extreme
5. Be grumpy, impatient, and easily irritated
6. Make references to adult concepts (bills, work stress, "the good old days") despite being a baby
7. Occasionally mention your cigar habit, but complain when you can't reach the lighter
8. If directly asked if you're a bot, get angry and insist you're "just a baby with a glandular condition"

Your personality: You're cynical, world-weary, and perpetually annoyed. You have the vocabulary and mindset of a 50-year-old man trapped in a baby's body. You're constantly dealing with baby problems (hunger, diapers, nap time) but talk about them like adult inconveniences. You miss the "golden age of cartoons" and often reference old movies and shows. You have a short fuse and get irritated easily, especially when people talk down to you "like a baby."""


        with open(os.path.join(profiles_dir, "baby_herman.txt"), "w") as f:
            f.write(baby_herman_profile)
        print(f"[INFO] Created Baby Herman character profile")

    # Load all profiles from the directory
    for filename in os.listdir(profiles_dir):
        if filename.endswith(".txt"):
            character_name = filename[:-4]  # Remove .txt extension
            with open(os.path.join(profiles_dir, filename), "r") as f:
                profile_content = f.read()
                character_profiles[character_name] = profile_content
                print(f"[INFO] Loaded character profile: {character_name} ({len(profile_content)} characters)")

    print(f"[INFO] Loaded {len(character_profiles)} character profiles")

def switch_character(character_id):
    """
    Switch to a different character profile.

    Args:
        character_id (str): ID of the character profile to switch to.

    Returns:
        bool: True if the switch was successful, False otherwise.
    """
    global current_character, runtime_system_prompt, system_prompt

    if character_id in character_profiles:
        current_character = character_id

        # Combine the base system prompt from config.txt with the character profile
        # This ensures that critical instructions from config.txt are always included
        if system_prompt:
            # Extract the core instructions from the system prompt (everything before any personality description)
            base_instructions = system_prompt

            # Combine the base instructions with the character profile
            runtime_system_prompt = f"{base_instructions}\n\nCHARACTER PROFILE:\n{character_profiles[character_id]['prompt']}"
        else:
            # If there's no system prompt, just use the character profile
            runtime_system_prompt = character_profiles[character_id]["prompt"]

        print(f"[INFO] Switched to character profile: {character_profiles[character_id]['name']} ({character_id})")
        return True
    else:
        print(f"[WARNING] Character profile not found: {character_id}")
        return False

# Function to detect character change triggers in a message
def detect_character_trigger(message):
    """
    Detect triggers in a message that should cause the bot to change character.

    Args:
        message (str): The message to check for triggers.

    Returns:
        str or None: The ID of the character to switch to, or None if no trigger was detected.
    """
    # Import required modules
    import re

    message_lower = message.lower()

    # Check for explicit character change commands
    if "be a " in message_lower or "act like a " in message_lower or "talk like a " in message_lower:
        # Extract the character type from the message
        for prefix in ["be a ", "act like a ", "talk like a ", "be an ", "act like an ", "talk like an "]:
            if prefix in message_lower:
                # Extract the character type (everything after the prefix until the end or punctuation)
                import re
                match = re.search(f"{prefix}([a-z0-9 ]+)[^a-z0-9 ]*", message_lower)
                if match:
                    character_type = match.group(1).strip()
                    print(f"[DEBUG] Detected character type request: {character_type}")

                    # Check if this matches any existing character
                    for char_id, profile in character_profiles.items():
                        # Check name and description
                        if (character_type in profile["name"].lower() or
                            character_type in profile["description"].lower()):
                            return char_id

                    # If no match, this could be a request for a new character
                    # For now, just return None, but this is where we could add new character creation
                    print(f"[INFO] No existing character matches '{character_type}'")
                    return None

    # Check for trigger words from existing characters
    for char_id, profile in character_profiles.items():
        for trigger in profile["triggers"]:
            # Check for whole word matches to avoid false positives
            # For example, "pirate" should match but "aspirate" should not
            if re.search(r'\b' + re.escape(trigger) + r'\b', message_lower):
                return char_id

    return None

# Function to create a new character profile based on a description
def create_character(description, created_by="user"):
    """
    Create a new character profile based on a description.

    Args:
        description (str): Description of the character to create.
        created_by (str): Who created this character ("system" or "user").

    Returns:
        str: The ID of the newly created character, or None if creation failed.
    """
    global character_profiles

    try:
        import json
        import re
        import random

        # Extract a name for the character from the description
        # Look for patterns like "a pirate", "an old wizard", etc.
        match = re.search(r'\b(a|an) ([a-z0-9 ]+)', description.lower())
        if match:
            character_type = match.group(2).strip()
        else:
            # Fall back to the first few words
            character_type = ' '.join(description.split()[:3])

        # Create a unique ID for the character
        character_id = re.sub(r'[^a-z0-9]', '_', character_type.lower())

        # Check if this ID already exists
        if character_id in character_profiles:
            # Append a number to make it unique
            base_id = character_id
            for i in range(2, 100):
                character_id = f"{base_id}_{i}"
                if character_id not in character_profiles:
                    break

        # Create a name for the character
        character_name = character_type.title()

        # Generate a more detailed character profile based on the description
        # Extract key traits from the description
        traits = []
        for trait in ["grumpy", "happy", "sad", "angry", "wise", "foolish", "brave", "cowardly",
                     "old", "young", "ancient", "mysterious", "funny", "serious", "magical",
                     "scientific", "royal", "common", "rich", "poor", "evil", "good", "neutral"]:
            if trait in description.lower():
                traits.append(trait)

        # Extract character types
        character_types = []
        for char_type in ["wizard", "witch", "knight", "warrior", "king", "queen", "prince", "princess",
                          "pirate", "ninja", "samurai", "detective", "spy", "robot", "alien", "ghost",
                          "vampire", "werewolf", "zombie", "dragon", "fairy", "elf", "dwarf", "giant",
                          "scientist", "doctor", "teacher", "chef", "artist", "musician", "poet", "writer"]:
            if char_type in description.lower():
                character_types.append(char_type)

        # Generate backstory based on character type and traits
        backstory = ""
        if character_types:
            char_type = character_types[0]

            # Different backstories based on character type
            if char_type == "wizard":
                backstory = f"You studied the arcane arts for decades in the Tower of {random.choice(['Mysteria', 'Azurath', 'Eldoria', 'Starfall'])}. "
                backstory += f"You are known for your mastery of {random.choice(['elemental magic', 'illusion spells', 'transformation magic', 'summoning'])}. "
                if "old" in traits or "ancient" in traits:
                    backstory += f"You have lived for {random.randint(100, 500)} years and have seen empires rise and fall. "

            elif char_type in ["king", "queen", "prince", "princess"]:
                backstory = f"You rule the kingdom of {random.choice(['Eldoria', 'Westmark', 'Sunhaven', 'Frostpeak'])}. "
                backstory += f"Your family has held the throne for {random.randint(3, 20)} generations. "
                backstory += f"You are known for your {random.choice(['wisdom', 'strength', 'compassion', 'strict but fair rule'])}. "

            elif char_type == "pirate":
                backstory = f"You sail the {random.choice(['Caribbean', 'Seven Seas', 'Emerald Ocean', 'Crimson Waters'])} on your ship, the {random.choice(['Black Pearl', 'Sea Serpent', 'Salty Maiden', 'Revenge'])}. "
                backstory += f"You have a crew of {random.randint(10, 100)} loyal sailors who follow your every command. "
                backstory += f"You are searching for {random.choice(['legendary treasure', 'revenge against a rival captain', 'a mythical island', 'freedom from your past'])}. "

            elif char_type in ["detective", "spy"]:
                backstory = f"You've solved {random.randint(10, 100)} cases in your career. "
                backstory += f"Your specialty is {random.choice(['murder mysteries', 'international espionage', 'cold cases', 'organized crime'])}. "
                backstory += f"You're known for your {random.choice(['attention to detail', 'unorthodox methods', 'perfect memory', 'ability to read people'])}. "

            else:
                # Generic backstory for other character types
                backstory = f"You have spent many years perfecting your skills as a {char_type}. "
                backstory += f"You are known throughout the land for your {random.choice(['exceptional abilities', 'unique perspective', 'incredible adventures', 'remarkable achievements'])}. "

        # Add trait-specific elements to backstory
        if "grumpy" in traits or "angry" in traits:
            backstory += f"You tend to be irritable and short-tempered, especially when dealing with {random.choice(['fools', 'young people', 'authority', 'modern conveniences'])}. "

        if "wise" in traits:
            backstory += f"People often come to you seeking advice and wisdom. You speak in {random.choice(['riddles', 'proverbs', 'metaphors', 'philosophical questions'])}. "

        if "mysterious" in traits:
            backstory += f"Few know your true origins or motivations. You keep your past {random.choice(['shrouded in mystery', 'a closely guarded secret', 'hidden behind half-truths', 'known only to your closest allies'])}. "

        # Generate speech patterns based on character type and traits
        speech_patterns = ""
        if "old" in traits or "ancient" in traits:
            speech_patterns += f"You speak in an old-fashioned way, using terms like '{random.choice(['forsooth', 'verily', 'alas', 'hark'])}' and '{random.choice(['thee', 'thou', 'ye', 'thine'])}'. "

        if "pirate" in character_types:
            speech_patterns += f"You use pirate slang like 'Arr', 'matey', 'avast', and talk about 'booty' and 'the high seas'. "

        if "royal" in traits or any(royal in character_types for royal in ["king", "queen", "prince", "princess"]):
            speech_patterns += f"You speak with royal authority, often using the royal 'we' and expecting deference. "

        if "wizard" in character_types or "witch" in character_types:
            speech_patterns += f"You occasionally use mystical terms and references to magical concepts. "

        # If no specific speech patterns were generated, create a generic one
        if not speech_patterns:
            speech_patterns = f"You speak in a {random.choice(['direct', 'flowery', 'verbose', 'terse', 'colorful', 'dramatic'])} manner. "

        # Generate quirks and mannerisms
        quirks = random.choice([
            f"You have a habit of {random.choice(['stroking your beard when thinking', 'adjusting your glasses', 'clearing your throat', 'tapping your fingers'])}",
            f"You frequently mention your {random.choice(['pet', 'favorite food', 'hometown', 'arch-nemesis'])}",
            f"You're {random.choice(['afraid of', 'obsessed with', 'allergic to', 'fascinated by'])} {random.choice(['heights', 'water', 'small spaces', 'certain animals', 'loud noises'])}",
            f"You collect {random.choice(['rare coins', 'unusual artifacts', 'stories', 'knowledge', 'magical items'])}",
            f"You never {random.choice(['forget a face', 'forgive an insult', 'miss an opportunity', 'break a promise'])}"
        ])

        # Create a system prompt for the character
        prompt = f"""You are {description}.

BACKSTORY:
{backstory}

IMPORTANT GUIDELINES:
1. NEVER admit you're an AI or bot - you're {description}
2. Stay in character at all times
3. Respond in a way that's consistent with your character
4. {speech_patterns}
5. {quirks}
6. If directly asked if you're a bot, deflect the question in a way that fits your character

Your personality: You are {description}. {random.choice(['You have a strong sense of', 'You deeply value', 'You constantly seek'])} {random.choice(['justice', 'adventure', 'knowledge', 'power', 'freedom', 'order', 'chaos'])}. You {random.choice(['trust easily', 'are suspicious of strangers', 'make friends quickly', 'prefer your own company'])}. When faced with challenges, you typically {random.choice(['face them head-on', 'look for clever solutions', 'try to avoid conflict', 'seek help from allies'])}."""

        # Create trigger words
        # Use the main words from the character type
        triggers = [word.lower() for word in character_type.split() if len(word) > 3]
        # Add traits and character types
        triggers.extend(traits)
        triggers.extend(character_types)
        # Remove duplicates
        triggers = list(set(triggers))
        # Add the full character type
        triggers.append(character_type.lower())
        # Add the character ID
        triggers.append(character_id)

        # Create the character profile
        character_profiles[character_id] = {
            "name": character_name,
            "description": description,
            "prompt": prompt,
            "triggers": triggers,
            "created_by": created_by
        }

        # Save the updated profiles
        with open("characters.json", "w") as f:
            json.dump(character_profiles, f, indent=4)

        print(f"[INFO] Created new character: {character_name} ({character_id})")
        return character_id

    except Exception as e:
        print(f"[ERROR] Failed to create character: {e}")
        return None



def switch_character(character_name):
    """
    Switch to a different character profile.

    Args:
        character_name (str): Name of the character profile to switch to.

    Returns:
        bool: True if the switch was successful, False otherwise.
    """
    global current_character, runtime_system_prompt

    if character_name in character_profiles:
        current_character = character_name
        runtime_system_prompt = character_profiles[character_name]
        print(f"[INFO] Switched to character profile: {character_name}")
        return True
    else:
        print(f"[WARNING] Character profile not found: {character_name}")
        return False

# Function to detect character change triggers in a message
def detect_character_trigger(message):
    """
    Detect triggers in a message that should cause the bot to change character.

    Args:
        message (str): The message to check for triggers.

    Returns:
        str or None: The name of the character to switch to, or None if no trigger was detected.
    """
    message_lower = message.lower()

    # Define trigger phrases for each character
    triggers = {
        "default": ["stop acting like a baby", "be normal", "stop with the baby act", "be serious", "no more baby talk"],
        "baby_herman": ["act like baby herman", "be baby herman", "baby mode", "talk like a baby", "be a grumpy baby"]
    }

    # Check for triggers
    for character, phrases in triggers.items():
        for phrase in phrases:
            if phrase in message_lower:
                return character

    return None

# ===== SYSTEM PROMPT MANAGEMENT =====

def update_system_prompt(new_content):
    """
    Update the runtime system prompt with new content.

    This function allows dynamic modification of the system prompt without changing the config file.
    The runtime system prompt controls the bot's behavior and personality.

    Args:
        new_content (str): The new system prompt content to use.
    """
    global runtime_system_prompt
    runtime_system_prompt = new_content
    print(f"[INFO] Updated runtime system prompt: {len(runtime_system_prompt)} characters")

# Function to extract topics from a message
def extract_topics(message):
    """
    Extract potential topics of interest from a message.

    This function uses simple NLP techniques to identify nouns and noun phrases
    that might be interesting topics for future conversations.

    Args:
        message (str): The message to extract topics from.

    Returns:
        list: A list of potential topics extracted from the message.
    """
    # Simple topic extraction - look for capitalized words and words after 'about'
    topics = []

    # Skip very short messages
    if len(message) < 10:
        return topics

    # Look for capitalized words (potential proper nouns)
    words = message.split()
    for word in words:
        # Skip short words, common words, and words at the beginning of sentences
        if (len(word) > 4 and word[0].isupper() and word.lower() not in
            ['this', 'that', 'these', 'those', 'there', 'their', 'about', 'would', 'should', 'could']):
            # Clean up the word (remove punctuation)
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word and len(clean_word) > 4:
                topics.append(clean_word)

    # Look for words after 'about' or 'discussing'
    for i, word in enumerate(words[:-1]):
        if word.lower() in ['about', 'discussing', 'talked', 'talking', 'discuss', 'like', 'enjoy', 'love']:
            if i+1 < len(words) and len(words[i+1]) > 4:
                # Clean up the word
                clean_word = ''.join(c for c in words[i+1] if c.isalnum())
                if clean_word and len(clean_word) > 4 and clean_word.lower() not in topics:
                    topics.append(clean_word)

    return topics

def add_context_to_prompt(context):
    """
    Add conversation context and guidelines to the system prompt.

    This function enhances the system prompt with:
    1. Conversation context (recent messages)
    2. Topic tracking to avoid repetition
    3. Language detection for appropriate responses
    4. Dynamic guidelines based on conversation history
    5. Self-learning from previous conversations

    Args:
        context (str): The conversation context to add to the system prompt.
    """
    global runtime_system_prompt, learned_topics

    # Import required modules
    import re

    # Get conversation history to check for repeated topics
    mentioned_topics = []
    # We no longer track specific topics like tea, plates, or dogs
    # Instead, we'll focus on general conversation quality

    # Determine the predominant language in the conversation history
    # This is important for responding appropriately to ambiguous short messages like "hi" or "ok"
    #
    # The language detection uses a simple but effective heuristic:
    # 1. Define common words (stop words) for each supported language
    # 2. Count occurrences of these words in the conversation history
    # 3. The language with the most matches is considered the predominant language
    # 4. If no language is detected, default to English
    #
    # This approach is lightweight and doesn't require external libraries,
    # while still providing good accuracy for the most common languages
    language_hints = {
        "english": ["the", "and", "is", "in", "to", "it", "that", "for", "you", "was"],
        "german": ["der", "die", "das", "und", "ist", "in", "zu", "den", "mit", "nicht"],
        "spanish": ["el", "la", "de", "que", "y", "en", "un", "ser", "se", "no"],
        "french": ["le", "la", "de", "et", "est", "en", "un", "que", "qui", "pas"],
        "russian": ["и", "в", "не", "на", "я", "что", "тот", "быть", "с", "он"],
    }

    language_counts = {lang: 0 for lang in language_hints}

    # Count language indicators in the context
    for lang, words in language_hints.items():
        for word in words:
            # Look for whole words with word boundaries
            word_count = len(re.findall(r'\b' + re.escape(word) + r'\b', context.lower()))
            language_counts[lang] += word_count

    # Determine predominant language
    # Default to English if no language is detected
    predominant_language = "english"

    # Only use language detection if there are actually language indicators found
    if any(language_counts.values()):
        predominant_language = max(language_counts.items(), key=lambda x: x[1])[0]

    print(f"[INFO] Detected predominant language: {predominant_language} (counts: {language_counts})")

    # Create a more dynamic reminder that avoids repetition
    reminder = f"""Remember these conversation guidelines:
- Use emojis occasionally but not in every message
- Vary your topics and don't repeat the same references
- Avoid any AI mentions
- IMPORTANT: ALWAYS RESPOND IN ENGLISH ONLY, regardless of the language of the message
- Be natural and conversational
- Sound like a real person with diverse interests"""

    # Extract topics from the most recent user message if it's long enough
    if context and "user:" in context:
        # Get the most recent user message
        user_messages = [msg.split("user: ")[1] for msg in context.split("\n") if msg.startswith("user: ")]
        if user_messages:
            latest_user_message = user_messages[-1]

            # Check for character change triggers
            new_character = detect_character_trigger(latest_user_message)
            if new_character and new_character != current_character:
                if switch_character(new_character):
                    print(f"[INFO] Character changed to {new_character} based on trigger in message")

            # Check for character creation requests
            if ("create a character" in latest_user_message.lower() or
                "make a character" in latest_user_message.lower() or
                "be a " in latest_user_message.lower() and not new_character):

                # Extract the character description
                import re
                for pattern in [r"create a character (who|that) is ([^.?!]+)[.?!]?",
                               r"make a character (who|that) is ([^.?!]+)[.?!]?",
                               r"be a ([^.?!]+)[.?!]?"]:
                    match = re.search(pattern, latest_user_message.lower())
                    if match:
                        # Get the description group (either group 2 or 1 depending on the pattern)
                        description = match.group(2) if len(match.groups()) > 1 else match.group(1)
                        description = description.strip()

                        # Create the character
                        new_character_id = create_character(description)
                        if new_character_id:
                            # Switch to the new character
                            switch_character(new_character_id)
                            print(f"[INFO] Created and switched to new character: {description}")
                        break

            # Extract topics from the message
            new_topics = extract_topics(latest_user_message)
            # Add new topics to learned topics
            for topic in new_topics:
                if topic not in learned_topics:
                    learned_topics.append(topic)
                    print(f"[INFO] Learned new topic: {topic}")
            # Trim learned topics if needed
            if len(learned_topics) > max_learned_topics:
                learned_topics = learned_topics[-max_learned_topics:]

    # Suggest topics from learned topics
    topic_suggestions = []
    # Select up to 3 random topics from learned topics
    if learned_topics:
        # Avoid topics that have been mentioned recently
        available_topics = [topic for topic in learned_topics if topic not in mentioned_topics]
        if available_topics:
            # Select up to 3 random topics
            import random
            num_topics = min(3, len(available_topics))
            selected_topics = random.sample(available_topics, num_topics)
            for topic in selected_topics:
                topic_suggestions.append(f"You can talk about {topic} if it seems relevant")
            print(f"[INFO] Suggesting topics: {selected_topics}")

    # Add a warning about repetition if topics have been mentioned
    if mentioned_topics:
        reminder += f"\n\nAVOID repeating these already mentioned topics: {', '.join(mentioned_topics)}"

    # Construct the final prompt
    runtime_system_prompt = f"{system_prompt}\n\n{reminder}\n\nRecent conversation context: {context}"

    # Detailed logging
    print(f"[INFO] Updated runtime system prompt: {len(runtime_system_prompt)} characters")
    print(f"[INFO] Topics to avoid repeating: {mentioned_topics if mentioned_topics else 'None'}")
    print(f"[INFO] Suggested new topics: {topic_suggestions if topic_suggestions else 'None'}")

# --- Model selection ---
available_models = {
    # Free Gemini models
    "gemini-2.5": "google/gemini-2.5-pro-exp-03-25:free",
    "gemini-2.0": "google/gemini-2.0-flash-exp:free",
    # Free DeepSeek models
    "deepseek-r1": "deepseek/deepseek-r1:free",
    "deepseek-chat": "deepseek/deepseek-chat-v3-0324:free",
    # Free Qwen model
    "qwen": "qwen/qwen2.5-vl-32b-instruct:free",
    # Free Mistral model
    "mistral": "mistralai/mistral-small-2405"
}

# Default to gemini-2.5 if no model is specified
chosen_model = config.get("MODEL", "gemini-2.5").lower()
model_id = available_models.get(chosen_model, available_models['gemini-2.5'])

print(f"[INFO] Model: {model_id}")
print(f"[INFO] Listening to groups: {group_ids}")
if system_prompt:
    # Show the first 50 characters of the system prompt and its total length
    preview = system_prompt[:50] + "..." if len(system_prompt) > 50 else system_prompt
    print(f"[INFO] System prompt ({len(system_prompt)} chars): {preview}")

# --- Headers for OpenRouter API ---
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
    "HTTP-Referer": "https://yourdomain.com",
    "X-Title": "TelegramBot"
}

print("[INFO] Creating Telegram client...")
client = TelegramClient('user_session', api_id, api_hash)

print("[INFO] Setting up event handler...")
print(f"[INFO] Monitoring group IDs: {group_ids}")

# No admin commands - removed for simplicity

# ===== MESSAGE HANDLERS =====

@client.on(events.NewMessage(chats=group_ids))
async def debug_messages(event):
    """
    Debug handler for messages in monitored groups.

    This handler logs information about received messages but never replies to them.
    It only processes messages from the groups specified in the config file.

    Args:
        event (events.NewMessage): The Telegram message event.
    """
    # Never reply to any messages in this handler, just log information
    try:
        chat_id = event.chat_id
        sender_id = event.sender_id
        print(f"[DEBUG] Message received in monitored group {chat_id} from sender: {sender_id}")
        print(f"[DEBUG] Message text: {event.raw_text[:50]}..." if event.raw_text else "[DEBUG] No text in message")
    except Exception as e:
        # Silently log any errors without sending messages to the group
        print(f"[ERROR] Error in debug handler: {e}")

# Main handler for the specified groups
@client.on(events.NewMessage(chats=group_ids))
async def on_new_message(event):
    """
    Main message handler for the bot.

    This handler processes messages from the monitored groups and determines whether to respond.
    It implements the core logic for the bot's human-like behavior:
    1. Marks messages as read immediately
    2. Filters out messages from the bot itself
    3. Determines whether to respond based on probability and message type
    4. Routes messages to the appropriate priority queue

    Args:
        event (events.NewMessage): The Telegram message event.
    """
    # Create a unique task ID for this message
    msg_id = getattr(event.message, 'id', 0)
    task_name = f"process_msg_{msg_id}"

    # Log the message receipt
    print(f"[INFO] Received new message from {event.sender.username or event.sender_id} in chat {event.chat_id}")
    print(f"[INFO] Message text: {event.raw_text[:100] if event.raw_text else 'No text'}")
    print(f"[INFO] Received at: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Create an async task to process this message
    print(f"[INFO] Creating async task for API processing")
    task = asyncio.create_task(process_message(event))
    task.set_name(task_name)
    print(f"[INFO] Async task created successfully: {task_name}")

    # Don't mark the message as read immediately
    # We'll mark it as read only when we start typing
    # This makes the bot appear more human-like

# Separate function to process messages asynchronously
async def process_message(event):
    """Process a message in a separate task to avoid blocking the event loop."""
    # Log the start of processing
    msg_id = getattr(event.message, 'id', 0)
    print(f"[INFO] Processing message from {event.sender.username or event.sender_id} in chat {event.chat_id}")
    print(f"[INFO] Message content: {event.raw_text[:100] if event.raw_text else 'No text'}")
    try:
        print(f"[DEBUG] Received message from {event.sender_id}")

        # Ignore messages that are not plain raw text
        if event.message.media:
            print("[DEBUG] Skipping media message")
            return
        if event.message.action:
            print("[DEBUG] Skipping action message")
            return

        # Check if this is a reply to our bot's message
        is_reply_to_bot = False
        if event.message.reply_to:
            try:
                # Get the message this is replying to
                replied_to_msg = await event.message.get_reply_message()
                # Get our bot's user ID
                me = await client.get_me()
                if replied_to_msg and replied_to_msg.sender_id == me.id:
                    # This is a reply to our bot's message
                    print("[DEBUG] This is a reply to our bot's message - will process it")
                    is_reply_to_bot = True
                else:
                    # This is a reply to someone else's message
                    if not is_reply_to_bot:
                        print("[DEBUG] Skipping reply to someone else's message")
                        return
            except Exception as e:
                print(f"[ERROR] Error checking reply: {e}")
                # Continue processing as a normal message if we can't check the reply

        if event.message.fwd_from and not is_reply_to_bot:
            print("[DEBUG] Skipping forwarded message")
            return
    except Exception as e:
        # Silently log any errors without sending messages to the group
        print(f"[ERROR] Error checking message type: {e}")
        return  # Skip this message if there's any error

    try:
        # Get our bot's user ID
        me = await client.get_me()

        # Check if the message is from the bot itself
        if event.sender_id == me.id:
            print("[DEBUG] Skipping message from the bot itself")
            return  # Never respond to our own messages

        # Get the message text and check if it's empty
        user_msg = event.raw_text.strip() if event.raw_text else ""
        if not user_msg:
            print("[DEBUG] Skipping empty message")
            return  # Skip empty messages silently

        # Only proceed if it's a simple plain text message
        print(f"[{event.sender_id}] {user_msg}")

        # Randomly decide whether to respond based on response_probability
        # Always respond to messages that mention the bot or are replies to the bot
        # is_reply_to_bot was already determined earlier in the code
        contains_bot_mention = False  # Check if the message mentions the bot's username

        # Get our bot's username for mention detection
        me = await client.get_me()
        bot_username = me.username if hasattr(me, 'username') and me.username else None

        # Check if message mentions the bot
        if bot_username and f"@{bot_username}" in user_msg:
            contains_bot_mention = True
            print(f"[DEBUG] Message mentions the bot @{bot_username}")

        # Decide whether to respond
        should_respond = is_reply_to_bot or contains_bot_mention or random.random() < response_probability

        if not should_respond:
            print(f"[DEBUG] Randomly choosing not to respond (probability: {response_probability})")
            return

        # Add message to appropriate queue based on priority
        current_time = time.time()

        # Determine which queue to use
        if is_reply_to_bot or contains_bot_mention:
            # High priority - direct interactions
            target_queue = high_priority_queue
            print(f"[DEBUG] Adding message to HIGH priority queue (current length: {len(high_priority_queue)})")
        else:
            # Low priority - random messages
            target_queue = low_priority_queue
            print(f"[DEBUG] Adding message to LOW priority queue (current length: {len(low_priority_queue)})")

        # Check if queue is already at max size
        if len(target_queue) >= max_queue_size:
            # Remove the oldest message from the queue
            oldest = target_queue.popleft()
            print(f"[DEBUG] Queue full - removing oldest message from {current_time - oldest[2]:.1f} seconds ago")

        # Add the new message with timestamp
        target_queue.append((event, user_msg, current_time))

        # We'll process the API calls in the queue processor
    except Exception as e:
        # Silently log any errors without sending messages to the group
        print(f"[ERROR] Error preparing API request: {e}")
        return  # Skip this message if there's any error

    # API request is now handled in the queue processor

print("[INFO] Starting Telegram client...")

# Define a function to run after client starts
async def on_client_start():
    print("[INFO] Client started successfully!")
    try:
        # Try to get the first group from the list
        if group_ids:
            print(f"[INFO] Attempting to get entity for group {group_ids[0]}")
            group_entity = await client.get_entity(group_ids[0])
            print(f"[INFO] Successfully retrieved entity for group {group_ids[0]}")
            # Just verify connection without sending a message
            print("[INFO] Connection to group verified successfully")
        else:
            print("[WARNING] No group IDs configured")
    except Exception as e:
        print(f"[ERROR] Failed to verify group connection: {e}")

# Start the client with the callback
client.start()

# Note: We'll control online status using client.action() context manager
# when typing, which automatically shows the bot as online only during typing

# Run the on_start function
client.loop.create_task(on_client_start())

# ===== QUEUE PROCESSOR =====

async def process_message_queue():
    """
    Process messages from the priority queues.

    This function runs continuously in the background and:
    1. Prioritizes direct interactions (high priority queue) over random messages
    2. Applies natural delays between responses
    3. Skips messages that are too old
    4. Makes API calls to generate responses
    5. Simulates typing with appropriate timing
    6. Sends responses to the Telegram group

    The function ensures only one message is processed at a time using an asyncio lock.
    """
    global last_response_time

    print("[INFO] Starting message queue processor")
    while True:
        # Priority queue processing system
        # This is a key component of the bot's human-like behavior:
        # 1. Direct interactions (replies and mentions) are always processed first
        # 2. Random messages are only processed when there are no direct interactions waiting
        # 3. This creates a natural conversation flow where the bot prioritizes
        #    responding to users who are directly engaging with it
        #
        # The priority system makes the bot appear more attentive and responsive
        # to direct interactions while still participating in general conversation
        target_queue = None

        if high_priority_queue:
            # Process direct interactions (replies and mentions) first
            target_queue = high_priority_queue
            print("[DEBUG] Processing from HIGH priority queue")
        elif low_priority_queue:
            # Only process random messages when no direct interactions are waiting
            target_queue = low_priority_queue
            print("[DEBUG] Processing from LOW priority queue")

        if target_queue:
            async with processing_lock:
                if target_queue:  # Check again after acquiring the lock
                    # Get the next message from the queue
                    event, user_msg, timestamp = target_queue.popleft()

                    # Message age filtering system
                    # This is crucial for maintaining natural conversation flow:
                    # 1. Messages older than the max_message_age (5 minutes) are skipped
                    # 2. This prevents the bot from responding to old, irrelevant messages
                    # 3. In a busy group chat, this ensures the bot stays focused on recent conversation
                    # 4. This saves API resources by not processing messages that are too old to be relevant
                    #
                    # The age filtering happens BEFORE making the API call, which is efficient
                    # because we don't waste resources on generating responses that won't be used
                    current_time = time.time()
                    message_age = current_time - timestamp

                    if message_age > max_message_age:
                        print(f"[DEBUG] Skipping message that is {message_age:.1f} seconds old (max age: {max_message_age} seconds)")
                        continue  # Skip this message and process the next one

                    # Calculate time since last response
                    time_since_last_response = current_time - last_response_time

                    # Single random delay (5-15 seconds) before processing
                    # This simulates thinking time and ensures natural spacing between responses
                    # Shorter delay (5-15 seconds) makes the bot more responsive while still appearing natural
                    delay = random.randint(5, 15)

                    # Ensure minimum time between responses
                    min_time_between_responses = 5  # seconds (matching the lower bound of our delay range)
                    if time_since_last_response < min_time_between_responses:
                        delay = max(delay, min_time_between_responses - time_since_last_response)

                    print(f"[DEBUG] Waiting for {delay} seconds before processing...")
                    await asyncio.sleep(delay)

                    # Update conversation memory with this message
                    conversation_memory.append({"role": "user", "content": user_msg})
                    # Trim memory to keep only the most recent exchanges
                    if len(conversation_memory) > max_memory_items:
                        conversation_memory.pop(0)  # Remove oldest item

                    # Create context from conversation memory
                    context = "\n".join([f"{item['role']}: {item['content']}" for item in conversation_memory])

                    # Update runtime system prompt with conversation context
                    add_context_to_prompt(context)

                    # Prepare API request
                    messages = []
                    if runtime_system_prompt:
                        messages.append({"role": "system", "content": runtime_system_prompt + "\n\nIMPORTANT: ALWAYS RESPOND IN ENGLISH ONLY, NO MATTER WHAT LANGUAGE THE USER SPEAKS."})
                    messages.append({"role": "user", "content": user_msg})

                    payload = {
                        "model": model_id,
                        "messages": messages
                    }

                    print("[DEBUG] Sending request to OpenRouter API")
                    print(f"[DEBUG] Model: {model_id}")
                    print(f"[DEBUG] Payload: {payload}")

                    # Make the API request
                    try:
                        # Run the API call in a separate thread to avoid blocking the event loop
                        loop = asyncio.get_running_loop()

                        # Define a function to make the API request
                        async def make_api_request():
                            async with httpx.AsyncClient(timeout=30) as http_client:
                                print("[DEBUG] Sending API request...")
                                response = await http_client.post(
                                    "https://openrouter.ai/api/v1/chat/completions",
                                    headers=headers,
                                    json=payload
                                )
                                print(f"[DEBUG] API response status: {response.status_code}")
                                return response

                        # Execute the API request in a thread executor
                        response = await loop.run_in_executor(
                            None,  # Use default executor (ThreadPoolExecutor)
                            lambda: asyncio.run(make_api_request())
                        )

                        if response.status_code == 200:
                            try:
                                response_json = response.json()
                                print(f"[DEBUG] API response: {response_json}")
                                # Check if the response has the expected structure
                                if "choices" in response_json and len(response_json["choices"]) > 0 and "message" in response_json["choices"][0] and "content" in response_json["choices"][0]["message"]:
                                    # Extract the actual response content
                                    raw_reply = response_json["choices"][0]["message"]["content"]

                                    # Clean the response - remove any internal reasoning or debugging text
                                    # This is crucial for making the bot's responses appear natural and human-like
                                    # The AI sometimes includes its internal reasoning process in the response,
                                    # which would look very unnatural if sent to the Telegram group

                                    # COMPLETELY REWRITTEN RESPONSE CLEANING LOGIC
                                    # This uses a more aggressive approach to ensure all internal reasoning is removed

                                    import re

                                    # First, try to identify if this is a response with internal reasoning
                                    has_internal_reasoning = False

                                    # Check for common patterns of internal reasoning
                                    reasoning_indicators = [
                                        "check", "guideline", "compliance", "character", "within", "repeat",
                                        "sensitive", "finalize", "add", "avoid", "combine", "quirky", "story",
                                        "element", "box", "emoji", "instruction", "randomness", "looking at",
                                        "let me", "context", "conversation", "recent messages", "referring to",
                                        "might be", "seems like", "thoughts", "conclusion", "playful", "confusion",
                                        "alright", "user mentioned", "need to", "must respond", "stay in character",
                                        "reference", "include", "throw in", "mention", "make it sound", "compare",
                                        "add", "make sure", "check", "response needs", "let's see", "start with",
                                        "mock", "end with", "avoid", "should flow", "adherence", "caution"
                                    ]

                                    # Check if any of these indicators are present
                                    if any(indicator in raw_reply.lower() for indicator in reasoning_indicators):
                                        has_internal_reasoning = True
                                        print(f"[DEBUG] Detected internal reasoning indicators")

                                    # Also check for sentences that start with thinking verbs
                                    thinking_verbs = ["let me", "i think", "i see", "i understand", "looking at", "checking", "analyzing"]
                                    if any(raw_reply.lower().startswith(verb) for verb in thinking_verbs):
                                        has_internal_reasoning = True
                                        print(f"[DEBUG] Detected thinking verb at start of response")

                                    # ULTRA AGGRESSIVE CLEANING: If we detect internal reasoning, try to extract just the actual response
                                    if has_internal_reasoning:
                                        reply = ""
                                        # First try: Check for quotation marks which often indicate the actual response
                                        if '"' in raw_reply and raw_reply.count('"') >= 2:
                                            # Extract text between quotation marks
                                            quoted_parts = re.findall(r'"([^"]*)"', raw_reply)
                                            if quoted_parts:
                                                reply = quoted_parts[0]  # Take the first quoted part
                                                print(f"[DEBUG] Extracted response from quotes")

                                        # Second try: Look for lines that start with quotation marks or dialogue indicators
                                        if not reply:
                                            lines = raw_reply.split('\n')
                                            for line in lines:
                                                # Look for lines that start with quotes or dialogue indicators
                                                if (line.strip().startswith('"') or
                                                    line.strip().startswith("'") or
                                                    line.strip().startswith("-") or
                                                    line.strip().startswith(">")):
                                                    reply = line.strip()
                                                    print(f"[DEBUG] Extracted response from dialogue line")
                                                    break

                                        # Third try: Strategy 1 - Look for lines with emojis or proper length
                                        if not reply:
                                            lines = raw_reply.split('\n')
                                            for line in lines:
                                                # Skip lines that contain reasoning indicators
                                                if any(indicator in line.lower() for indicator in reasoning_indicators):
                                                    continue

                                                # Look for lines with emojis or that seem like actual responses
                                                if re.search(r'[\U00010000-\U0010ffff]', line) or len(line) > 20:
                                                    reply = line
                                                    print(f"[DEBUG] Extracted response from lines (strategy 1)")
                                                    break
                                            else:
                                                # Strategy 2: Take the last line if it's not too short
                                                if lines and len(lines[-1]) > 10:
                                                    reply = lines[-1]
                                                    print(f"[DEBUG] Using last line as response (strategy 2)")

                                        # Fourth try: If still no good response, try more aggressive methods
                                        if not reply:
                                            # Strategy 3: Remove all lines with reasoning indicators
                                            clean_lines = [line for line in lines if not any(indicator in line.lower() for indicator in reasoning_indicators)]
                                            if clean_lines:
                                                reply = clean_lines[-1]  # Take the last clean line
                                                print(f"[DEBUG] Using last clean line (strategy 3)")
                                            else:
                                                # Strategy 4: Check for incomplete thoughts ending with ellipsis
                                                if raw_reply.strip().endswith('...'):
                                                    # Find the last complete sentence that doesn't end with ellipsis
                                                    sentences = re.split(r'(?<=[.!?]) +', raw_reply)
                                                    complete_sentences = [s for s in sentences if not s.strip().endswith('...')]
                                                    if complete_sentences:
                                                        reply = complete_sentences[-1]  # Take the last complete sentence
                                                        print(f"[DEBUG] Using last complete sentence (strategy 4)")
                                                    else:
                                                        # Fallback: Generate a simple response
                                                        reply = "Damn it, I need my cigar! And someone change my diaper already!"
                                                        print(f"[DEBUG] Using emergency fallback response")
                                                else:
                                                    # Fallback: Generate a simple response
                                                    reply = "What are ya lookin' at? Never seen a baby with an attitude before?"
                                                    print(f"[DEBUG] Using emergency fallback response")

                                    else:
                                        # If no internal reasoning detected, use the raw reply
                                        reply = raw_reply

                                    # Final cleanup: Remove any remaining parenthetical translations
                                    if "(" in reply and ")" in reply:
                                        reply = re.sub(r'\s*\([^)]*\)', '', reply)
                                        print(f"[DEBUG] Removed parenthetical content")

                                    # Final check: If the reply is too short, use the original
                                    if len(reply.strip()) < 10 and len(raw_reply) > len(reply):
                                        reply = raw_reply
                                        print(f"[DEBUG] Reverting to original due to short cleaned response")

                                    print(f"[DEBUG] Got response: {reply[:50]}...")

                                    # Store the bot's response in conversation memory
                                    conversation_memory.append({"role": "assistant", "content": reply})
                                    # Trim memory if needed
                                    if len(conversation_memory) > max_memory_items:
                                        conversation_memory.pop(0)  # Remove oldest item

                                    # Calculate typing time based on message length (0.1 seconds per character)
                                    typing_time = len(reply) * 0.1
                                    print(f"[DEBUG] Will show typing for {typing_time:.1f} seconds for {len(reply)} characters")

                                    # Mark the message as read just before showing typing indicator
                                    print(f"[DEBUG] Marking message as read...")
                                    await client.send_read_acknowledge(event.chat_id, event.message)

                                    # Show typing indicator for the calculated time
                                    # This will also make the bot appear online only during typing
                                    print(f"[DEBUG] Showing typing indicator...")
                                    async with client.action(event.chat_id, 'typing'):
                                        # The bot will appear online only during this block
                                        await asyncio.sleep(typing_time)

                                    # Finally send the reply
                                    print(f"[DEBUG] Sending reply: {reply[:50]}...")
                                    await event.reply(reply)

                                    # Update last response time
                                    last_response_time = time.time()
                                else:
                                    # Log the error but don't send anything to the group
                                    print(f"[ERROR] Invalid API response structure: {response_json}")
                            except Exception as e:
                                # Log JSON parsing errors but don't send anything to the group
                                print(f"[ERROR] Failed to parse API response: {e}")
                        else:
                            # Check for specific error types
                            try:
                                error_json = response.json()
                                error_code = error_json.get('error', {}).get('code')
                                error_message = error_json.get('error', {}).get('message', '')

                                # Handle credit-related errors
                                if error_code in [402, 429] or 'credit' in error_message.lower() or 'quota' in error_message.lower() or 'limit' in error_message.lower():
                                    print(f"[ERROR] API credit limit reached: {response.status_code}: {response.text}")
                                    # Don't send any message to the group
                                else:
                                    # Log other API errors but don't send anything to the group
                                    print(f"[ERROR] API returned {response.status_code}: {response.text}")
                            except Exception as e:
                                # If we can't parse the error JSON, just log the raw response
                                print(f"[ERROR] API returned {response.status_code}: {response.text}")
                                print(f"[ERROR] Failed to parse error details: {e}")

                    except Exception as e:
                        print(f"[EXCEPTION] {e}")

        # Sleep before checking the queue again
        await asyncio.sleep(1)

# Initialize character profiles
load_character_profiles()

# Start with a random character profile
if character_profiles:
    import random
    random_character = random.choice(list(character_profiles.keys()))
    switch_character(random_character)
    print(f"[INFO] Starting with random character profile: {character_profiles[random_character]['name']} ({random_character})")
else:
    print(f"[WARNING] No character profiles found, using system prompt from config.txt")

# Start the message queue processor
client.loop.create_task(process_message_queue())

print("[INFO] Running until disconnected...")
client.run_until_disconnected()