#!/usr/bin/env python33
"""
🔊 _TTS FOLDER COMPREHENSIVE BATCH TESTER
Systematically tests all Python scripts in the _tts folder
"""

import os
import subprocess
import sys
from pathlib import Path
import time

PIPER_PATH = "/home/<USER>/colestart/piper_production_env/bin/piper"
VOICES_DIR = "/mnt/c/Users/<USER>/Desktop/codestart/piper/voices"
TEMP_DIR = "/tmp/fixed_tts_server"
os.makedirs(TEMP_DIR, exist_ok=True)
ENV_PATH = "/home/<USER>/colestart/piper_production_env"
DEFAULT_VOICE_MODEL = "en_GB-jenny_dioco-medium.onnx"


def find_python_scripts(directory):
    """Find all Python scripts in directory and subdirectories"""
    python_scripts = []
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment directories
        dirs[:] = [d for d in dirs if not d.endswith('_env') and d != '__pycache__']
        
        for file in files:
            if file.endswith('.py'):
                full_path = os.path.join(root, file)
                python_scripts.append(full_path)
    
    return sorted(python_scripts)

def test_script_syntax(script_path):
    """Test if a Python script has valid syntax"""
    try:
        result = subprocess.run(
            ['python3', '-m', 'py_compile', script_path],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0, result.stderr
    except subprocess.TimeoutExpired:
        return False, "Timeout during compilation"
    except Exception as e:
        return False, str(e)

def main():
    print("🎯 _TTS FOLDER COMPREHENSIVE TESTING STARTED")
    print("=" * 60)
    
    # Change to the correct directory
    base_dir = "/home/<USER>/colestart"
    os.chdir(base_dir)
    
    # Find all Python scripts in _tts folder
    tts_dir = "_tts"
    if not os.path.exists(tts_dir):
        print(f"❌ Directory {tts_dir} not found!")
        return
    
    scripts = find_python_scripts(tts_dir)
    total_scripts = len(scripts)
    
    print(f"📊 Found {total_scripts} Python scripts to test")
    print("=" * 60)
    
    # Test results
    valid_scripts = []
    invalid_scripts = []
    
    # Test each script
    for i, script in enumerate(scripts, 1):
        relative_path = os.path.relpath(script, base_dir)
        print(f"[{i}/{total_scripts}] Testing: {relative_path}")
        
        is_valid, error_msg = test_script_syntax(script)
        
        if is_valid:
            print(f"  ✅ Syntax: VALID")
            valid_scripts.append(relative_path)
        else:
            print(f"  ❌ Syntax: INVALID - {error_msg}")
            invalid_scripts.append((relative_path, error_msg))
        
        # Progress update every 10 scripts
        if i % 10 == 0:
            print(f"📊 Progress: {i}/{total_scripts} scripts tested")
            print(f"   ✅ Valid: {len(valid_scripts)}")
            print(f"   ❌ Invalid: {len(invalid_scripts)}")
            print()
    
    # Final results
    print("\n" + "=" * 60)
    print("🎯 _TTS FOLDER TESTING COMPLETED")
    print("=" * 60)
    print(f"📊 Total Scripts: {total_scripts}")
    print(f"✅ Valid Scripts: {len(valid_scripts)} ({len(valid_scripts)/total_scripts*100:.1f}%)")
    print(f"❌ Invalid Scripts: {len(invalid_scripts)} ({len(invalid_scripts)/total_scripts*100:.1f}%)")
    
    # Save detailed results
    results_file = "codebase_analysis_20250125/folder_testing_results/_tts_detailed_results.txt"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    
    with open(results_file, 'w') as f:
        f.write("🔊 _TTS FOLDER DETAILED TESTING RESULTS\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Total Scripts: {total_scripts}\n")
        f.write(f"Valid Scripts: {len(valid_scripts)}\n")
        f.write(f"Invalid Scripts: {len(invalid_scripts)}\n\n")
        
        f.write("✅ VALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script in valid_scripts:
            f.write(f"  {script}\n")
        
        f.write("\n❌ INVALID SCRIPTS:\n")
        f.write("-" * 30 + "\n")
        for script, error in invalid_scripts:
            f.write(f"  {script}\n")
            f.write(f"    Error: {error}\n\n")
    
    print(f"📄 Detailed results saved to: {results_file}")
    
    # Summary by category
    print("\n📊 RESULTS BY CATEGORY:")
    categories = {
        'ultimate_tts': [s for s in valid_scripts if 'ultimate_tts' in s],
        'piper_tts_server': [s for s in valid_scripts if 'piper_tts_server' in s],
        'idiot_tts': [s for s in valid_scripts if 'idiot_tts' in s],
        'piper_script': [s for s in valid_scripts if 'piper_script' in s],
        'piper_source': [s for s in valid_scripts if 'piper/src' in s],
        'other': [s for s in valid_scripts if not any(cat in s for cat in ['ultimate_tts', 'piper_tts_server', 'idiot_tts', 'piper_script', 'piper/src'])]
    }
    
    for category, scripts in categories.items():
        if scripts:
            print(f"  {category}: {len(scripts)} scripts")

if __name__ == "__main__":
    main()
