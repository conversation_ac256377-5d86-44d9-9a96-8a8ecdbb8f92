import asyncio
import traceback
import aiohttp

# Configuration
url = "https://api.fireworks.ai/inference/v1/chat/completions"
API_KEY = "fw_3ZPwRxotc19poR4Pv2cD7tKX" # Replace with your actual API key
TEST_MESSAGE = "Hello, how are you?"
headers = {
  "Accept": "application/json",
  "Content-Type": "application/json",
  "Authorization": f"Bearer {API_KEY}"
}
DELAY_SECONDS = 30  # You can adjust this value

# List of models to test
FIREWORKS_MODELS = {
    "high": [
      "accounts/fireworks/models/qwen2-vl-72b-instruct",
      "accounts/fireworks/models/deepseek-v3",
      "accounts/fireworks/models/llama-v3p1-70b-instruct",
      "accounts/fireworks/models/llama-v3-70b-instruct",
      "accounts/fireworks/models/deepseek-v3-0324",
      "accounts/fireworks/models/deepseek-r1-basic",
      "accounts/fireworks/models/llama-v3p3-70b-instruct",
      "accounts/teo-0b3810/models/evenup-phi-long-prompt",
      "accounts/teo-0b3810/models/evenup-phi-short-prompt",
      "accounts/fireworks/models/deepseek-r1",
      "accounts/yi-01-ai/models/yi-large",
      "accounts/fireworks/models/llama-v3p1-405b-instruct-long",
      "accounts/fireworks/models/llama-v3p1-405b-instruct",
      "accounts/sentientfoundation/models/dobby-unhinged-llama-3-3-70b-new",
      "accounts/fireworks/models/mixtral-8x22b-instruct",
      "accounts/fireworks/models/qwen2p5-72b-instruct"
    ],
    "medium": [
      "accounts/fireworks/models/llama-v3p1-8b-instruct",
      "accounts/fireworks/models/llama-v3p2-90b-vision-instruct",
      "accounts/instacart/models/srl-finetune-0417-debug",
      "accounts/ks2025-c3e5c8/models/al1",
      "accounts/zmd5zer-d5cab1/models/ft-10cf39a6-ebbdd",
      "accounts/fireworks/models/firesearch-ocr-v6",
      "accounts/fireworks/models/llama-guard-3-8b",
      "accounts/perplexity/models/r1-1776"
    ],
    "low": [
      "accounts/fireworks/models/qwq-32b",
      "accounts/fireworks/models/llama4-maverick-instruct-basic",
      "accounts/fireworks/models/qwen2p5-vl-32b-instruct",
      "accounts/fireworks/models/llama4-scout-instruct-basic",
      "accounts/fireworks/models/qwen-qwq-32b-preview",
      "accounts/fireworks/models/phi-3-vision-128k-instruct"
    ]
}

async def test_model(model_name):
    try:
        async with aiohttp.ClientSession() as session:
            payload = {
                "model": model_name,
                "messages": [{"role": "user", "content": TEST_MESSAGE}]
            }
            async with session.post(url, json=payload, headers=headers) as response:
                response.raise_for_status()
                data = await response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    print(f"  Response from {model_name}: {data['choices'][0]['message']['content']}")
                    return True  # Indicate success
                else:
                    print(f"  No choices in response from {model_name}: {data}")
                    return False
    except Exception as e:
        print(f"  Error with {model_name}: {traceback.format_exc()}")
        return False # Indicate failure

async def main():
    print("Testing Fireworks models with AIMLAPI...")
    for priority, models in FIREWORKS_MODELS.items():
        print(f"\nTesting models with {priority} priority:")
        for model_name in models:
            print(f"Testing model: {model_name}")
            success = await test_model(model_name)
            if success:
                print(f"{model_name} is accessible")
            else:
                print(f"{model_name} is not accessible")
            await asyncio.sleep(DELAY_SECONDS)  # Pause for the specified number of seconds

if __name__ == "__main__":
    asyncio.run(main())
