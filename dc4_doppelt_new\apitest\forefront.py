import requests
import json

API_KEY = "sk-WhSJ7ncvx7ApAkCXtOLADfeww4kRvmSs"

models = [
    "notbdq/mistral-turkish-v2",
    "Artples/L-MChat-7b",
    "forefront/Mistral-7B-Instruct-v0.1",
    "mistralai/Mistral-7B-Instruct-v0.1",
    "forefront/Mistral-7B-v0.2-hf",
    "alpindale/Mistral-7B-v0.2-hf",
    "WizardLM/WizardMath-7B-V1.1",
    "forefront/Mistral-7B-claude-chat",
    "Norquinal/Mistral-7B-claude-chat",
    "forefront/high-testing-y-check",
    "forefront/high-testing-y-new",
    "forefront/high-testing-y_new",
    "forefront/high-testing-y",
    "forefront/high-testing-x-1",
    "forefront/test-1234",
    "forefront/test-123",
    "forefront/NeuralBeagle14-7B",
    "mlabonne/NeuralBeagle14-7B",
    "forefront/zephyr-7b-sft-full-SPIN-iter3",
    "UCLA-AGI/zephyr-7b-sft-full-SPIN-iter3",
    "forefront/OpenHermes-2.5-Mistral-7B",
    "forefront/teknium/OpenHermes-2.5-Mistral-7B",
    "minitaur/test",
    "personal/test-2",
    "personal/my-first-model",
    "acme-2/test-2",
    "acme-2/test",
    "forefront/jlkjklj",
    "forefront/test-price-match",
    "forefront/test-evals-2",
    "forefront/test-usage",
    "forefront/test-mmlu-mini",
    "forefront/inference-boost",
    "forefront/translate",
    "forefront/xDAN-L1-Chat-RL-v1-chatml",
    "forefront/Starling-LM-7B-alpha-chatml",
    "forefront/Nous-Hermes-2-Vision-Alpha-chatml",
    "forefront/Loyal-Macaroni-Maid-7B-chatml",
    "forefront/CodeNinja-1.0-OpenChat-7B-chatml",
    "forefront/mistral-ft-optimized-1227-chatml",
    "forefront/Nous-Capybara-7B-V1.9-chatml",
    "forefront/dolphin-2.6-mistral-7b-dpo-chatml",
    "forefront/Mistral-7B-OpenOrca-chatml",
    "forefront/zephyr-7b-beta-chatml",
    "forefront/openchat-3.5-1210-chatml",
    "forefront/Mistral-7B-Instruct-v0.2-chatml",
    "forefront/neural-chat-7b-v3-1-chatml",
    "forefront/MonadGPT-chatml",
    "forefront/Marcoro14-7B-slerp-chatml",
    "forefront/test-6-percent-4-epochs",
    "forefront/test-6-percent",
    "forefront/test-evals-validation",
    "forefront/test",
    "mistralai/Mistral-7B-v0.1",
    "Intel/neural-chat-7b-v3-1",
    "berkeley-nest/Starling-LM-7B-alpha",
    "cognitivecomputations/dolphin-2.6-mistral-7b-dpo",
    "Open-Orca/Mistral-7B-OpenOrca",
    "NousResearch/Nous-Capybara-7B-V1.9",
    "xDAN-AI/xDAN-L1-Chat-RL-v1",
    "openchat/openchat-3.5-1210",
    "NousResearch/Nous-Hermes-2-Vision-Alpha",
    "mistralai/Mistral-7B-Instruct-v0.2",
    "beowolx/CodeNinja-1.0-OpenChat-7B",
    "Pclanglais/MonadGPT",
    "HuggingFaceH4/zephyr-7b-beta",
    "SanjiWatsuki/Loyal-Macaroni-Maid-7B",
    "mlabonne/Marcoro14-7B-slerp",
    "OpenPipe/mistral-ft-optimized-1227"
]

url = "https://api.forefront.ai/v1/chat/completions"  

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

def test_model(model_name):
    payload = {
        "model": model_name,
        "prompt": "Hello, Forefront AI!",
        "max_tokens": 1
    }
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        result = response.json()
        print(f"Test with model '{model_name}': Success!")
        print(f"Response: {result}")
    except requests.exceptions.HTTPError as errh:
        print(f"Error with model '{model_name}': {errh}")
        print(f"Response status code: {response.status_code}")
        print(f"Response body: {response.text}")
    except requests.exceptions.ConnectionError as errc:
        print(f"Error with model '{model_name}': Connection Error: {errc}")
    except requests.exceptions.Timeout as errt:
        print(f"Error with model '{model_name}': Timeout Error: {errt}")
    except requests.exceptions.RequestException as err:
        print(f"Error with model '{model_name}': An unexpected error occurred: {err}")
    except Exception as e:
        print(f"Error with model '{model_name}': An unexpected error occurred: {e}")

if __name__ == "__main__":
    for model in models:
        test_model(model)
