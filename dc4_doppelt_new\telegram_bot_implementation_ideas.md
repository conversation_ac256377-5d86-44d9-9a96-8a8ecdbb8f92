# Telegram Bot Implementation Ideas

## System Prompt Improvements

Add these explicit instructions to the system prompt:

```
IMPORTANT FORMATTING INSTRUCTIONS:
1. DO NOT quote or repeat message numbers (like "Message 1:", "Message 2:") in your responses
2. DO NOT directly quote the numbered messages from the context
3. DO NOT refer to specific message numbers in your response
4. INSTEAD, respond to the overall conversation topic and themes
5. TREAT the numbered messages as context only, not as a format to mimic
6. WRITE your response as a natural, flowing message without numbering or quoting
7. FOCUS on the content and meaning of the messages, not their format or numbering
```

## Context Format Improvements

### Option 1: Metadata-Separated Format

```python
def get_group_context(group_id, last_n=15, include_bot_messages=False):
    # ... existing code ...
    
    # Format the messages with clear separation between metadata and content
    formatted_messages = []
    for i, msg in enumerate(messages, 1):
        # Skip bot messages if needed
        if not include_bot_messages and (msg.get('role') == 'assistant' or msg['username'] == 'Assistant'):
            continue
            
        # Format with clear metadata separation
        text = msg['text']
        formatted_messages.append(f"[METADATA: message_id={i}]\n{text}\n[END_METADATA]")
    
    return "\n".join(formatted_messages)
```

### Option 2: Machine-Readable Format

```python
def get_group_context(group_id, last_n=15, include_bot_messages=False):
    # ... existing code ...
    
    # Format the messages in a way that's clearly for machine parsing
    formatted_messages = []
    for i, msg in enumerate(messages, 1):
        # Skip bot messages if needed
        if not include_bot_messages and (msg.get('role') == 'assistant' or msg['username'] == 'Assistant'):
            continue
            
        # Use XML-like format that won't be mimicked
        text = msg['text'].replace('"', '&quot;')  # Escape quotes
        formatted_messages.append(f'<message id="{i}" content="{text}" />')
    
    return "\n".join(formatted_messages)
```

### Option 3: Topic-Based Format

```python
def get_group_context(group_id, last_n=15, include_bot_messages=False):
    # ... existing code ...
    
    # Combine all messages into a single narrative
    all_text = " ".join([msg['text'] for msg in messages if not (
        not include_bot_messages and (msg.get('role') == 'assistant' or msg['username'] == 'Assistant')
    )])
    
    return f"""
CONVERSATION SUMMARY:
The group has been discussing the following topics:
- {all_text[:100]}...
- ...

The most recent messages are about: {all_text[-200:]}
"""
```

## Frequency Response Improvements

For the word frequency trigger responses, modify the prompt to be more explicit:

```python
prompt = f"""You are participating in a group chat conversation. The word "{trigger_word}" has been mentioned several times.

IMPORTANT - DO NOT QUOTE OR NUMBER YOUR RESPONSE LIKE THE CONTEXT BELOW:

Recent messages containing "{trigger_word}":
{trigger_context}

Additional conversation context:
{broader_context}

Join this conversation naturally with a single, flowing response. Do not use message numbers or quotes.
"""
```

## Special Context Improvements

For replied-to messages, make the format less likely to be mimicked:

```python
special_context = f"""
<context type="reply">
<original_message>{replied_to_message_content}</original_message>
<reply>{user_msg}</reply>
</context>

INSTRUCTIONS: Someone replied to your previous message. Respond naturally to their reply without quoting or numbering your response.
"""
```

## Implementation Priority

1. Start with the system prompt improvements - this is the quickest fix
2. If that doesn't work, try the context format improvements
3. If issues persist, implement the special context improvements
4. As a last resort, try the topic-based format
