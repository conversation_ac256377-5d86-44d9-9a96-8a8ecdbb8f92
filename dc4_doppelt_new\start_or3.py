"""
Bot Launcher for or3.py with Provider Selection

This script allows the user to select which API provider to use
without modifying the config.txt file. It then launches or3.py.

Usage:
    python start_or3.py
"""

import os
import sys
import subprocess

# Provider names for display
PROVIDER_NAMES = {
    "1": "OpenRouter",
    "2": "DeepSeek",
    "3": "Google",
    "4": "Hugging Face"
}

# API key mapping
API_KEY_MAPPING = {
    "1": "API_KEY1",  # OpenRouter
    "2": "API_KEY2",  # DeepSeek
    "3": "API_KEY3",  # Google
    "4": "API_KEY4"   # Hugging Face
}

# Available models for each provider (only free models that are actually available)
PROVIDER_MODELS = {
    "1": [  # OpenRouter (free models)
        "mistralai/mistral-7b-instruct:free",
        "mistralai/mistral-small:free",
        "google/gemini-1.5-flash-latest:free",
        "meta-llama/llama-3-8b-instruct:free",
        "meta-llama/llama-3.3-70b-instruct:free",
        "deepseek/deepseek-r1:free",
        "qwen/qwq-32b:free"
    ],
    "2": [  # DeepSeek (free tier)
        "deepseek-chat"
    ],
    "3": [  # Google (free tier)
        "gemini-1.0-pro",
        "gemini-1.5-flash"
    ],
    "4": [  # Hugging Face (your custom models)
        "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO",
        "mistralai/Mistral-7B-Instruct-v0.2",
        "meta-llama/Llama-2-7b-chat-hf",
        "openchat/openchat-3.5-0106",
        "HuggingFaceH4/zephyr-7b-beta"
    ]
}

def load_config(path="config.txt"):
    """Load configuration from a text file with support for multi-line values."""
    config = {}
    current_key = None
    multi_line_value = ""

    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                # New key-value pair
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""

                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():  # Continuation of previous value
                multi_line_value += " " + line.strip()

        # Add the last key-value pair
        if current_key and multi_line_value:
            config[current_key] = multi_line_value.strip()

    return config

def create_bot_config(original_config, provider_choice, model):
    """Create a separate bot config file with the selected provider's API key and model."""
    # Create a new config dictionary
    new_config = original_config.copy()

    # Check for API keys based on provider choice
    api_key_name = API_KEY_MAPPING[provider_choice]
    if api_key_name not in original_config:
        print(f"Error: {api_key_name} not found in config.txt")
        return None

    # Set the API_KEY to the selected provider's API key
    new_config["API_KEY"] = original_config[api_key_name]

    # Add the selected model to the config
    new_config["MODEL"] = model

    # Add the provider type
    if provider_choice == "1":
        new_config["API_PROVIDER"] = "openrouter"
    elif provider_choice == "2":
        new_config["API_PROVIDER"] = "deepseek"
    elif provider_choice == "3":
        new_config["API_PROVIDER"] = "google"
    elif provider_choice == "4":
        new_config["API_PROVIDER"] = "huggingface"

    # Write to a completely separate bot config file
    bot_config_path = "bot_config.txt"
    with open(bot_config_path, "w") as f:
        for key, value in new_config.items():
            # Only write necessary keys
            if key not in ["API_KEY1", "API_KEY2", "API_KEY3", "API_KEY4"] or key == "API_KEY":
                f.write(f"{key}={value}\n")

    return bot_config_path

def select_provider():
    """Ask the user to select an API provider."""
    print("\n=== Select API Provider ===")
    print("1. OpenRouter (Free models from various providers)")
    print("2. DeepSeek (DeepSeek models)")
    print("3. Google (Gemini models)")
    print("4. Hugging Face (Your custom models)")

    while True:
        choice = input("\nEnter your choice (1-4): ").strip()
        if choice in ["1", "2", "3", "4"]:
            return choice
        print("Invalid choice. Please enter a number between 1 and 4.")

def select_model(provider_choice):
    """Ask the user to select a model for the chosen provider."""
    models = PROVIDER_MODELS[provider_choice]
    provider_name = PROVIDER_NAMES[provider_choice]

    print(f"\n=== Select {provider_name} Model ===")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")

    while True:
        try:
            choice = int(input(f"\nEnter your choice (1-{len(models)}): ").strip())
            if 1 <= choice <= len(models):
                return models[choice-1]
            print(f"Invalid choice. Please enter a number between 1 and {len(models)}.")
        except ValueError:
            print("Please enter a valid number.")

# No cleanup needed since we're using a separate bot_config.txt file

def main():
    # Check if config.txt exists
    if not os.path.exists("config.txt"):
        print("Error: config.txt not found. Please create a config.txt file with your API keys.")
        return

    # Load the original config
    config = load_config("config.txt")

    # Check if all required API keys are present
    required_keys = ["API_ID", "API_HASH", "GROUP_IDS"]
    missing_keys = [key for key in required_keys if key not in config]
    if missing_keys:
        print(f"Error: Missing required keys in config.txt: {', '.join(missing_keys)}")
        return

    # Select provider
    provider_choice = select_provider()
    provider_name = PROVIDER_NAMES[provider_choice]

    # Select model
    model = select_model(provider_choice)

    # Create separate bot config file
    bot_config_path = create_bot_config(config, provider_choice, model)
    if not bot_config_path:
        return

    # This launcher is for or3.py only
    script_path = "or3.py"

    # Print summary
    print("\n=== Bot Configuration ===")
    print(f"Provider: {provider_name}")
    print(f"Model: {model}")
    print(f"Script: {script_path}")
    print(f"Config: {bot_config_path}")
    print("\nStarting bot...")

    # Run the bot with the bot_config.txt
    try:
        # Run the bot script (it will use bot_config.txt automatically)
        print(f"Running: {sys.executable} {script_path}")
        subprocess.run([sys.executable, script_path])
    except Exception as e:
        print(f"Error running bot: {e}")

if __name__ == "__main__":
    main()
