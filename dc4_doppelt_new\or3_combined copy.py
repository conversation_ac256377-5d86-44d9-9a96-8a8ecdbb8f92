"""
SIMPLIFIED TELEGRAM BOT
======================

This script implements a Telegram bot that:
1. Responds to messages in configured groups
2. Maintains conversation context
3. Supports character profiles for different personalities
4. Uses multiple API providers with fallback and retry logic
5. Preserves the original message across provider attempts

Features:
- Direct message processing
- Character switching based on triggers
- Conversation memory for context
- Fallback to next API provider on failure
- Retry logic for transient errors
- Minimal typing indicators

Dependencies:
- telethon
- httpx
- json
- asyncio
- time
- re

Usage:
1. Configure in config.txt with API keys, group IDs, and provider settings
2. Run the script
3. <PERSON><PERSON> responds in specified groups

Version: 2.2
"""

from telethon import TelegramClient, events
import httpx
import json
import asyncio
import time
import re
import os
from typing import Dict, Any, Optional, List
try:
    import openai
except ImportError:
    print("[WARNING] OpenAI package not installed. Some features may not work properly.")

# ===== GLOBAL VARIABLES AND SETTINGS =====

conversation_memory = []
max_memory_items = 10
current_character = "default"
character_profiles = {}
runtime_system_prompt = None

print("[INFO] Starting simplified Telegram bot...")

# ===== API PROVIDER CLASSES =====

class APIProvider:
    """Base class for API providers"""
    def __init__(self, api_key: str, model: str):
        self.api_key = api_key
        self.model = model

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        raise NotImplementedError("Each API provider must implement this method")

    @staticmethod
    def get_providers(config: Dict[str, str]) -> List['APIProvider']:
        """Return a list of initialized API providers"""
        provider_order = config.get('PROVIDER_ORDER', 'openrouter').lower().split(',')
        providers = []

        for provider_name in provider_order:
            provider_name = provider_name.strip()
            api_key = None
            model = config.get('MODEL', '')

            if provider_name == 'openrouter':
                api_key = config.get('OPENROUTER_API_KEY', '')
                model = config.get('OPENROUTER_MODEL', '')
            elif provider_name == 'cerebras':
                api_key = config.get('CEREBRAS_API_KEY', '')
                model = config.get('CEREBRAS_MODEL', '')
            elif provider_name == 'cohere':
                api_key = config.get('COHERE_API_KEY', '')
                model = config.get('COHERE_MODEL', '')
            elif provider_name == 'mistral':
                api_key = config.get('MISTRAL_API_KEY', '')
                model = config.get('MISTRAL_MODEL', '')
            elif provider_name == 'lamini':
                api_key = config.get('LAMINI_API_KEY', '')
                model = config.get('LAMINI_MODEL', '')
            else:
                print(f"[WARNING] Unknown API provider: {provider_name}")
                continue

            if not api_key:
                print(f"[WARNING] API key missing for provider: {provider_name}")
                continue

            try:
                provider_instance = _get_provider_instance(provider_name, api_key, model)
                providers.append(provider_instance)
                print(f"[INFO] Initialized provider: {provider_name}")
            except Exception as e:
                print(f"[ERROR] Failed to initialize provider {provider_name}: {e}")

        if not providers:
            raise ValueError("No API providers could be initialized.")
        return providers

def _get_provider_instance(provider_name: str, api_key: str, model: str) -> 'APIProvider':
    if provider_name == 'openrouter':
        return OpenRouterProvider(api_key, model)
    elif provider_name == 'cerebras':
        return CerebrasProvider(api_key, model)
    elif provider_name == 'cohere':
        return CohereProvider(api_key, model)
    elif provider_name == 'mistral':
        return MistralProvider(api_key, model)
    elif provider_name == 'lamini':
        return LaminiProvider(api_key, model)
    raise ValueError(f"Unknown API provider: {provider_name}")

class OpenRouterProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        try:
            with httpx.Client(timeout=30) as client:
                response = client.post(
                    "https://openrouter.ai/api/v1/chat/completions",
                    headers=headers,
                    json=data
                )
                response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"__ERROR__: OpenRouter API error: {str(e)}"

class CerebrasProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        try:
            with httpx.Client(timeout=30) as client:
                response = client.post(
                    "https://api.cerebras.ai/v1/chat/completions",
                    headers=headers,
                    json=data
                )
                response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"__ERROR__: Cerebras API error: {str(e)}"

class CohereProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        # Cohere uses a different format for system prompts
        combined_prompt = f"{system_prompt}\n\n{prompt}" if system_prompt.strip() else prompt

        data = {
            "model": self.model,
            "prompt": combined_prompt,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        try:
            with httpx.Client(timeout=30) as client:
                response = client.post(
                    "https://api.cohere.ai/v1/generate",
                    headers=headers,
                    json=data
                )
                response.raise_for_status()
            result = response.json()
            return result["generations"][0]["text"]
        except Exception as e:
            return f"__ERROR__: Cohere API error: {str(e)}"

class MistralProvider(APIProvider):
    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        try:
            with httpx.Client(timeout=30) as client:
                response = client.post(
                    "https://api.mistral.ai/v1/chat/completions",
                    headers=headers,
                    json=data
                )
                response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"]
        except Exception as e:
            return f"__ERROR__: Mistral API error: {str(e)}"

class LaminiProvider(APIProvider):
    def __init__(self, api_key: str, model: str, api_url: str = "https://api.lamini.ai/inf"):
        self.api_key = api_key
        self.model = model
        self.api_url = api_url

    def generate_response(self, prompt: str, system_prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        messages = []
        if system_prompt.strip():
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        # Try using OpenAI client first (preferred method)
        try:
            import openai
            client = openai.OpenAI(
                api_key=self.api_key,
                base_url=self.api_url,
            )

            try:
                response = client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
                return response.choices[0].message.content
            except Exception as e:
                return f"__ERROR__: Lamini API error: {str(e)}"

        # Fall back to httpx if OpenAI client is not available
        except ImportError:
            print("[INFO] OpenAI package not installed. Using httpx fallback for Lamini API.")
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "OpenAI-Python/1.0",
                "Accept": "application/json"
            }

            data = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            endpoint = f"{self.api_url}/v1/chat/completions"

            try:
                with httpx.Client(timeout=30, follow_redirects=True) as client:
                    response = client.post(endpoint, headers=headers, json=data)
                    response.raise_for_status()
                result = response.json()
                return result["choices"][0]["message"]["content"]
            except Exception as e:
                return f"__ERROR__: Lamini API error: {str(e)}"

# ===== CONFIGURATION FUNCTIONS =====

def load_config(path="config.txt"):
    config = {}
    current_key = None
    multi_line_value = ""
    with open(path, "r") as f:
        for line in f:
            line = line.rstrip()
            if '=' in line and (current_key is None or not line.startswith(" ")):
                if current_key and multi_line_value:
                    config[current_key] = multi_line_value.strip()
                    multi_line_value = ""
                key, value = line.split("=", 1)
                current_key = key.strip()
                multi_line_value = value.strip()
            elif current_key and line.strip():
                multi_line_value += " " + line.strip()
    if current_key and multi_line_value:
        config[current_key] = multi_line_value.strip()
    return config

# Load configuration
config = load_config()
print("[INFO] Using config.txt for configuration")

api_id = int(config["API_ID"])
api_hash = config["API_HASH"]
group_ids = [int(x.strip()) for x in config["GROUP_IDS"].split(",")]
system_prompt = config.get("SYSTEM_PROMPT", "").strip()
runtime_system_prompt = system_prompt

print(f"[INFO] System prompt length: {len(system_prompt)} characters")

# ===== CHARACTER PROFILE MANAGEMENT =====

def load_character_profiles(profiles_file="characters.json"):
    global character_profiles
    try:
        if os.path.exists(profiles_file):
            with open(profiles_file, "r") as f:
                character_profiles = json.load(f)
            print(f"[INFO] Loaded {len(character_profiles)} character profiles from {profiles_file}")
        else:
            character_profiles = {}
            with open(profiles_file, "w") as f:
                json.dump(character_profiles, f, indent=4)
            print(f"[INFO] Created new empty character profiles file")
    except Exception as e:
        print(f"[ERROR] Failed to load character profiles: {e}")
        character_profiles = {}
    for character_id, profile in character_profiles.items():
        print(f"[INFO] Loaded profile: {profile['name']} ({character_id}) - {len(profile['triggers'])} triggers")

def switch_character(character_id):
    global current_character, runtime_system_prompt, system_prompt
    if character_id in character_profiles:
        current_character = character_id
        if system_prompt:
            runtime_system_prompt = f"{system_prompt}\n\nCHARACTER PROFILE:\n{character_profiles[character_id]['prompt']}"
        else:
            runtime_system_prompt = character_profiles[character_id]["prompt"]
        print(f"[INFO] Switched to character profile: {character_profiles[character_id]['name']} ({character_id})")
        return True
    print(f"[WARNING] Character profile not found: {character_id}")
    return False

def detect_character_trigger(message):
    message_lower = message.lower()
    if "be a " in message_lower or "act like a " in message_lower or "talk like a " in message_lower:
        for prefix in ["be a ", "act like a ", "talk like a ", "be an ", "act like an "]:
            if prefix in message_lower:
                match = re.search(f"{prefix}([^.!?]+)[.!?]*", message_lower)
                if match:
                    full_description = match.group(1).strip()
                    words = full_description.split()
                    core_type = ' '.join(words[:min(2, len(words))])
                    print(f"[DEBUG] Detected character request: '{full_description}' (core type: '{core_type}')")
                    for char_id, profile in character_profiles.items():
                        if core_type in profile["name"].lower() or core_type in profile["description"].lower():
                            return char_id
                    print(f"[INFO] No existing character matches '{core_type}'")
                    return None
    for char_id, profile in character_profiles.items():
        for trigger in profile["triggers"]:
            if re.search(r'\b' + re.escape(trigger) + r'\b', message_lower):
                return char_id
    return None

def create_character(description, created_by="user", api_providers=None):
    global character_profiles
    api_providers = api_providers or globals().get('api_providers', [])
    if not api_providers:
        print(f"[ERROR] No API providers available for character creation")
        return None
    try:
        words = description.lower().split()
        character_type = ' '.join(words[:min(3, len(words))])
        character_id = re.sub(r'[^a-z0-9]', '_', character_type)
        if character_id in character_profiles:
            character_id = f"{character_id}_{int(time.time())}"
        print(f"[INFO] Creating new character: {description} (ID: {character_id})")
        character_prompt = f"""
Create a CONCISE character profile for '{description}' using bullet points and minimal text.

Include:
1. NAME: A fitting name (short and memorable).
2. BACKSTORY: 1-2 sentences maximum about origin and key events.
3. PERSONALITY: 3-5 key traits or quirks (bullet points).
4. APPEARANCE: 2-3 distinctive features (bullet points).
5. BEHAVIORS: 2-3 specific habits (bullet points).
6. CATCHPHRASES: 1-3 short, distinctive phrases.
7. VOICE: 2-3 speech characteristics (bullet points).

IMPORTANT: Be extremely concise. Use bullet points. Avoid lengthy descriptions and narratives.
Focus on distinctive traits that define the character's speech and behavior.
"""
        detailed_profile = None
        for provider in api_providers:
            print(f"[INFO] Trying provider for character creation: {provider.__class__.__name__}")
            try:
                detailed_profile = provider.generate_response(
                    prompt=character_prompt,
                    system_prompt="You are a character profile creator specializing in detailed, immersive profiles.",
                    temperature=0.8,
                    max_tokens=2000
                )
                if not detailed_profile.startswith("__ERROR__"):
                    print(f"[INFO] Successfully generated character profile with {provider.__class__.__name__}")
                    break
                else:
                    print(f"[WARNING] Provider {provider.__class__.__name__} failed: {detailed_profile}")
            except Exception as e:
                print(f"[ERROR] Provider {provider.__class__.__name__} failed: {e}")
        if not detailed_profile or detailed_profile.startswith("__ERROR__"):
            print(f"[ERROR] All providers failed to create character profile")
            return None
        name_match = re.search(r'NAME:?\s*([^\n]+)', detailed_profile)
        character_name = name_match.group(1).strip() if name_match else description.split()[0].title()
        prompt = f"""You are {description}.

{detailed_profile}

ROLEPLAY GUIDELINES:
1. Act as {description} with described traits and speech patterns
2. Use character's catchphrases and speech style
3. Stay in character while being helpful
4. If asked about being AI, acknowledge briefly but remain in character"""
        triggers = []
        for word in character_name.lower().split():
            if len(word) > 2 and word not in triggers:
                triggers.append(word)
        for word in description.lower().split():
            if len(word) > 3 and word not in ["the", "and", "with", "that", "this", "from", "like", "have"] and word not in triggers:
                triggers.append(word)
        if character_id not in triggers:
            triggers.append(character_id)
        if len(triggers) < 2 and description.split():
            for word in description.split()[:2]:
                clean_word = re.sub(r'[^a-z0-9]', '', word.lower())
                if clean_word and clean_word not in triggers:
                    triggers.append(clean_word)
        character_profiles[character_id] = {
            "name": character_name,
            "description": description,
            "prompt": prompt,
            "triggers": triggers,
            "created_by": created_by
        }
        with open("characters.json", "w") as f:
            json.dump(character_profiles, f, indent=4)
        print(f"[INFO] Successfully created character: {character_name} (ID: {character_id})")
        print(f"[INFO] Triggers: {', '.join(triggers)}")
        return character_id
    except Exception as e:
        print(f"[ERROR] Failed to create character: {e}")
        return None

# ===== SYSTEM PROMPT MANAGEMENT =====

def update_system_prompt(new_content):
    global runtime_system_prompt
    runtime_system_prompt = new_content
    print(f"[INFO] Updated runtime system prompt: {len(runtime_system_prompt)} characters")

def extract_topics(message):
    topics = []
    if len(message) < 10:
        return topics
    words = message.split()
    for word in words:
        if (len(word) > 4 and word[0].isupper() and
            word.lower() not in ['this', 'that', 'these', 'those', 'there', 'their', 'about']):
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word and len(clean_word) > 4:
                topics.append(clean_word)
    return topics

# ===== INITIALIZATION =====

# Initialize API providers
try:
    api_providers = APIProvider.get_providers(config)
    provider_names = [p.__class__.__name__ for p in api_providers]
    print(f"[INFO] Initialized {len(api_providers)} providers: {', '.join(provider_names)}")
except Exception as e:
    print(f"[ERROR] Failed to initialize API providers: {e}")
    exit(1)

# Initialize Telegram client
print(f"[INFO] Listening to groups: {group_ids}")
client = TelegramClient('user_session', api_id, api_hash)

# ===== MESSAGE HANDLER =====

@client.on(events.NewMessage(chats=group_ids))
async def on_new_message(event):
    """Handle new messages in monitored groups"""
    try:
        # Skip non-text messages, messages from self, and empty messages
        if event.message.media or event.message.action:
            return

        me = await client.get_me()
        if event.sender_id == me.id:
            return

        user_msg = event.raw_text.strip() if event.raw_text else ""
        if not user_msg:
            return

        print(f"[INFO] Message from {event.sender.username or event.sender_id}: {user_msg[:50]}...")

        # Check for character switching
        new_character = detect_character_trigger(user_msg)
        if new_character and new_character != current_character:
            switch_character(new_character)

        # Check for character creation requests
        character_request_patterns = [
            r"be a ([^.!?]+)[.!?]*",
            r"be an ([^.!?]+)[.!?]*",
            r"act like a ([^.!?]+)[.!?]*",
            r"act like an ([^.!?]+)[.!?]*",
            r"pretend to be a ([^.!?]+)[.!?]*",
            r"pretend to be an ([^.!?]+)[.!?]*",
            r"roleplay as a ([^.!?]+)[.!?]*",
            r"roleplay as an ([^.!?]+)[.!?]*"
        ]

        for pattern in character_request_patterns:
            match = re.search(pattern, user_msg.lower())
            if match:
                description = match.group(1).strip()
                print(f"[INFO] Creating character: {description}")
                async with client.action(event.chat_id, 'typing'):
                    new_character_id = create_character(description, api_providers=api_providers)
                    if new_character_id:
                        switch_character(new_character_id)
                return

        # Add message to conversation memory
        conversation_memory.append({"role": "user", "content": user_msg})
        if len(conversation_memory) > max_memory_items:
            conversation_memory.pop(0)

        # Limit conversation context for character profiles to prevent token limit issues
        limited_memory = conversation_memory
        if runtime_system_prompt and len(conversation_memory) > 5:
            limited_memory = conversation_memory[-5:]

        # Prepare context and system prompt
        context = "\n".join([f"{item['role']}: {item['content']}" for item in limited_memory])
        system_prompt_text = f"{runtime_system_prompt}\n\nRecent conversation context: {context}" if runtime_system_prompt else f"Recent conversation context: {context}"

        # Generate response using available providers
        async with client.action(event.chat_id, 'typing'):
            await client.send_read_acknowledge(event.chat_id, event.message)
            response = None

            for provider in api_providers:
                provider_name = provider.__class__.__name__
                print(f"[INFO] Trying provider: {provider_name}")
                try:
                    response = provider.generate_response(
                        prompt=user_msg,
                        system_prompt=system_prompt_text,
                        temperature=0.7,
                        max_tokens=1000
                    )
                    if not response.startswith("__ERROR__"):
                        print(f"[INFO] Success with {provider_name}")
                        break
                    else:
                        print(f"[WARNING] {provider_name} failed: {response[:100]}...")
                except Exception as e:
                    print(f"[ERROR] {provider_name} error: {str(e)[:100]}...")

            # Send response if successful
            if response and not response.startswith("__ERROR__"):
                conversation_memory.append({"role": "assistant", "content": response})
                if len(conversation_memory) > max_memory_items:
                    conversation_memory.pop(0)
                await event.reply(response)
                print(f"[INFO] Sent: {response[:50]}...")
            else:
                print(f"[ERROR] All providers failed")

    except Exception as e:
        print(f"[ERROR] Message processing error: {e}")

# ===== MAIN EXECUTION =====

async def main():
    """Main execution function"""
    print("[INFO] Starting Telegram bot...")

    # Start client
    await client.start()

    # Verify group connections
    try:
        if group_ids:
            for group_id in group_ids:
                await client.get_entity(group_id)
            print(f"[INFO] Connected to {len(group_ids)} groups")
        else:
            print("[WARNING] No group IDs configured")
    except Exception as e:
        print(f"[ERROR] Group connection error: {e}")

    # Load character profiles
    load_character_profiles()

    # Set initial character or default mode
    global runtime_system_prompt
    if character_profiles:
        character_keys = sorted(list(character_profiles.keys()))
        first_character = character_keys[0]
        switch_character(first_character)
        print(f"[INFO] Starting with character: {character_profiles[first_character]['name']}")
    else:
        print("[INFO] No characters found. Use 'be a [character]' to create one.")
        runtime_system_prompt = ""  # Empty system prompt for default behavior

    print("[INFO] Bot is running. Press Ctrl+C to stop.")

# Run the bot
client.loop.run_until_complete(main())
client.run_until_disconnected()