  ok we have a big brain blocade here.
  ....we dont have to give those instructions to the api since it will not remeber the 
  posts that it made before....so we cant avo
  id the ai to respond in those terms we dont send 
  the ai their own resposses for token resons......so
   we have to overthink the instructions we give in every 
   respond.....we cant give instructions like dont repeat 
   yourself...since it dont know what it said before........is 
   that right?????????????????check this the ai will not remeber 
   what it did say before espeacially not when it changes the provider 
   as well since rate limit...................save all that to a file i
    have to reset u......that our approach from those guideline also
     overwrites the character itself....also that we need to filter 
     those repeatative quotes later after we recieved the response
      we will compare the parts with the old messages from the api 
      ai and well just remove those repeatative parts...i think thats
       the best approach since the ai and the character will not stiop repeating itself............
